"""
DEPRECATED: Workflow management module for the orchestrator

⚠️  WARNING: This module is DEPRECATED as of MCP client workflow integration.
⚠️  Multi-step workflow capabilities have been moved to MCP client delegation.
⚠️  This provides better error handling, retries, and native MCP protocol compliance.

Previously handled workflow detection, execution, and template management
for complex multi-step financial operations.

Use MCP client delegation in orchestrator._delegate_to_mcp_client() instead.
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from services.tool_composition import (
    ToolWorkflow, ToolChain, WorkflowTemplates, WorkflowExecutor,
    WorkflowStep, StepStatus
)

logger = logging.getLogger(__name__)


class WorkflowManager:
    """Manages workflow detection and execution for financial operations"""
    
    def __init__(self, tool_executor=None):
        self.workflow_executor = None
        self.tool_executor = tool_executor
        self._workflow_templates = {}
        self._initialize_workflow_templates()
    
    def _initialize_workflow_templates(self):
        """Initialize pre-built workflow templates"""
        self._workflow_templates = {
            'month_end_close': WorkflowTemplates.month_end_close,
            'customer_onboarding': WorkflowTemplates.customer_onboarding,
            'invoice_processing': WorkflowTemplates.invoice_processing,
            'financial_reporting': WorkflowTemplates.financial_reporting
        }
        logger.info(f"Initialized {len(self._workflow_templates)} workflow templates")
    
    async def detect_workflow_opportunity(self, message: str, intent_info: Dict[str, Any]) -> Optional[str]:
        """
        Detect if the request matches a pre-built workflow template
        
        Returns:
            Workflow template name if matched, None otherwise
        """
        message_lower = message.lower()
        intent = intent_info.get('primary_intent', '')
        
        # Direct workflow mappings
        workflow_mappings = {
            'month_end_close': ['month-end close', 'close the books', 'period close', 'month end'],
            'customer_onboarding': ['new customer', 'onboard customer', 'add customer', 'create customer'],
            'invoice_processing': ['process invoices', 'invoice processing', 'pending invoices'],
            'financial_reporting': ['financial reports', 'generate reports', 'financial statements', 'reporting package']
        }
        
        # Check for workflow keywords
        for workflow_name, keywords in workflow_mappings.items():
            if any(keyword in message_lower for keyword in keywords):
                logger.info(f"Detected workflow opportunity: {workflow_name}")
                return workflow_name
        
        # Check by intent
        intent_to_workflow = {
            'month_end_close': 'month_end_close',
            'comprehensive_analysis': 'financial_reporting'
        }
        
        if intent in intent_to_workflow:
            return intent_to_workflow[intent]
        
        return None
    
    async def execute_workflow(self, workflow_name: str, context: Optional[Dict[str, Any]] = None, 
                             response_processor=None) -> Dict[str, Any]:
        """
        Execute a pre-built workflow
        
        Args:
            workflow_name: Name of the workflow template
            context: Context parameters for the workflow
            response_processor: Optional response processor for formatting
            
        Returns:
            Workflow execution results
        """
        if workflow_name not in self._workflow_templates:
            raise ValueError(f"Unknown workflow: {workflow_name}")
        
        # Get workflow template
        template_func = self._workflow_templates[workflow_name]
        
        # Create workflow instance with context
        if workflow_name == 'customer_onboarding':
            # Customer onboarding requires customer name
            customer_name = context.get('customer_name', 'New Customer')
            workflow = template_func(customer_name)
        elif workflow_name == 'financial_reporting':
            # Financial reporting requires period
            period = context.get('period', datetime.now().strftime('%Y-%m'))
            workflow = template_func(period)
        else:
            workflow = template_func()
        
        # Initialize workflow executor if needed
        if not self.workflow_executor and self.tool_executor:
            self.workflow_executor = WorkflowExecutor(tool_executor=self.tool_executor)
        
        # Execute workflow
        logger.info(f"Executing workflow: {workflow_name}")
        result = await self.workflow_executor.execute(workflow, context)
        
        # Format response if processor available
        if response_processor:
            formatted_response = await response_processor.format_workflow_response(
                workflow_name=workflow_name,
                workflow_result=result,
                context=context
            )
            result['formatted_response'] = formatted_response
        
        return result
    
    async def create_custom_workflow(self, steps: List[Dict[str, Any]], name: str = "custom_workflow") -> ToolWorkflow:
        """
        Create a custom workflow from step definitions
        
        Args:
            steps: List of step definitions
            name: Workflow name
            
        Returns:
            Created workflow
        """
        workflow = ToolWorkflow(name)
        
        for i, step_def in enumerate(steps):
            step = WorkflowStep(
                id=step_def.get('id', f'step_{i}'),
                tool_name=step_def['tool_name'],
                parameters=step_def.get('parameters', {}),
                depends_on=step_def.get('depends_on', []),
                description=step_def.get('description', '')
            )
            workflow.add_step(step)
        
        return workflow
    
    async def create_tool_chain(self, tools: List[str], name: str = "tool_chain") -> ToolChain:
        """
        Create a simple sequential tool chain
        
        Args:
            tools: List of tool names to execute in sequence
            name: Chain name
            
        Returns:
            Tool chain
        """
        chain = ToolChain(name)
        
        for tool_name in tools:
            chain.add(tool_name)
        
        return chain
    
    def extract_workflow_context(self, message: str, workflow_name: str) -> Dict[str, Any]:
        """
        Extract context parameters from message for a specific workflow
        
        Args:
            message: User message
            workflow_name: Name of the workflow
            
        Returns:
            Extracted context parameters
        """
        import re
        context = {}
        
        # Extract dates
        date_pattern = r'\b(\d{4}-\d{2}-\d{2})\b'
        dates = re.findall(date_pattern, message)
        if dates:
            if len(dates) >= 2:
                context['start_date'] = dates[0]
                context['end_date'] = dates[1]
            elif len(dates) == 1:
                context['end_date'] = dates[0]
        
        # Extract period (YYYY-MM)
        period_pattern = r'\b(\d{4}-\d{2})\b'
        periods = re.findall(period_pattern, message)
        if periods:
            context['period'] = periods[0]
        
        # Workflow-specific extraction
        if workflow_name == 'customer_onboarding':
            # Extract customer name
            customer_match = re.search(
                r'(?:customer|client|company)(?:\s+named?)?\s+([A-Za-z0-9\s&\-\.]+)', 
                message, 
                re.I
            )
            if customer_match:
                context['customer_name'] = customer_match.group(1).strip()
        
        return context