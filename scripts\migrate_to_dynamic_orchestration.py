#!/usr/bin/env python
"""
Migration Script: Multi-Agent to Dynamic Orchestration

This script helps migrate from the old multi-agent configuration to the new
dynamic orchestration architecture.

Usage:
    python scripts/migrate_to_dynamic_orchestration.py [--dry-run] [--backup]
"""

import os
import sys
import yaml
import json
import shutil
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def backup_file(file_path: Path, backup_dir: Path):
    """Create a backup of a file"""
    if file_path.exists():
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
        backup_path = backup_dir / backup_name
        shutil.copy2(file_path, backup_path)
        print(f"✓ Backed up {file_path.name} to {backup_path}")
        return backup_path
    return None


def migrate_fastagent_config(config_path: Path, dry_run: bool = False):
    """Migrate mcp.config.yaml to dynamic orchestration"""
    print("\n=== Migrating mcp.config.yaml ===")
    
    if not config_path.exists():
        print("✗ mcp.config.yaml not found")
        return False
    
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Extract old agent configurations
    old_agents = config.get('agents', {})
    removed_agents = []
    
    # Keep only orchestrator agent
    new_agents = {}
    for agent_id, agent_config in old_agents.items():
        if agent_id == 'orchestrator':
            # Update orchestrator configuration
            agent_config['name'] = 'Dynamic Orchestrator'
            agent_config['description'] = 'Intelligently routes requests to MCP tools based on intent'
            
            # Expand capabilities to include all migrated agents
            if 'capabilities' not in agent_config:
                agent_config['capabilities'] = []
            
            agent_config['capabilities'].extend([
                "Intent detection and routing",
                "Dynamic tool discovery",
                "Multi-tool workflows",
                "Confidence-based clarification",
                "GL operations (via MCP tools)",
                "AR operations (via MCP tools)",
                "AP operations (via MCP tools)",
                "Financial analysis (via MCP tools)",
                "Report generation (via MCP tools)",
                "Data validation (via MCP tools)"
            ])
            
            # Remove duplicates
            agent_config['capabilities'] = list(set(agent_config['capabilities']))
            
            new_agents[agent_id] = agent_config
        else:
            removed_agents.append(agent_id)
    
    # Update config
    config['agents'] = new_agents
    
    # Add metadata to MCP servers if not present
    if 'mcp' in config and 'servers' in config['mcp']:
        for server_name, server_config in config['mcp']['servers'].items():
            if 'tags' not in server_config:
                # Add appropriate tags based on server name
                if 'intacct' in server_name.lower():
                    server_config['tags'] = ['intacct', 'financial', 'erp']
                elif 'sbca' in server_name.lower() or 'sage business cloud' in server_name.lower():
                    server_config['tags'] = ['sbca', 'accounting', 'cloud']
                else:
                    server_config['tags'] = ['custom']
            
            if 'metadata' not in server_config:
                server_config['metadata'] = {}
    
    # Add migration notice
    migration_notice = """# Fast-Agent Configuration File
# Dynamic Orchestration Configuration
# 
# This configuration has been migrated to the dynamic orchestration architecture.
# Previous agents have been removed and their functionality is now handled by
# the orchestrator routing to appropriate MCP tools.
#
# Removed agents: {}
# Migration date: {}
""".format(', '.join(removed_agents), datetime.now().isoformat())
    
    if not dry_run:
        # Write updated config
        with open(config_path, 'w') as f:
            f.write(migration_notice + "\n")
            yaml.dump(config, f, default_flow_style=False, sort_keys=False)
        print(f"✓ Updated mcp.config.yaml")
        print(f"  - Removed agents: {', '.join(removed_agents)}")
        print(f"  - Enhanced orchestrator capabilities")
    else:
        print(f"[DRY RUN] Would remove agents: {', '.join(removed_agents)}")
        print(f"[DRY RUN] Would update orchestrator capabilities")
    
    return True


def update_environment_variables(env_path: Path, dry_run: bool = False):
    """Update environment variables for dynamic orchestration"""
    print("\n=== Updating Environment Variables ===")
    
    new_vars = {
        'ORCHESTRATOR_CONFIDENCE_THRESHOLD': '0.8',
        'ORCHESTRATOR_MAX_RETRIES': '3',
        'ORCHESTRATOR_TIMEOUT_SECONDS': '90',
        'MCP_CONFIG_DIR': '.config',
        'MCP_DISCOVERY_INTERVAL': '300',
        'MCP_HEALTH_CHECK_INTERVAL': '30',
        'MAX_CONCURRENT_TOOLS': '5',
        'TOOL_CACHE_TTL_SECONDS': '3600'
    }
    
    if env_path.exists():
        # Read existing env file
        with open(env_path, 'r') as f:
            content = f.read()
        
        # Add new variables if not present
        lines = content.splitlines()
        for var, value in new_vars.items():
            if not any(line.startswith(f"{var}=") for line in lines):
                lines.append(f"{var}={value}")
                print(f"✓ Added {var}={value}")
        
        if not dry_run:
            with open(env_path, 'w') as f:
                f.write('\n'.join(lines) + '\n')
    else:
        # Create new env file from example
        example_path = env_path.parent / 'api.env.example'
        if example_path.exists():
            if not dry_run:
                shutil.copy2(example_path, env_path)
                print(f"✓ Created {env_path.name} from example")
        else:
            print(f"✗ No environment file found and no example available")
            return False
    
    return True


def create_migration_report(project_root: Path, removed_agents: List[str]):
    """Create a migration report"""
    report = {
        'migration_date': datetime.now().isoformat(),
        'migration_type': 'multi-agent to dynamic orchestration',
        'removed_agents': removed_agents,
        'new_architecture': {
            'orchestrator': 'Dynamic Orchestration Agent',
            'routing': 'Intent-based with confidence scoring',
            'tool_discovery': 'Dynamic MCP tool discovery',
            'configuration': 'Runtime MCP server management'
        },
        'api_changes': {
            'removed_endpoints': [
                f'/agents/{agent}/chat' for agent in removed_agents
            ],
            'new_endpoints': [
                '/mcp/servers (CRUD operations)',
                '/mcp/tools (tool discovery)',
                '/mcp/servers/{name}/health'
            ]
        },
        'benefits': [
            'No need to select specific agents',
            'Automatic routing based on intent',
            'Dynamic tool discovery',
            'Runtime server configuration',
            'Simplified frontend integration'
        ]
    }
    
    report_path = project_root / '.config' / 'migration_report.json'
    report_path.parent.mkdir(exist_ok=True)
    
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n✓ Created migration report at {report_path}")
    return report_path


def main():
    parser = argparse.ArgumentParser(
        description='Migrate from multi-agent to dynamic orchestration architecture'
    )
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be changed without making modifications'
    )
    parser.add_argument(
        '--backup',
        action='store_true',
        help='Create backups of all modified files'
    )
    parser.add_argument(
        '--project-root',
        type=Path,
        default=Path.cwd(),
        help='Project root directory (default: current directory)'
    )
    
    args = parser.parse_args()
    
    print("=== AI Workspace Migration Tool ===")
    print(f"Migrating to Dynamic Orchestration Architecture")
    print(f"Project root: {args.project_root}")
    
    if args.dry_run:
        print("\n** DRY RUN MODE - No changes will be made **")
    
    # Create backup directory if requested
    backup_dir = None
    if args.backup and not args.dry_run:
        backup_dir = args.project_root / '.backups' / f"migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        backup_dir.mkdir(parents=True, exist_ok=True)
        print(f"\nBackup directory: {backup_dir}")
    
    # Files to migrate
    fastagent_config = args.project_root / 'mcp.config.yaml'
    env_file = args.project_root / '.env'
    if not env_file.exists():
        env_file = args.project_root / 'api.env'
    
    # Backup files if requested
    if backup_dir:
        backup_file(fastagent_config, backup_dir)
        if env_file.exists():
            backup_file(env_file, backup_dir)
    
    # Perform migration
    success = True
    
    # Migrate mcp.config.yaml
    if not migrate_fastagent_config(fastagent_config, args.dry_run):
        success = False
    
    # Update environment variables
    if not update_environment_variables(env_file, args.dry_run):
        success = False
    
    # Create migration report
    if success and not args.dry_run:
        # Get list of removed agents (would need to parse config again)
        with open(fastagent_config, 'r') as f:
            lines = f.readlines()
            for line in lines:
                if line.startswith('# Removed agents:'):
                    removed_agents = [a.strip() for a in line.split(':', 1)[1].split(',')]
                    break
            else:
                removed_agents = []
        
        create_migration_report(args.project_root, removed_agents)
    
    # Summary
    print("\n=== Migration Summary ===")
    if success:
        print("✓ Migration completed successfully!")
        if args.dry_run:
            print("\nRun without --dry-run to apply changes")
        else:
            print("\nNext steps:")
            print("1. Review the updated configuration files")
            print("2. Update your API keys in the environment file")
            print("3. Restart the service")
            print("4. Use the orchestrator endpoint for all requests")
            print("\nAPI endpoint changes:")
            print("  OLD: POST /agents/{agent_id}/chat")
            print("  NEW: POST /agents/orchestrator/chat")
    else:
        print("✗ Migration encountered errors")
        print("Please check the output above for details")
    
    return 0 if success else 1


if __name__ == '__main__':
    sys.exit(main())