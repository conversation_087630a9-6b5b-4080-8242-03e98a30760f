{"level":"ERROR","timestamp":"2025-05-30T23:49:36.631486","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to parse next step: Invalid Anthropic API key\n\nThe configured Anthropic API key was rejected.\nPlease check that your API key is valid and not expired."}
{"level":"ERROR","timestamp":"2025-05-30T23:49:36.631542","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to generate next step, ending iteration early"}
{"level":"ERROR","timestamp":"2025-05-30T23:49:40.079515","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: athrow(): asynchronous generator is already running","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
{"level":"ERROR","timestamp":"2025-05-31T00:40:53.724378","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to parse next step: Invalid Anthropic API key\n\nThe configured Anthropic API key was rejected.\nPlease check that your API key is valid and not expired."}
{"level":"ERROR","timestamp":"2025-05-31T00:40:53.724436","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to generate next step, ending iteration early"}
{"level":"ERROR","timestamp":"2025-05-31T00:40:57.117786","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: athrow(): asynchronous generator is already running","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
{"level":"ERROR","timestamp":"2025-05-31T00:47:49.318557","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to parse next step: Invalid Anthropic API key\n\nThe configured Anthropic API key was rejected.\nPlease check that your API key is valid and not expired."}
{"level":"ERROR","timestamp":"2025-05-31T00:47:49.318627","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to generate next step, ending iteration early"}
{"level":"ERROR","timestamp":"2025-05-31T00:47:53.018116","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: athrow(): asynchronous generator is already running","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
{"level":"ERROR","timestamp":"2025-05-31T01:36:20.623247","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to parse next step: Invalid Anthropic API key\n\nThe configured Anthropic API key was rejected.\nPlease check that your API key is valid and not expired."}
{"level":"ERROR","timestamp":"2025-05-31T01:36:20.623292","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to generate next step, ending iteration early"}
{"level":"ERROR","timestamp":"2025-05-31T01:36:24.900084","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: athrow(): asynchronous generator is already running","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
{"level":"ERROR","timestamp":"2025-05-31T01:38:04.054198","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to parse next step: Invalid Anthropic API key\n\nThe configured Anthropic API key was rejected.\nPlease check that your API key is valid and not expired."}
{"level":"ERROR","timestamp":"2025-05-31T01:38:04.054251","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to generate next step, ending iteration early"}
{"level":"ERROR","timestamp":"2025-05-31T01:38:09.158221","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: athrow(): asynchronous generator is already running","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
{"level":"ERROR","timestamp":"2025-05-31T01:41:42.291705","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to parse next step: Invalid Anthropic API key\n\nThe configured Anthropic API key was rejected.\nPlease check that your API key is valid and not expired."}
{"level":"ERROR","timestamp":"2025-05-31T01:41:42.291835","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to generate next step, ending iteration early"}
{"level":"ERROR","timestamp":"2025-05-31T01:41:45.657112","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: athrow(): asynchronous generator is already running","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
{"level":"ERROR","timestamp":"2025-05-31T01:43:14.434725","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to parse next step: Invalid Anthropic API key\n\nThe configured Anthropic API key was rejected.\nPlease check that your API key is valid and not expired."}
{"level":"ERROR","timestamp":"2025-05-31T01:43:14.434789","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to generate next step, ending iteration early"}
{"level":"ERROR","timestamp":"2025-05-31T01:43:17.597861","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: athrow(): asynchronous generator is already running","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
{"level":"ERROR","timestamp":"2025-05-31T01:47:13.320627","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to parse next step: Invalid Anthropic API key\n\nThe configured Anthropic API key was rejected.\nPlease check that your API key is valid and not expired."}
{"level":"ERROR","timestamp":"2025-05-31T01:47:13.320684","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to generate next step, ending iteration early"}
{"level":"ERROR","timestamp":"2025-05-31T01:47:16.330414","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: athrow(): asynchronous generator is already running","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
{"level":"ERROR","timestamp":"2025-05-31T01:48:54.467320","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to parse next step: Invalid Anthropic API key\n\nThe configured Anthropic API key was rejected.\nPlease check that your API key is valid and not expired."}
{"level":"ERROR","timestamp":"2025-05-31T01:48:54.467423","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to generate next step, ending iteration early"}
{"level":"ERROR","timestamp":"2025-05-31T01:48:57.549557","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: athrow(): asynchronous generator is already running","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
{"level":"ERROR","timestamp":"2025-05-31T01:57:56.624468","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to parse next step: Invalid Anthropic API key\n\nThe configured Anthropic API key was rejected.\nPlease check that your API key is valid and not expired."}
{"level":"ERROR","timestamp":"2025-05-31T01:57:56.624521","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to generate next step, ending iteration early"}
{"level":"ERROR","timestamp":"2025-05-31T01:57:59.856301","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: athrow(): asynchronous generator is already running","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
{"level":"ERROR","timestamp":"2025-05-31T01:59:56.742236","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to parse next step: Invalid Anthropic API key\n\nThe configured Anthropic API key was rejected.\nPlease check that your API key is valid and not expired."}
{"level":"ERROR","timestamp":"2025-05-31T01:59:56.742352","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to generate next step, ending iteration early"}
{"level":"ERROR","timestamp":"2025-05-31T01:59:59.804477","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: athrow(): asynchronous generator is already running","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
{"level":"ERROR","timestamp":"2025-05-31T02:06:18.508172","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to parse next step: Invalid Anthropic API key\n\nThe configured Anthropic API key was rejected.\nPlease check that your API key is valid and not expired."}
{"level":"ERROR","timestamp":"2025-05-31T02:06:18.508225","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to generate next step, ending iteration early"}
{"level":"ERROR","timestamp":"2025-05-31T02:06:21.567329","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: athrow(): asynchronous generator is already running","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
{"level":"ERROR","timestamp":"2025-05-31T02:20:48.420845","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to parse next step: Invalid Anthropic API key\n\nThe configured Anthropic API key was rejected.\nPlease check that your API key is valid and not expired."}
{"level":"ERROR","timestamp":"2025-05-31T02:20:48.420938","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to generate next step, ending iteration early"}
{"level":"ERROR","timestamp":"2025-05-31T02:20:51.624693","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: athrow(): asynchronous generator is already running","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
{"level":"ERROR","timestamp":"2025-05-31T02:25:16.420236","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to parse next step: Invalid Anthropic API key\n\nThe configured Anthropic API key was rejected.\nPlease check that your API key is valid and not expired."}
{"level":"ERROR","timestamp":"2025-05-31T02:25:16.420340","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to generate next step, ending iteration early"}
{"level":"ERROR","timestamp":"2025-05-31T02:25:19.699075","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: athrow(): asynchronous generator is already running","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
{"level":"ERROR","timestamp":"2025-06-04T16:41:39.623984","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: ","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
{"level":"ERROR","timestamp":"2025-06-04T16:44:12.107274","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: ","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
{"level":"ERROR","timestamp":"2025-06-04T18:56:18.916465","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: ","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
{"level":"ERROR","timestamp":"2025-06-04T21:03:27.641036","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to parse next step: Invalid Anthropic API key\n\nThe configured Anthropic API key was rejected.\nPlease check that your API key is valid and not expired."}
{"level":"ERROR","timestamp":"2025-06-04T21:03:27.641180","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to generate next step, ending iteration early"}
{"level":"ERROR","timestamp":"2025-06-04T21:13:49.405184","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to parse next step: Invalid Anthropic API key\n\nThe configured Anthropic API key was rejected.\nPlease check that your API key is valid and not expired."}
{"level":"ERROR","timestamp":"2025-06-04T21:13:49.405368","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to generate next step, ending iteration early"}
{"level":"ERROR","timestamp":"2025-06-04T21:45:06.706682","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to parse next step: Anthropic API key not configured\n\nThe Anthropic API key is required but not set.\nAdd it to your configuration file under anthropic.api_key or set the ANTHROPIC_API_KEY environment variable."}
{"level":"ERROR","timestamp":"2025-06-04T21:45:06.706796","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to generate next step, ending iteration early"}
{"level":"ERROR","timestamp":"2025-06-04T22:03:55.086386","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Error executing task: Anthropic API key not configured\n\nThe Anthropic API key is required but not set.\nAdd it to your configuration file under anthropic.api_key or set the ANTHROPIC_API_KEY environment variable."}
{"level":"ERROR","timestamp":"2025-06-04T22:03:58.498290","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Error executing task: Anthropic API key not configured\n\nThe Anthropic API key is required but not set.\nAdd it to your configuration file under anthropic.api_key or set the ANTHROPIC_API_KEY environment variable."}
{"level":"ERROR","timestamp":"2025-06-04T22:04:07.822344","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Error executing task: Anthropic API key not configured\n\nThe Anthropic API key is required but not set.\nAdd it to your configuration file under anthropic.api_key or set the ANTHROPIC_API_KEY environment variable."}
{"level":"ERROR","timestamp":"2025-06-04T22:04:24.090581","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Error executing task: Anthropic API key not configured\n\nThe Anthropic API key is required but not set.\nAdd it to your configuration file under anthropic.api_key or set the ANTHROPIC_API_KEY environment variable."}
{"level":"ERROR","timestamp":"2025-06-04T22:04:28.572806","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Error executing task: Anthropic API key not configured\n\nThe Anthropic API key is required but not set.\nAdd it to your configuration file under anthropic.api_key or set the ANTHROPIC_API_KEY environment variable."}
{"level":"ERROR","timestamp":"2025-06-04T22:04:38.730240","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Error executing task: Anthropic API key not configured\n\nThe Anthropic API key is required but not set.\nAdd it to your configuration file under anthropic.api_key or set the ANTHROPIC_API_KEY environment variable."}
{"level":"ERROR","timestamp":"2025-06-04T22:04:57.564816","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Error executing task: Anthropic API key not configured\n\nThe Anthropic API key is required but not set.\nAdd it to your configuration file under anthropic.api_key or set the ANTHROPIC_API_KEY environment variable."}
{"level":"ERROR","timestamp":"2025-06-04T22:05:00.383435","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Error executing task: Anthropic API key not configured\n\nThe Anthropic API key is required but not set.\nAdd it to your configuration file under anthropic.api_key or set the ANTHROPIC_API_KEY environment variable."}
{"level":"ERROR","timestamp":"2025-06-04T22:05:03.862530","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Error executing task: Anthropic API key not configured\n\nThe Anthropic API key is required but not set.\nAdd it to your configuration file under anthropic.api_key or set the ANTHROPIC_API_KEY environment variable."}
{"level":"WARNING","timestamp":"2025-06-05T00:03:58.914797","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to complete in 3 iterations"}
{"level":"WARNING","timestamp":"2025-06-05T00:09:08.925299","namespace":"mcp_agent.agents.base_agent.financial_orchestrator","message":"Failed to complete in 3 iterations"}
{"level":"ERROR","timestamp":"2025-06-05T14:50:44.908360","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: unhandled errors in a TaskGroup (1 sub-exception)","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
{"level":"ERROR","timestamp":"2025-06-05T14:51:01.330923","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"sage-intacct: Lifecycle task encountered an error: unhandled errors in a TaskGroup (1 sub-exception)","data":{"exc_info":true,"data":{"progress_action":"Error","server_name":"sage-intacct"}}}
