# Fix for Token Limit Error (48,212 tokens exceeding 30k limit)

## Problem
The system is trying to send all 165 discovered tools to GPT-4, resulting in 48,212 tokens which exceeds the 30k limit. This happens because the Intelligent Tool Filtering System is not properly initialized.

## Root Cause
1. The Intelligent Tool Filtering System is implemented but the vector database is empty
2. When filtering fails, the system falls back to sending ALL tools to the LLM
3. The initialization script has never been run

## Solution

### Step 1: Install Missing Dependencies
```bash
pip install structlog sentence-transformers numpy sqlite-vec
```

### Step 2: Run the Initialization Script
```bash
python scripts/initialize_intelligent_filtering.py
```

This script will:
- Create the vector database at `data/ai_workspace_vectors.db`
- Discover all 165 tools from the MCP servers
- Generate embeddings for each tool
- Index them in the vector database
- Test the filtering to ensure it works

### Step 3: Verify the Fix
After initialization, the system will:
- Filter 165 tools down to maximum 20 tools per request
- Reduce token usage from 48,212 to ~2,000 tokens (95% reduction)
- Send only the most relevant tools to the LLM

## What Happens During Initialization

1. **Vector Database Setup**: Creates SQLite database with vector extension
2. **Tool Discovery**: Finds all tools from configured MCP servers
3. **Embedding Generation**: Creates 768-dimension embeddings for each tool
4. **Indexing**: Stores tools with embeddings for fast semantic search
5. **Testing**: Verifies filtering works with a test query

## Expected Output
```
==============================================================
INTELLIGENT TOOL FILTERING SYSTEM - INITIALIZATION
==============================================================

1. Loading configuration...
   Found 2 MCP servers configured
   - sage-intacct
   - sage-business-cloud-accounting

2. Initializing vector database...
   Vector store status: healthy
   Backend: sqlite
   Database: data/ai_workspace_vectors.db

3. Initializing embedding service...
   Model: sentence-transformers/all-mpnet-base-v2
   Embedding dimension: 768
   Cache enabled: True

4. Discovering tools from MCP servers...

5. Creating tool indexing pipeline...

6. Indexing tools (this may take a moment)...

✅ Indexing complete!
   Total tools discovered: 165
   Successfully indexed: 165
   Failed: 0

7. Testing the system...
   Test query: 'Get financial summary'
   Filtered from 165 to 20 tools
   Token reduction: 87.9%

==============================================================
✅ INITIALIZATION COMPLETE
==============================================================
```

## Verification
Once complete, test with:
```bash
python run.py
```

The system should now work without token limit errors.

## Architecture Note
After the refactoring, the system uses:
- `EnhancedLLMServiceWithFiltering` for intelligent filtering
- Vector database for semantic search
- Hard limit of 20 tools per request
- Automatic fallback to regular service if filtering fails

## Technical Details
The filtering happens in `services/enhanced_llm_service_with_filtering.py`:
- Lines 98-103: Filters tools using semantic search
- Lines 161-169: Falls back to unfiltered if any error occurs
- This is why initialization is critical - without it, filtering always fails