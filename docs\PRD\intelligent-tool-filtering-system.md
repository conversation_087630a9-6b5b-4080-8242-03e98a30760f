# Intelligent Tool Filtering System - PRD & Task List

**Scaling AI Workspace to Unlimited MCP Servers Through Semantic Intelligence**

---

## Executive Summary

This PRD defines the implementation of an **Intelligent Tool Filtering System** that enables the AI Workspace to scale to unlimited MCP servers and tools without exceeding LLM token limits. The system uses vector embeddings and semantic search to intelligently pre-filter tools before sending them to the LLM, maintaining high accuracy while dramatically improving performance.

### Vision Statement

Transform the AI Workspace from a system constrained by token limits to one that can intelligently handle thousands of tools across dozens of MCP servers, getting smarter with every interaction while actually becoming faster.

### Core Innovation

Replace sending entire tool catalogs to the LLM with **intelligent semantic pre-filtering** that:
- Reduces token usage by 95% (from 48k to 2k tokens)
- Improves response time by 50-70%
- Increases accuracy through learning
- Scales to unlimited tools
- **Always intelligent from day one** - no configuration required

---

## Problem Statement

### Current State
- **Token Limit Crisis**: With 159+ SBCA tools + Intacct tools, the system exceeds GPT-4's 30k token limit
- **No Filtering**: Entire tool catalog sent to LLM every request
- **No Learning**: Each request processed in isolation
- **Linear Scaling**: More tools = more tokens = system failure

### Desired State
- **Unlimited Scale**: Handle thousands of tools across dozens of MCP servers
- **Intelligent Filtering**: Send only 15-20 most relevant tools to LLM
- **Continuous Learning**: System improves with every interaction
- **Faster Performance**: Reduced tokens = faster LLM responses

---

## Technical Architecture

### System Components

```mermaid
graph TD
    A[User Request] --> B[Query Enhancer]
    B --> C[Embedding Generator]
    C --> D[Vector Search Engine]
    D --> E[Intelligent Ranker]
    E --> F[Tool Filter max 20 tools]
    F --> G[Enhanced LLM Service]
    G --> H[Tool Execution]
    H --> I[Learning Feedback Loop]
    I --> D
    
    J[MCP Server Registration] --> K[Tool Indexer]
    K --> L[Vector Database]
    L --> D
```

### 1. Vector Database Architecture

The system uses a flexible vector database architecture with SQLite as the default, providing **intelligent filtering from the moment of installation** with zero configuration required. PostgreSQL is available as an enterprise option for massive scale:

```python
class VectorStoreFactory:
    """Factory to create appropriate vector store based on configuration"""
    
    @staticmethod
    def create(config: Dict[str, Any]) -> VectorStore:
        backend = config.get("backend", "sqlite")
        
        if backend == "sqlite":
            # Default - intelligent search from day one, no setup required
            # Provides semantic matching, relevance scoring, and pattern learning
            return SqliteVecStore(
                db_path=config.get("db_path", "data/ai_workspace_vectors.db")
            )
        elif backend == "postgresql":
            # Enterprise option - user provides connection
            if not config.get("connection_string"):
                raise ValueError("PostgreSQL requires connection_string")
            return PgVectorStore(
                connection_string=config["connection_string"]
            )
        else:
            raise ValueError(f"Unknown backend: {backend}")
```

### 2. Tool Indexing Pipeline

When MCP servers connect, their tools are automatically indexed into the vector database:

```python
class ToolIndexingPipeline:
    """
    Indexes tools from MCP servers into vector database
    """
    
    def __init__(self, vector_store: VectorStore):
        self.vector_store = vector_store
        self.embedder = ToolEmbedder()
    
    async def index_mcp_server(self, server_name: str, tools: List[Tool]):
        """
        Index all tools from a newly connected MCP server
        """
        for tool in tools:
            # Create rich embedding text
            embedding_text = self._create_embedding_text(tool)
            
            # Generate embedding
            embedding = await self.embedder.encode(embedding_text)
            
            # Store in vector database (SQLite or PostgreSQL)
            await self.vector_store.upsert(
                id=f"{server_name}__{tool.name}",
                embedding=embedding,
                metadata={
                    "server": server_name,
                    "tool_name": tool.name,
                    "description": tool.description,
                    "category": tool.category,
                    "parameters": tool.parameters,
                    "examples": tool.examples,
                    "execution_stats": {
                        "count": 0,
                        "success_rate": 0.0,
                        "avg_execution_time": 0.0,
                        "last_used": None
                    }
                }
            )
```

### 3. Intelligent Query Enhancement

Before searching, queries are enhanced with context:

```python
class QueryEnhancer:
    """
    Enhances user queries with context for better matching
    """
    
    def enhance_query(self, 
                     query: str, 
                     conversation_context: List[Dict],
                     user_preferences: Dict) -> str:
        """
        Add context to improve search relevance
        """
        enhanced_parts = [query]
        
        # Add recent conversation context
        if conversation_context:
            recent = conversation_context[-3:]  # Last 3 messages
            context_summary = self._summarize_context(recent)
            enhanced_parts.append(f"Context: {context_summary}")
        
        # Add user preferences
        if user_preferences.get("preferred_tools"):
            enhanced_parts.append(
                f"User often uses: {', '.join(user_preferences['preferred_tools'][:5])}"
            )
        
        # Add temporal context
        enhanced_parts.append(f"Current task type: {self._infer_task_type(query)}")
        
        return " | ".join(enhanced_parts)
```

### 4. Vector Search with Progressive Strategies

```python
class IntelligentToolFilter:
    """
    Always-intelligent tool filtering with semantic understanding from day one
    """
    
    async def get_relevant_tools(self, 
                                query: str, 
                                context: Dict,
                                max_tools: int = 20) -> List[Tool]:
        """
        Get relevant tools using intelligent semantic strategies
        ALWAYS uses embeddings and semantic understanding
        NEVER falls back to "dumb" mode or sends all tools
        """
        # Primary Strategy: High-precision semantic search
        tools = await self._semantic_search(query, k=max_tools, threshold=0.8)
        
        if len(tools) >= 10:
            return tools[:max_tools]
        
        # Intelligent Expansion: Broaden semantic search (still intelligent)
        tools = await self._semantic_search(query, k=30, threshold=0.6)
        
        if len(tools) < 5:
            # Smart Category Inference: Use embeddings to find category matches
            categories = self._infer_categories(query)
            category_tools = await self._search_by_categories(categories, limit=15)
            tools = self._merge_unique(tools, category_tools)
        
        if len(tools) < 5:
            # Contextual Intelligence: Popular tools based on learned patterns
            # Even with no history, uses tool metadata quality scores
            popular = await self._get_contextually_relevant_tools(context, limit=10)
            tools = self._merge_unique(tools, popular)
        
        # Always apply intelligent ranking based on:
        # - Semantic similarity scores
        # - Tool description quality
        # - Parameter matching
        # - Learned patterns (when available)
        ranked_tools = await self._intelligent_rank(tools, query, context)
        
        # HARD LIMIT - prevent token overflow while maintaining intelligence
        return ranked_tools[:max_tools]
```

### 5. Learning and Adaptation System

```python
class ToolLearningEngine:
    """
    Learns from execution feedback to improve future filtering
    """
    
    async def record_execution(self,
                             query: str,
                             selected_tool: str,
                             execution_result: Dict):
        """
        Learn from tool execution outcomes
        """
        success = execution_result.get("success", False)
        execution_time = execution_result.get("duration_ms", 0)
        
        # Update tool statistics
        await self.vector_store.update_metadata(
            selected_tool,
            {
                "execution_stats.count": {"$inc": 1},
                "execution_stats.success_rate": self._calculate_new_rate(selected_tool, success),
                "execution_stats.avg_execution_time": self._calculate_avg_time(selected_tool, execution_time),
                "execution_stats.last_used": datetime.now()
            }
        )
        
        # Store successful query-tool patterns
        if success:
            pattern_embedding = await self.embedder.encode(f"{query} -> {selected_tool}")
            await self.pattern_store.add(
                embedding=pattern_embedding,
                metadata={
                    "query": query,
                    "tool": selected_tool,
                    "success": True,
                    "timestamp": datetime.now()
                }
            )
        
        # Adjust tool embedding if significant pattern emerges
        if await self._should_update_embedding(selected_tool):
            await self._update_tool_embedding(selected_tool)
```

### 6. Integration with Enhanced LLM Service

```python
class EnhancedLLMServiceWithFiltering(EnhancedLLMService):
    """
    Enhanced LLM Service integrated with intelligent tool filtering
    """
    
    def __init__(self, tool_filter: IntelligentToolFilter):
        super().__init__()
        self.tool_filter = tool_filter
        
    async def analyze_and_respond(self,
                                 message: str,
                                 tool_catalog: Dict[str, Any],
                                 context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analyze request with intelligently filtered tools
        """
        # Get relevant tools instead of full catalog
        relevant_tools = await self.tool_filter.get_relevant_tools(
            query=message,
            context=context,
            max_tools=20  # Token budget allows ~20 tools
        )
        
        # Log filtering effectiveness
        logger.info(
            f"Filtered tools from {len(tool_catalog)} to {len(relevant_tools)} "
            f"(reduction: {100 - (len(relevant_tools)/len(tool_catalog)*100):.1f}%)"
        )
        
        # Convert to tool catalog format
        filtered_catalog = {
            tool.id: tool for tool in relevant_tools
        }
        
        # Call parent method with filtered catalog
        return await super().analyze_and_respond(
            message=message,
            tool_catalog=filtered_catalog,
            context=context
        )
```

### 7. SQLite-vec Implementation

The default SQLite implementation provides zero-configuration vector search:

```python
class SqliteVecStore(VectorStore):
    """SQLite with sqlite-vec extension for vector operations"""
    
    def __init__(self, db_path: str = "data/ai_workspace_vectors.db"):
        self.db_path = db_path
        self._ensure_directory()
        self.conn = self._initialize_db()
        
    def _initialize_db(self):
        """Initialize SQLite with vec extension"""
        import sqlite3
        import sqlite_vec
        
        # Create connection
        conn = sqlite3.connect(self.db_path)
        conn.enable_load_extension(True)
        
        # Load sqlite-vec extension
        sqlite_vec.load(conn)
        
        # Create tables
        conn.execute("""
            CREATE TABLE IF NOT EXISTS tool_embeddings (
                id TEXT PRIMARY KEY,
                tool_name TEXT NOT NULL,
                server_name TEXT NOT NULL,
                description TEXT,
                category TEXT,
                parameters TEXT,  -- JSON
                examples TEXT,    -- JSON array
                embedding BLOB,   -- Vector stored as BLOB
                
                -- Metadata
                execution_count INTEGER DEFAULT 0,
                success_rate REAL DEFAULT 0.0,
                avg_execution_time REAL DEFAULT 0.0,
                last_used TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create vector index
        conn.execute("""
            CREATE VIRTUAL TABLE IF NOT EXISTS vec_tool_embeddings USING vec0(
                tool_id TEXT PRIMARY KEY,
                embedding FLOAT[768]
            )
        """)
        
        return conn
    
    async def search(self, 
                    query_embedding: np.ndarray,
                    k: int = 20,
                    threshold: float = 0.7) -> List[Dict[str, Any]]:
        """Fast vector similarity search"""
        # Convert embedding to bytes
        embedding_bytes = query_embedding.astype(np.float32).tobytes()
        
        # KNN search with sqlite-vec
        results = self.conn.execute("""
            SELECT 
                t.id,
                t.tool_name,
                t.server_name,
                t.description,
                t.category,
                t.parameters,
                t.execution_count,
                t.success_rate,
                vec_distance_cosine(v.embedding, ?) as distance
            FROM vec_tool_embeddings v
            JOIN tool_embeddings t ON v.tool_id = t.id
            WHERE distance <= ?
            ORDER BY distance
            LIMIT ?
        """, (embedding_bytes, 1 - threshold, k)).fetchall()
        
        return [self._row_to_dict(row) for row in results]
```

**Performance Characteristics:**
- **1,000 tools**: <5ms search time with full semantic understanding
- **10,000 tools**: <20ms search time maintaining intelligence
- **100,000 tools**: ~100ms (consider PostgreSQL at this scale)
- **Day One Intelligence**: Semantic search works immediately after installation
- **Zero Configuration**: No setup required for intelligent filtering

---

## Implementation Phases

### Phase 1: Core Infrastructure (Week 1)

#### Objective
Build the foundational vector search and embedding infrastructure

#### Tasks

**1.1 Set Up Vector Database**
- [x] Install sqlite-vec Python package ✅
- [x] Create SQLite database initialization code ✅
- [x] Implement SqliteVecStore class ✅
- [x] Create vector operations with sqlite-vec ✅
- [ ] Build PostgreSQL adapter (PgVectorStore) for enterprise users
- [x] Create VectorStoreFactory for backend selection ✅
- [ ] Add automatic migration tool (SQLite → PostgreSQL)
- [x] Implement health checks for both backends ✅

**1.2 Build Embedding Service**
- [x] Set up sentence-transformers (all-mpnet-base-v2) ✅
- [x] Create `ToolEmbedder` class ✅
- [x] Implement embedding caching mechanism ✅
- [x] Add batch embedding support ✅
- [x] Create embedding text formatter ✅

**1.3 Implement Tool Indexing Pipeline**
- [x] Create `ToolIndexingPipeline` class ✅
- [x] Hook into MCP server registration ✅
- [x] Index existing tools on startup ✅
- [x] Add re-indexing capabilities ✅
- [x] Implement index monitoring ✅

**1.4 Basic Vector Search**
- [x] Implement `IntelligentToolFilter` class (basic version) ✅
- [x] Add cosine similarity search ✅
- [x] Create simple ranking algorithm ✅
- [x] Add search result caching ✅
- [x] Implement search metrics logging ✅

**1.5 Integration Testing**
- [ ] Test embedding generation
- [ ] Verify search accuracy
- [ ] Benchmark search performance
- [ ] Test with multiple MCP servers
- [ ] Validate token reduction

#### Deliverables
- **Intelligent vector search from day one** that reduces tokens by 90%+
- All tools indexed with semantic embeddings immediately
- Full intelligent filtering integrated with orchestrator
- No "basic" or "dumb" mode - always intelligent

---

### Phase 2: Enhanced Intelligence (Week 2)

#### Objective
Build on the day-one intelligence with learning, deeper context awareness, and adaptive ranking

#### Tasks

**2.1 Query Enhancement System**
- [ ] Build `QueryEnhancer` class
- [ ] Add conversation context integration
- [ ] Implement temporal context detection
- [ ] Add domain inference
- [ ] Create query expansion logic

**2.2 Progressive Search Strategies**
- [x] Implement multi-stage search fallbacks ✅
- [x] Add category-based search ✅
- [x] Build popularity-based fallback ✅
- [x] Create dynamic threshold adjustment ✅
- [x] Add search strategy metrics ✅

**2.3 Intelligent Ranking System**
- [ ] Implement multi-factor ranking algorithm
- [ ] Add execution history weighting
- [ ] Include user preference scoring
- [ ] Add recency bias for relevant tools
- [ ] Create A/B testing framework

**2.4 Learning Engine**
- [ ] Build `ToolLearningEngine` class
- [ ] Implement execution feedback loop
- [ ] Add pattern storage system
- [ ] Create embedding update mechanism
- [ ] Add cross-user learning (privacy-safe)

**2.5 Advanced Integration**
- [ ] Update `EnhancedLLMService` with filtering
- [ ] Add learning feedback to orchestrator
- [ ] Implement adaptive thresholds
- [ ] Add performance monitoring
- [ ] Create learning dashboards

#### Deliverables
- System that learns and improves with usage
- Context-aware tool selection
- 95%+ accuracy in tool relevance

---

### Phase 3: Optimization & Scale (Week 3)

#### Objective
Optimize for performance and massive scale

#### Tasks

**3.1 Performance Optimization**
- [ ] Implement embedding compression
- [ ] Add vector quantization
- [ ] Optimize index structures (IVF, HNSW)
- [ ] Add result pre-computation
- [ ] Implement smart caching layers

**3.2 Scale Testing**
- [ ] Test with 1000+ tools
- [ ] Benchmark with 10+ MCP servers
- [ ] Load test concurrent requests
- [ ] Measure latency at scale
- [ ] Identify bottlenecks

**3.3 Advanced Features**
- [ ] Add real-time embedding updates
- [ ] Implement tool relationship graphs
- [ ] Add predictive pre-filtering
- [ ] Create tool suggestion system
- [ ] Build anomaly detection

**3.4 Monitoring & Analytics**
- [ ] Create performance dashboards
- [ ] Add search quality metrics
- [ ] Implement learning effectiveness tracking
- [ ] Add cost analysis (tokens saved)
- [ ] Create system health monitoring

**3.5 Documentation & Deployment**
- [ ] Write comprehensive documentation
- [ ] Create deployment guides
- [ ] Add configuration templates
- [ ] Build admin tools
- [ ] Create troubleshooting guides

#### Deliverables
- System handling 1000+ tools with <50ms latency
- Comprehensive monitoring and analytics
- Production-ready deployment

---

## Success Metrics

### Performance Metrics
- **Token Reduction**: 95%+ (from 48k to <2k tokens)
- **Search Latency**: <50ms for tool filtering
- **Total Response Time**: 50-70% faster than current
- **Scale Capacity**: 1000+ tools without degradation

### Intelligence Metrics
- **Day One Intelligence**: 85%+ accuracy on first use (no training needed)
- **Tool Relevance**: 95%+ accuracy in top-20 results after learning
- **Learning Effectiveness**: 10% improvement per 1000 executions
- **Context Awareness**: 90%+ accuracy with context
- **Zero Configuration Performance**: Full semantic search without any setup

### System Metrics
- **Uptime**: 99.9% availability
- **Error Rate**: <0.1% search failures
- **Memory Usage**: <1GB for 1000 tools
- **CPU Usage**: <10% during search

---

## Configuration Schema

```yaml
intelligent_filtering:
  enabled: true  # Always true - no non-intelligent mode
  
  vector_database:
    # Default - intelligent from installation, zero configuration
    backend: "sqlite"  # Provides semantic search immediately
    
    # SQLite configuration (default)
    sqlite:
      db_path: "data/ai_workspace_vectors.db"
      # sqlite-vec loaded automatically
      
    # PostgreSQL configuration (enterprise option)
    # backend: "postgresql"  # Uncomment to use PostgreSQL
    postgresql:
      connection_string: "${POSTGRES_VECTOR_DB_URL}"
      # User must ensure pgvector extension is installed
      pool_size: 10
    
  embedding:
    model: "sentence-transformers/all-mpnet-base-v2"
    dimension: 768
    batch_size: 32
    cache:
      enabled: true
      ttl_hours: 24
      max_size: 10000
    
  search:
    max_results: 20  # Maximum tools to send to LLM
    strategies:
      - name: "direct_semantic"
        threshold: 0.8
        k: 20
      - name: "relaxed_semantic"
        threshold: 0.6
        k: 30
      - name: "category_based"
        enabled: true
      - name: "popularity_fallback"
        limit: 10
    
  learning:
    enabled: true
    feedback_weight: 0.2
    pattern_detection: true
    embedding_updates:
      enabled: true
      frequency: "daily"
      threshold: 100  # executions before update
    
  monitoring:
    metrics_enabled: true
    log_searches: true
    track_effectiveness: true
    dashboard_port: 9091
```

---

## Risk Mitigation

### Technical Risks

**Risk**: Embedding model limitations
- **Mitigation**: Use state-of-the-art models, plan for model upgrades
- **Fallback**: Multiple search strategies ensure results

**Risk**: Vector search accuracy
- **Mitigation**: Continuous learning and adaptation
- **Fallback**: Progressive search strategies

**Risk**: Latency at scale
- **Mitigation**: Optimized indexes, caching, pre-computation
- **Fallback**: Asynchronous processing where possible

### Operational Risks

**Risk**: Learning corruption
- **Mitigation**: Validation checks, outlier detection
- **Fallback**: Rollback to previous embeddings

**Risk**: Privacy concerns
- **Mitigation**: Anonymized learning, no PII in embeddings
- **Fallback**: Per-company isolated learning

---

## Future Enhancements

### Near-term (3-6 months)
- Multi-modal embeddings (include UI screenshots)
- Cross-MCP server tool composition
- Predictive tool pre-loading
- Natural language tool creation

### Long-term (6-12 months)
- Autonomous tool discovery
- Self-organizing tool taxonomies
- Conversational tool teaching
- Industry-specific optimizations

---

## Database Backend Comparison

### SQLite with sqlite-vec (Default)
**Advantages:**
- **Intelligent from day one** - semantic search works immediately
- Zero configuration - works out of the box
- No external dependencies
- Single file database - easy backup/restore
- Excellent performance for typical workloads (<10k tools)
- Ships with the application
- Full embedding support without setup

**When to Use:**
- Always the default choice
- Development and testing
- Production deployments up to 10,000 tools
- When immediate intelligence is required
- No compromise on features vs PostgreSQL

### PostgreSQL with pgvector (Enterprise)
**Advantages:**
- Handles massive scale (100k+ tools)
- Concurrent access from multiple processes
- Advanced indexing options (IVF, HNSW)
- Better query optimization
- Production-grade reliability

**When to Use:**
- More than 10,000 tools
- Multi-instance deployments
- High concurrency requirements
- Enterprise production environments

### Migration Path
Users can start with SQLite and seamlessly migrate to PostgreSQL when needed:
```bash
# Export from SQLite
python -m ai_workspace.tools.export_vectors --from sqlite --to postgres
```

---

## Conclusion

This Intelligent Tool Filtering System transforms the AI Workspace from a system limited by token constraints to one that scales infinitely while being **genuinely intelligent from the moment of installation**. By combining semantic search, progressive strategies, and continuous learning, we create a system that never feels "dumb" to users.

The SQLite-first approach with PostgreSQL as an enterprise option ensures:
1. **Intelligent from day one** - full semantic search works immediately
2. **Zero barrier to entry** - no configuration needed for intelligence
3. **Continuous improvement** - gets smarter with use but starts smart
4. **Scalability when needed** - clear upgrade path for growth
5. **No compromise on features** - same intelligence regardless of backend

The phased approach ensures we deliver immediate intelligence (85%+ accuracy on first use) with 95% token reduction, while building toward a system that learns and adapts to understand user intent better than any hardcoded rules ever could.

---

**Document Status**: Ready for Implementation
**Owner**: AI Workspace Team
**Timeline**: 3 weeks
**Priority**: Critical - Blocking SBCA Integration