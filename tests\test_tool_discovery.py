"""
Tests for Tool Discovery Service
"""

import pytest
import asyncio
from datetime import datetime, timezone
from typing import List, Dict, Any

from services.tool_discovery import (
    ToolDiscoveryService,
    UnifiedToolSchema,
    ToolCategory,
    ToolCapability,
    get_tool_discovery_service
)
from services.mcp_registry import (
    MCPServerRegistry,
    MCPServerConfig,
    ToolSchema
)


class MockMCPRegistry:
    """Mock MCP Registry for testing."""
    
    def __init__(self):
        self.tools = [
            ToolSchema(
                name="search_across_modules",
                description="Search across multiple Intacct modules",
                parameters={
                    "type": "object",
                    "properties": {
                        "query": {"type": "string"},
                        "modules": {"type": "array"}
                    },
                    "required": ["query"]
                },
                server_name="sage-intacct"
            ),
            ToolSchema(
                name="get_financial_summary",
                description="Get a financial summary across modules",
                parameters={
                    "type": "object",
                    "properties": {
                        "start_date": {"type": "string"},
                        "end_date": {"type": "string"}
                    }
                },
                server_name="sage-intacct"
            ),
            ToolSchema(
                name="generate_consolidated_report",
                description="Generate a consolidated report across modules",
                parameters={
                    "type": "object",
                    "properties": {
                        "report_type": {"type": "string"},
                        "start_date": {"type": "string"},
                        "end_date": {"type": "string"}
                    },
                    "required": ["report_type", "start_date", "end_date"]
                },
                server_name="sage-intacct"
            ),
            ToolSchema(
                name="create_journal_entry",
                description="Create a new journal entry in the general ledger",
                parameters={
                    "type": "object",
                    "properties": {
                        "date": {"type": "string"},
                        "entries": {"type": "array"}
                    },
                    "required": ["date", "entries"]
                },
                server_name="accounting-tools"
            ),
            ToolSchema(
                name="analyze_variance",
                description="Analyze variance between actual and budget",
                parameters={
                    "type": "object",
                    "properties": {
                        "period": {"type": "string"},
                        "threshold": {"type": "number"}
                    }
                },
                server_name="analysis-tools"
            )
        ]
    
    async def get_all_tools(self) -> List[ToolSchema]:
        """Get all tools."""
        return self.tools
    
    async def refresh_tools(self, server_name=None):
        """Refresh tools (no-op in mock)."""
        pass


@pytest.mark.asyncio
class TestToolDiscoveryService:
    """Test suite for ToolDiscoveryService."""
    
    @pytest.fixture
    def mock_registry(self):
        """Create mock registry."""
        return MockMCPRegistry()
    
    @pytest.fixture
    async def discovery_service(self, mock_registry):
        """Create discovery service with mock registry."""
        return ToolDiscoveryService(registry=mock_registry)
    
    async def test_discover_all_tools(self, discovery_service):
        """Test discovering all tools from registry."""
        tools = await discovery_service.discover_all_tools()
        
        assert len(tools) == 5
        assert all(isinstance(tool, UnifiedToolSchema) for tool in tools)
        
        # Check tool names
        tool_names = [tool.name for tool in tools]
        assert "search_across_modules" in tool_names
        assert "get_financial_summary" in tool_names
        assert "generate_consolidated_report" in tool_names
        assert "create_journal_entry" in tool_names
        assert "analyze_variance" in tool_names
    
    async def test_unified_schema_creation(self, discovery_service):
        """Test unified schema creation from tool schema."""
        tools = await discovery_service.discover_all_tools()
        
        # Check search tool
        search_tool = next(t for t in tools if t.name == "search_across_modules")
        assert search_tool.full_name == "sage-intacct:search_across_modules"
        assert search_tool.display_name == "Search Across Modules"
        assert search_tool.category == ToolCategory.SEARCH
        assert ToolCapability.SEARCH in search_tool.capabilities
        assert ToolCapability.READ in search_tool.capabilities
        
        # Check financial tool
        financial_tool = next(t for t in tools if t.name == "get_financial_summary")
        assert financial_tool.category == ToolCategory.FINANCIAL
        assert ToolCapability.READ in financial_tool.capabilities
        
        # Check reporting tool
        report_tool = next(t for t in tools if t.name == "generate_consolidated_report")
        assert report_tool.category == ToolCategory.REPORTING
        assert ToolCapability.CREATE in report_tool.capabilities
        assert ToolCapability.REPORT in report_tool.capabilities
    
    async def test_category_determination(self):
        """Test category determination logic."""
        assert UnifiedToolSchema._determine_category(
            "get_financial_summary", 
            "Get financial data"
        ) == ToolCategory.FINANCIAL
        
        assert UnifiedToolSchema._determine_category(
            "create_journal_entry",
            "Create a journal entry in the ledger"
        ) == ToolCategory.ACCOUNTING
        
        assert UnifiedToolSchema._determine_category(
            "generate_report",
            "Generate a consolidated report"
        ) == ToolCategory.REPORTING
        
        assert UnifiedToolSchema._determine_category(
            "analyze_trends",
            "Analyze financial trends"
        ) == ToolCategory.ANALYSIS
    
    async def test_capability_determination(self):
        """Test capability determination logic."""
        caps = UnifiedToolSchema._determine_capabilities(
            "get_data",
            "Retrieve financial data"
        )
        assert ToolCapability.READ in caps
        
        caps = UnifiedToolSchema._determine_capabilities(
            "create_entry",
            "Create a new journal entry"
        )
        assert ToolCapability.CREATE in caps
        
        caps = UnifiedToolSchema._determine_capabilities(
            "search_records",
            "Search through records"
        )
        assert ToolCapability.SEARCH in caps
    
    async def test_search_tools(self, discovery_service):
        """Test tool search functionality."""
        await discovery_service.discover_all_tools()
        
        # Search by query
        results = await discovery_service.search_tools("financial")
        assert len(results) > 0
        assert any("financial" in r.name.lower() or "financial" in r.description.lower() 
                  for r in results)
        
        # Search by category
        results = await discovery_service.search_tools("", category=ToolCategory.REPORTING)
        assert len(results) > 0
        assert all(r.category == ToolCategory.REPORTING for r in results)
        
        # Search by capability
        results = await discovery_service.search_tools(
            "", 
            capabilities=[ToolCapability.CREATE]
        )
        assert len(results) > 0
        assert all(ToolCapability.CREATE in r.capabilities for r in results)
        
        # Search by server
        results = await discovery_service.search_tools("", server_name="sage-intacct")
        assert len(results) == 3
        assert all(r.server_name == "sage-intacct" for r in results)
    
    async def test_relevance_scoring(self, discovery_service):
        """Test relevance scoring in search."""
        await discovery_service.discover_all_tools()
        
        # Exact name match should rank highest
        results = await discovery_service.search_tools("search_across_modules")
        assert results[0].name == "search_across_modules"
        
        # Partial match
        results = await discovery_service.search_tools("journal")
        assert any(t.name == "create_journal_entry" for t in results)
    
    async def test_get_tools_by_capability(self, discovery_service):
        """Test getting tools by capability."""
        await discovery_service.discover_all_tools()
        
        # Get all search tools
        search_tools = await discovery_service.get_tools_by_capability(ToolCapability.SEARCH)
        assert len(search_tools) > 0
        assert all(ToolCapability.SEARCH in t.capabilities for t in search_tools)
        
        # Get create tools in accounting category
        create_tools = await discovery_service.get_tools_by_capability(
            ToolCapability.CREATE,
            category=ToolCategory.ACCOUNTING
        )
        assert all(
            ToolCapability.CREATE in t.capabilities and 
            t.category == ToolCategory.ACCOUNTING 
            for t in create_tools
        )
    
    async def test_get_tools_by_category(self, discovery_service):
        """Test getting tools by category."""
        await discovery_service.discover_all_tools()
        
        financial_tools = await discovery_service.get_tools_by_category(ToolCategory.FINANCIAL)
        assert len(financial_tools) > 0
        assert all(t.category == ToolCategory.FINANCIAL for t in financial_tools)
    
    async def test_get_tool_by_name(self, discovery_service):
        """Test getting specific tool by name."""
        await discovery_service.discover_all_tools()
        
        # Get by short name
        tool = await discovery_service.get_tool_by_name("search_across_modules")
        assert tool is not None
        assert tool.name == "search_across_modules"
        
        # Get by full name
        tool = await discovery_service.get_tool_by_name("sage-intacct:search_across_modules")
        assert tool is not None
        assert tool.full_name == "sage-intacct:search_across_modules"
        
        # Disambiguate by server
        tool = await discovery_service.get_tool_by_name(
            "search_across_modules", 
            server_name="sage-intacct"
        )
        assert tool is not None
        assert tool.server_name == "sage-intacct"
    
    async def test_get_related_tools(self, discovery_service):
        """Test getting related tools."""
        await discovery_service.discover_all_tools()
        
        # Get tools related to financial summary
        related = await discovery_service.get_related_tools("get_financial_summary")
        assert len(related) > 0
        
        # Should include other financial/reporting tools
        related_names = [t.name for t in related]
        assert "generate_consolidated_report" in related_names or \
               "search_across_modules" in related_names
    
    async def test_get_statistics(self, discovery_service):
        """Test catalog statistics."""
        await discovery_service.discover_all_tools()
        
        stats = discovery_service.get_statistics()
        assert stats["total_tools"] == 5
        assert stats["servers"] == 3  # sage-intacct, accounting-tools, analysis-tools
        assert len(stats["categories"]) > 0
        assert len(stats["capabilities"]) > 0
    
    async def test_export_catalog(self, discovery_service):
        """Test catalog export for LLM consumption."""
        await discovery_service.discover_all_tools()
        
        export = await discovery_service.export_catalog()
        assert len(export) == 5
        
        # Check export format
        for tool in export:
            assert "name" in tool
            assert "display_name" in tool
            assert "description" in tool
            assert "category" in tool
            assert "capabilities" in tool
            assert "parameters" in tool
            assert "server" in tool
            assert "keywords" in tool
    
    async def test_refresh_catalog(self, discovery_service):
        """Test catalog refresh."""
        await discovery_service.discover_all_tools()
        initial_count = len(discovery_service._catalog)
        
        # Refresh should maintain the same tools
        await discovery_service.refresh_catalog()
        assert len(discovery_service._catalog) == initial_count
    
    async def test_keyword_extraction(self):
        """Test keyword extraction."""
        keywords = UnifiedToolSchema._extract_keywords(
            "get_financial_summary",
            "Get a comprehensive financial summary with revenue and expense data"
        )
        
        # Should include relevant words
        assert "financial" in keywords
        assert "summary" in keywords
        assert "revenue" in keywords
        assert "expense" in keywords
        
        # Should not include stop words
        assert "a" not in keywords
        assert "the" not in keywords
        assert "with" not in keywords
    
    async def test_empty_catalog_handling(self, discovery_service):
        """Test handling of empty catalog."""
        # Don't discover tools first
        
        # Should auto-discover when needed
        results = await discovery_service.search_tools("test")
        assert isinstance(results, list)
        
        catalog = await discovery_service.get_tool_catalog()
        assert len(catalog) == 5  # Should have discovered tools
    
    async def test_indices_update(self, discovery_service):
        """Test that search indices are properly updated."""
        await discovery_service.discover_all_tools()
        
        # Check category index
        assert len(discovery_service._category_index[ToolCategory.SEARCH]) > 0
        assert len(discovery_service._category_index[ToolCategory.FINANCIAL]) > 0
        
        # Check capability index
        assert len(discovery_service._capability_index[ToolCapability.READ]) > 0
        assert len(discovery_service._capability_index[ToolCapability.CREATE]) > 0
        
        # Check keyword index
        assert len(discovery_service._keyword_index["financial"]) > 0
        assert len(discovery_service._keyword_index["search"]) > 0
