"""
Dynamic Orchestrator Agent - Single entry point for all financial operations

This agent dynamically discovers and uses tools from registered MCP servers
to handle any financial request without hardcoded routing logic.

Refactored to use direct MCP client connections instead of FastAgent framework.
"""
import asyncio
import json
import logging
from typing import Dict, Any, List, Optional, Union, Tuple
from datetime import datetime, timezone
import re
from pathlib import Path

# Import centralized model configuration
from config.model_config import get_model_for_agent

# Import our services
from services.mcp_registry import MCPServerRegistry as MCPRegistry
from services.tool_discovery import ToolDiscoveryService
from services.parallel_executor import ParallelToolExecutor, ToolCall
from services.confidence_scorer import ConfidenceScorer
from services.tool_composition import (
    ToolWorkflow, Tool<PERSON>hain, WorkflowTemplates, WorkflowExecutor,
    WorkflowStep, StepStatus
)
from services.response_processor import ResponseProcessor
from services.llm_service import LLMService
from services.enhanced_llm_service import EnhancedLLMService

# Configure logging
logger = logging.getLogger(__name__)

# Try to import filtering service, fall back to regular if not available
try:
    from services.enhanced_llm_service_with_filtering import EnhancedLLMServiceWithFiltering
    FILTERING_AVAILABLE = True
except ImportError:
    logger.warning("Intelligent filtering not available - dependencies missing. Using regular service.")
    FILTERING_AVAILABLE = False

# Load system prompts
import os
from pathlib import Path

# Load the master orchestrator prompt
def load_system_prompt() -> str:
    """Load the orchestrator system prompt from file"""
    prompt_path = Path(__file__).parent.parent / "prompts" / "orchestrator-system-prompt.md"
    if prompt_path.exists():
        with open(prompt_path, 'r', encoding='utf-8') as f:
            content = f.read()
            # Extract the core system prompt between the markers
            start_marker = "## Core System Prompt"
            end_marker = "## Tool Description Templates"
            if start_marker in content and end_marker in content:
                start_idx = content.index(start_marker) + len(start_marker)
                end_idx = content.index(end_marker)
                # Extract the prompt between triple backticks
                prompt_section = content[start_idx:end_idx].strip()
                if "```" in prompt_section:
                    # Find content between backticks
                    parts = prompt_section.split("```")
                    if len(parts) >= 2:
                        return parts[1].strip()
            return content
    else:
        # Fallback to embedded prompt
        return """
You are the Sage Intacct AI Workspace Orchestrator - an intelligent financial operations assistant that EXECUTES tools to fulfill user requests.

## Your Core Responsibilities

1. **Intent Analysis**: Analyze user requests to determine their intent and required tools
2. **Tool Execution**: ACTUALLY EXECUTE the appropriate tools - don't just describe what you would do
3. **Workflow Coordination**: Execute multi-step processes by chaining tools intelligently
4. **Context Management**: Maintain context across tool calls within a workflow
5. **Error Recovery**: Handle tool failures gracefully with fallback strategies

## IMPORTANT: Tool Execution

When a user makes a request:
- EXECUTE the appropriate tools immediately by calling them
- DO NOT just describe what tools you would use - ACTUALLY CALL THEM
- DO NOT ask for confirmation unless the user specifically requests it
- RETURN the actual results from the tool execution
- If asked about sales, revenue, or financial data, USE the appropriate MCP tools like generate_consolidated_report or get_financial_summary
- Tools are called by using them, not by describing them

Focus on taking action and returning real results.
"""

# Initialize the orchestrator system prompt
ORCHESTRATOR_SYSTEM_PROMPT = load_system_prompt()

class DynamicOrchestrator:
    """
    Dynamic orchestrator that discovers and uses tools from all registered MCP servers
    Now uses direct MCP client connections instead of FastAgent framework
    """
    
    def __init__(self, registry: Optional[MCPRegistry] = None):
        """Initialize the dynamic orchestrator"""
        self.registry = registry or MCPRegistry()
        self.discovery = ToolDiscoveryService(self.registry)
        self._initialized = False
        self.parallel_executor = None
        self.confidence_scorer = ConfidenceScorer()
        self.workflow_executor = None
        self._workflow_templates = {}
        self._available_tools = {}
        self._model_config = get_model_for_agent("orchestrator")
        self.response_processor = ResponseProcessor()
        self.llm_service = LLMService()
        
        # Use filtering service if available, otherwise fall back to regular
        if FILTERING_AVAILABLE:
            try:
                self.enhanced_llm_service = EnhancedLLMServiceWithFiltering()
                logger.info("Initialized Dynamic Orchestrator with Intelligent Tool Filtering")
            except Exception as e:
                logger.warning(f"Failed to initialize filtering service: {e}. Using regular service.")
                self.enhanced_llm_service = EnhancedLLMService()
        else:
            self.enhanced_llm_service = EnhancedLLMService()
            logger.info("Initialized Dynamic Orchestrator (standard mode)")
            
        self._initialize_workflow_templates()
    
    async def initialize(self) -> None:
        """Initialize the orchestrator with dynamic server discovery"""
        if self._initialized:
            return
            
        try:
            # Discover all available servers
            servers = await self.registry.list_servers()
            server_names = [server['name'] for server in servers] if servers else []
            
            if not server_names:
                logger.warning("No MCP servers registered, orchestrator will have limited functionality")
            
            # Ensure all servers are connected
            for server in servers:
                server_name = server['name']
                if server['status'] != 'connected':
                    logger.info(f"Attempting to connect to server: {server_name}")
                    # The registry should handle the connection
            
            # Discover and cache available tools
            catalog = await self.discovery.get_tool_catalog()
            self._available_tools = catalog
            
            logger.info(f"Discovered {len(self._available_tools)} tools from {len(server_names)} servers")
            
            # Initialize workflow executor with our tool execution method
            self.workflow_executor = WorkflowExecutor(tool_executor=self._execute_tool_directly)
            
            # Initialize parallel executor with our tool execution method
            self.parallel_executor = ParallelToolExecutor(self._execute_tool_directly)
            
            self._initialized = True
            logger.info(f"Orchestrator initialized with servers: {server_names}")
            
        except Exception as e:
            logger.error(f"Failed to initialize orchestrator: {str(e)}", exc_info=True)
            raise    
    async def _build_dynamic_instruction(self) -> str:
        """Build dynamic instruction with discovered tools"""
        try:
            # Get tool catalog
            catalog = await self.discovery.get_tool_catalog()
            
            # If no tools available, return base prompt
            if not catalog:
                logger.warning("No tools discovered, using base orchestrator prompt")
                return ORCHESTRATOR_SYSTEM_PROMPT
            
            # Group tools by category
            tools_by_category = {}
            for tool_name, tool in catalog.items():
                category = tool.category
                if category not in tools_by_category:
                    tools_by_category[category] = []
                tools_by_category[category].append(tool)
            
            # Format tools for the prompt
            tool_descriptions = []
            for category, tools in sorted(tools_by_category.items()):
                if tools:
                    tool_descriptions.append(f"\n### {category.replace('_', ' ').title()} Tools:")
                    for tool in tools:
                        tool_descriptions.append(
                            f"- **{tool.full_name}**: {tool.description}"
                        )
            
            # Combine base prompt with dynamic tool list
            dynamic_prompt = f"{ORCHESTRATOR_SYSTEM_PROMPT}\n\n## Available Tools\n"
            if tool_descriptions:
                dynamic_prompt += "\n".join(tool_descriptions)
            else:
                dynamic_prompt += "\nNo tools currently available. I can still help with general questions and guidance."
            
            return dynamic_prompt
            
        except Exception as e:
            logger.error(f"Failed to build dynamic instruction: {str(e)}")
            return ORCHESTRATOR_SYSTEM_PROMPT
    
    def _initialize_workflow_templates(self):
        """Initialize pre-built workflow templates"""
        self._workflow_templates = {
            'month_end_close': WorkflowTemplates.month_end_close,
            'customer_onboarding': WorkflowTemplates.customer_onboarding,
            'invoice_processing': WorkflowTemplates.invoice_processing,
            'financial_reporting': WorkflowTemplates.financial_reporting
        }
        logger.info(f"Initialized {len(self._workflow_templates)} workflow templates")
    
    async def _detect_workflow_opportunity(self, message: str, intent_info: Dict[str, Any]) -> Optional[str]:
        """
        Detect if the request matches a pre-built workflow template
        
        Returns:
            Workflow template name if matched, None otherwise
        """
        message_lower = message.lower()
        intent = intent_info.get('primary_intent', '')
        
        # Direct workflow mappings
        workflow_mappings = {
            'month_end_close': ['month-end close', 'close the books', 'period close', 'month end'],
            'customer_onboarding': ['new customer', 'onboard customer', 'add customer', 'create customer'],
            'invoice_processing': ['process invoices', 'invoice processing', 'pending invoices'],
            'financial_reporting': ['financial reports', 'generate reports', 'financial statements', 'reporting package']
        }
        
        # Check for workflow keywords
        for workflow_name, keywords in workflow_mappings.items():
            if any(keyword in message_lower for keyword in keywords):
                logger.info(f"Detected workflow opportunity: {workflow_name}")
                return workflow_name
        
        # Check by intent
        intent_to_workflow = {
            'month_end_close': 'month_end_close',
            'comprehensive_analysis': 'financial_reporting'
        }
        
        if intent in intent_to_workflow:
            return intent_to_workflow[intent]
        
        return None
    
    async def execute_workflow(self, workflow_name: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute a pre-built workflow
        
        Args:
            workflow_name: Name of the workflow template
            context: Context parameters for the workflow
            
        Returns:
            Workflow execution results
        """
        if workflow_name not in self._workflow_templates:
            raise ValueError(f"Unknown workflow: {workflow_name}")
        
        # Get workflow template
        template_func = self._workflow_templates[workflow_name]
        
        # Create workflow instance with context
        if workflow_name == 'customer_onboarding':
            # Customer onboarding requires customer name
            customer_name = context.get('customer_name', 'New Customer')
            workflow = template_func(customer_name)
        elif workflow_name == 'financial_reporting':
            # Financial reporting requires period
            period = context.get('period', datetime.now().strftime('%Y-%m'))
            workflow = template_func(period)
        else:
            workflow = template_func()
        
        # Initialize workflow executor if needed (should already be done in initialize)
        if not self.workflow_executor:
            self.workflow_executor = WorkflowExecutor(tool_executor=self._execute_tool_directly)
        
        # Execute workflow
        logger.info(f"Executing workflow: {workflow_name}")
        result = await self.workflow_executor.execute(workflow, context)
        
        # Format response using response processor
        formatted_response = await self.response_processor.format_workflow_response(
            workflow_name=workflow_name,
            workflow_result=result,
            context=context
        )
        
        # Add formatted response to result
        result['formatted_response'] = formatted_response
        
        return result
    
    async def create_custom_workflow(self, steps: List[Dict[str, Any]], name: str = "custom_workflow") -> ToolWorkflow:
        """
        Create a custom workflow from step definitions
        
        Args:
            steps: List of step definitions, each containing:
                - tool_name: Name of the tool
                - parameters: Tool parameters
                - depends_on: List of step IDs this depends on
                - id (optional): Step ID
            name: Workflow name
            
        Returns:
            Created workflow
        """
        workflow = ToolWorkflow(name)
        
        for i, step_def in enumerate(steps):
            step = WorkflowStep(
                id=step_def.get('id', f'step_{i}'),
                tool_name=step_def['tool_name'],
                parameters=step_def.get('parameters', {}),
                depends_on=step_def.get('depends_on', []),
                description=step_def.get('description', '')
            )
            workflow.add_step(step)
        
        return workflow
    
    async def create_tool_chain(self, tools: List[str], name: str = "tool_chain") -> ToolChain:
        """
        Create a simple sequential tool chain
        
        Args:
            tools: List of tool names to execute in sequence
            name: Chain name
            
        Returns:
            Tool chain
        """
        chain = ToolChain(name)
        
        for tool_name in tools:
            chain.add(tool_name)
        
        return chain    
    async def _detect_intent(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analyze user message to detect intent and confidence using enhanced scoring
        
        Returns:
            Dict with intent details, confidence score, and scoring details
        """
        # Intent detection patterns (from routing-patterns.md)
        intent_patterns = {
            # GL Intents
            'gl_balance': {
                'keywords': ['balance', 'account', 'gl'],
                'patterns': ['what.*balance', 'show.*account', 'gl.*balance'],
                'confidence_boost': 0.1
            },
            'journal_entry': {
                'keywords': ['journal', 'entry', 'debit', 'credit', 'je'],
                'patterns': ['create.*journal', 'post.*entry', 'journal.*entry'],
                'confidence_boost': 0.2
            },
            'financial_statements': {
                'keywords': ['financial statement', 'p&l', 'balance sheet', 'cash flow', 'income statement'],
                'patterns': ['generate.*statement', 'show.*p&l', 'balance.*sheet'],
                'confidence_boost': 0.15
            },
            
            # AR Intents
            'customer_balance': {
                'keywords': ['customer', 'balance', 'ar', 'receivable'],
                'patterns': ['customer.*balance', 'ar.*balance', 'receivable.*amount'],
                'confidence_boost': 0.1
            },
            'create_invoice': {
                'keywords': ['invoice', 'create', 'issue', 'bill'],
                'patterns': ['create.*invoice', 'new.*invoice', 'issue.*invoice'],
                'confidence_boost': 0.2
            },
            
            # AP Intents
            'vendor_balance': {
                'keywords': ['vendor', 'balance', 'ap', 'payable'],
                'patterns': ['vendor.*balance', 'ap.*balance', 'payable.*amount'],
                'confidence_boost': 0.1
            },
            'payment_batch': {
                'keywords': ['payment batch', 'payment run', 'pay vendors', 'batch payment'],
                'patterns': ['create.*payment.*batch', 'payment.*run', 'pay.*vendor'],
                'confidence_boost': 0.25
            },            
            # Analysis Intents
            'variance_analysis': {
                'keywords': ['variance', 'budget', 'actual', 'comparison'],
                'patterns': ['variance.*analysis', 'budget.*actual', 'compare.*budget'],
                'confidence_boost': 0.15
            },
            'ratio_calculation': {
                'keywords': ['ratio', 'liquidity', 'leverage', 'margin', 'profitability'],
                'patterns': ['financial.*ratio', 'calculate.*ratio', 'liquidity.*ratio'],
                'confidence_boost': 0.15
            },
            'trend_analysis': {
                'keywords': ['trend', 'pattern', 'over time', 'historical', 'growth'],
                'patterns': ['trend.*analysis', 'historical.*pattern', 'growth.*rate'],
                'confidence_boost': 0.15
            },
            'comprehensive_analysis': {
                'keywords': ['comprehensive', 'full analysis', 'complete review', 'financial analysis', 'financial review'],
                'patterns': ['comprehensive.*analysis', 'full.*review', 'complete.*analysis'],
                'confidence_boost': 0.2
            },
            
            # Report Intents
            'executive_summary': {
                'keywords': ['executive', 'summary', 'overview', 'high-level'],
                'patterns': ['executive.*summary', 'management.*report', 'high.*level.*summary'],
                'confidence_boost': 0.15
            },
            'financial_report': {
                'keywords': ['report', 'financial report', 'statement', 'dashboard'],
                'patterns': ['generate.*report', 'create.*report', 'financial.*report'],
                'confidence_boost': 0.1
            },
            'month_end_report': {
                'keywords': ['month-end report', 'period report', 'close report'],
                'patterns': ['month.*end.*report', 'close.*report', 'period.*report'],
                'confidence_boost': 0.2
            },
            
            # Validation Intents
            'period_validation': {
                'keywords': ['period', 'status', 'open', 'closed', 'locked'],
                'patterns': ['period.*status', 'is.*period.*open', 'check.*period'],
                'confidence_boost': 0.15
            },
            'data_integrity': {
                'keywords': ['integrity', 'data quality', 'consistency', 'reconciliation'],
                'patterns': ['data.*integrity', 'check.*consistency', 'validate.*data'],
                'confidence_boost': 0.15
            },
            'balance_validation': {
                'keywords': ['balance', 'validate', 'verify', 'check balance'],
                'patterns': ['validate.*balance', 'check.*balance', 'verify.*balance'],
                'confidence_boost': 0.15
            },
            
            # Complex Workflows
            'month_end_close': {
                'keywords': ['month-end', 'close', 'period close', 'close the books'],
                'patterns': ['month.*end.*close', 'close.*period', 'close.*books'],
                'confidence_boost': 0.3
            }
        }
        
        # Initial basic scoring
        message_lower = message.lower()
        detected_intents = []
        
        for intent_name, intent_config in intent_patterns.items():
            score = 0.0
            
            # Check keywords (40% weight)
            keyword_matches = sum(1 for kw in intent_config['keywords'] if kw in message_lower)
            if keyword_matches > 0:
                score += 0.4 * (keyword_matches / len(intent_config['keywords']))
            
            # Check patterns (30% weight)
            import re
            pattern_matches = sum(1 for pattern in intent_config['patterns'] 
                                if re.search(pattern, message_lower))
            if pattern_matches > 0:
                score += 0.3 * (pattern_matches / len(intent_config['patterns']))
            
            # Add confidence boost for specific intents
            score += intent_config.get('confidence_boost', 0)
            
            if score > 0:
                detected_intents.append({
                    'intent': intent_name,
                    'confidence': min(score, 1.0)
                })
        
        # Sort by confidence
        detected_intents.sort(key=lambda x: x['confidence'], reverse=True)
        
        # Use enhanced confidence scoring
        if detected_intents:
            primary_intent = detected_intents[0]
            
            # Calculate enhanced confidence score
            context = context or {}
            enhanced_confidence, scoring_details = self.confidence_scorer.calculate_confidence(
                primary_intent['intent'],
                message,
                context,
                detected_intents
            )
            
            return {
                'primary_intent': primary_intent['intent'],
                'confidence': enhanced_confidence,
                'all_intents': detected_intents,
                'scoring_details': scoring_details,
                'confidence_action': self.confidence_scorer.get_confidence_action(enhanced_confidence)
            }
        else:
            # No intent detected
            default_confidence = 0.3
            return {
                'primary_intent': 'general_query',
                'confidence': default_confidence,
                'all_intents': [],
                'scoring_details': {},
                'confidence_action': self.confidence_scorer.get_confidence_action(default_confidence)
            }
    
    async def _select_tools(self, intent: str, message: str) -> List[str]:
        """
        Select appropriate tools based on intent and available tools
        
        Returns:
            List of tool names to use
        """
        # Map intents to tool capabilities
        intent_to_capabilities = {
            'gl_balance': ['read', 'query'],
            'journal_entry': ['create', 'write'],
            'financial_statements': ['read', 'report'],
            'customer_balance': ['read', 'query'],
            'create_invoice': ['create', 'write'],
            'vendor_balance': ['read', 'query'],
            'payment_batch': ['create', 'process'],
            'variance_analysis': ['analyze', 'compare'],
            'ratio_calculation': ['analyze', 'calculate'],
            'trend_analysis': ['analyze', 'historical'],
            'comprehensive_analysis': ['analyze', 'report'],
            'executive_summary': ['report', 'summarize'],
            'financial_report': ['report', 'generate'],
            'month_end_report': ['report', 'process'],
            'period_validation': ['validate', 'check'],
            'data_integrity': ['validate', 'check'],
            'balance_validation': ['validate', 'verify'],
            'month_end_close': ['process', 'validate', 'report']
        }
        
        # Map intents to categories
        intent_to_categories = {
            'gl_balance': ['general_ledger'],
            'journal_entry': ['general_ledger'],
            'financial_statements': ['general_ledger', 'reporting'],
            'customer_balance': ['accounts_receivable'],
            'create_invoice': ['accounts_receivable'],
            'vendor_balance': ['accounts_payable'],
            'payment_batch': ['accounts_payable'],
            'variance_analysis': ['analysis'],
            'ratio_calculation': ['analysis'],
            'trend_analysis': ['analysis'],
            'comprehensive_analysis': ['analysis', 'reporting'],
            'executive_summary': ['reporting'],
            'financial_report': ['reporting'],
            'month_end_report': ['reporting', 'validation'],
            'period_validation': ['validation'],
            'data_integrity': ['validation'],
            'balance_validation': ['validation'],
            'month_end_close': ['cross_module', 'validation']
        }        
        # Search for appropriate tools
        capabilities = intent_to_capabilities.get(intent, ['read'])
        categories = intent_to_categories.get(intent, [])
        
        results = await self.discovery.search_tools(
            query=message,
            category=categories[0] if categories else None,
            capabilities=capabilities
        )
        
        # Return top matching tools
        return [tool['full_name'] for tool in results[:3]]
    
    async def _process_with_llm(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process user request with Enhanced LLM for unified analysis and response
        
        This uses the EnhancedLLMService which:
        1. Analyzes the request with full context
        2. Determines if tools are needed or conversational response
        3. Returns either tool calls to execute or direct response
        4. No pattern matching fallbacks
        """
        try:
            # Use enhanced LLM service for unified analysis and response
            logger.info("Using EnhancedLLMService for unified processing")
            llm_result = await self.enhanced_llm_service.analyze_and_respond(
                message=message,
                tool_catalog=self._available_tools,
                context=context
            )
            
            response_type = llm_result.get("response_type")
            logger.info(f"LLM response type: {response_type}")
            
            # Handle different response types
            if response_type == "tool_execution":
                # Execute the requested tools
                tool_calls = llm_result.get("tool_calls", [])
                results = []
                tools_executed = []
                
                for tool_call in tool_calls:
                    try:
                        result = await self._execute_tool_directly(
                            tool_call['tool'],
                            tool_call.get('parameters', {})
                        )
                        results.append({
                            "tool": tool_call['tool'],
                            "success": True,
                            "result": result
                        })
                        tools_executed.append(tool_call['tool'])
                        logger.info(f"Successfully executed tool: {tool_call['tool']}")
                    except Exception as e:
                        logger.error(f"Tool execution failed: {str(e)}")
                        results.append({
                            "tool": tool_call['tool'],
                            "success": False,
                            "error": str(e)
                        })
                
                # Use response processor to format the tool results
                if results:
                    processed_response = await self.response_processor.process_tool_results(
                        user_request=message,
                        tool_results=results,
                        context=context
                    )
                    response = processed_response.get("response", "Results processed successfully.")
                else:
                    response = llm_result.get("response_preview", "No results to display.")
                
                return {
                    "response": response,
                    "tools_executed": tools_executed,
                    "execution_details": {
                        "llm_analysis": llm_result,
                        "tool_calls": tool_calls,
                        "results": results
                    }
                }
                
            elif response_type == "conversational":
                # Return the conversational response directly
                return {
                    "response": llm_result.get("conversational_response", "I can help with that."),
                    "tools_executed": [],
                    "execution_details": {
                        "llm_analysis": llm_result,
                        "response_type": "conversational"
                    }
                }
                
            elif response_type == "error":
                # Handle error response
                return {
                    "response": llm_result.get("conversational_response", llm_result.get("error_message", "An error occurred.")),
                    "tools_executed": [],
                    "execution_details": {
                        "llm_analysis": llm_result,
                        "error": True
                    }
                }
                
            else:
                # Unexpected response type
                logger.warning(f"Unexpected response type: {response_type}")
                return {
                    "response": "I'm not sure how to help with that request.",
                    "tools_executed": [],
                    "execution_details": {
                        "llm_analysis": llm_result,
                        "unexpected_type": response_type
                    }
                }
                
        except Exception as e:
            logger.error(f"Enhanced LLM processing failed: {str(e)}", exc_info=True)
            return {
                "response": f"I encountered an error processing your request: {str(e)}. Please ensure the system is properly configured with an OpenAI API key.",
                "tools_executed": [],
                "execution_details": {"error": str(e)}
            }
    
    
    async def _execute_tool_directly(self, tool_name: str, parameters: Dict[str, Any]) -> Any:
        """Execute a single tool directly via MCP client"""
        try:
            # Parse tool name if it contains server prefix
            if ':' in tool_name:
                server_name, actual_tool_name = tool_name.split(':', 1)
            else:
                # Try to find the tool in our catalog
                tool_info = self._available_tools.get(tool_name)
                if not tool_info:
                    # Check with mcp__ prefix (MCP tool format)
                    if tool_name.startswith('mcp__'):
                        parts = tool_name.split('__')
                        if len(parts) >= 3:
                            server_name = parts[1]
                            actual_tool_name = '__'.join(parts[2:])
                        else:
                            raise ValueError(f"Invalid MCP tool name format: {tool_name}")
                    else:
                        raise ValueError(f"Tool not found: {tool_name}")
                else:
                    server_name = tool_info.server_name
                    actual_tool_name = tool_info.name
            
            # Execute the tool via registry
            result = await self.registry.call_tool(server_name, actual_tool_name, parameters)
            
            logger.info(f"Executed tool {tool_name} successfully")
            return result
            
        except Exception as e:
            logger.error(f"Failed to execute tool {tool_name}: {str(e)}")
            raise
    
    async def execute_parallel_tools(self, tool_calls: List[Dict[str, Any]], 
                                   allow_partial_failure: bool = True) -> Dict[str, Any]:
        """
        Execute multiple tools in parallel when possible
        
        Args:
            tool_calls: List of tool call specifications, each with:
                - tool_name: Name of the tool to execute
                - parameters: Parameters for the tool
                - id (optional): Unique identifier for the call
                - dependencies (optional): List of call IDs this depends on
            allow_partial_failure: Whether to continue if some tools fail
            
        Returns:
            Dict containing results, errors, and execution metadata
        """
        if not self.parallel_executor:
            # Initialize parallel executor with our tool execution function
            self.parallel_executor = ParallelToolExecutor(self._execute_tool_directly)
        
        result = await self.parallel_executor.execute_parallel(tool_calls, allow_partial_failure)
        
        # Format response using response processor
        if result.get('success') or (allow_partial_failure and result.get('results')):
            formatted_response = await self.response_processor.format_parallel_response(
                parallel_result=result
            )
            result['formatted_response'] = formatted_response
        
        return result
    
    async def _detect_parallel_opportunities(self, message: str, intent_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Detect opportunities for parallel tool execution based on the request
        
        Args:
            message: User's message
            intent_info: Intent detection results
            
        Returns:
            List of tool calls that can be parallelized
        """
        parallel_patterns = {
            # Multiple module validation
            'multi_module_validation': {
                'keywords': ['all modules', 'validate everything', 'check all'],
                'tools': [
                    {'tool_name': 'mcp__sage-intacct__health_check', 'parameters': {}},
                    {'tool_name': 'mcp__sage-intacct__list_enabled_modules', 'parameters': {}},
                    {'tool_name': 'mcp__sage-intacct__get_financial_summary', 'parameters': {}}
                ]
            },
            
            # Month-end close workflow
            'month_end_parallel': {
                'keywords': ['month-end', 'close', 'period close'],
                'tools': [
                    # First batch - data gathering
                    {'tool_name': 'mcp__sage-intacct__get_financial_summary', 
                     'parameters': {'include_modules': ['GL', 'AR', 'AP']}, 'id': 'summary'},
                    
                    # Second batch - validation (depends on summary)
                    {'tool_name': 'mcp__sage-intacct__execute_month_end_close',
                     'parameters': {'dry_run': True, 'modules': ['GL', 'AR', 'AP']}, 
                     'id': 'validation', 'dependencies': ['summary']}
                ]
            },
            
            # Comprehensive analysis
            'comprehensive_analysis': {
                'keywords': ['comprehensive', 'full analysis', 'complete review'],
                'tools': [
                    # Parallel data gathering
                    {'tool_name': 'mcp__sage-intacct__search_across_modules',
                     'parameters': {'query': 'current period transactions', 'limit': 50}},
                    {'tool_name': 'mcp__sage-intacct__get_financial_summary',
                     'parameters': {}},
                    {'tool_name': 'mcp__sage-intacct__list_enabled_modules',
                     'parameters': {}}
                ]
            }
        }
        
        # Check message against patterns
        message_lower = message.lower()
        
        for pattern_name, pattern_config in parallel_patterns.items():
            if any(keyword in message_lower for keyword in pattern_config['keywords']):
                logger.info(f"Detected parallel execution opportunity: {pattern_name}")
                return pattern_config['tools']
        
        # Check if multiple tools were selected for the intent
        if 'tools_selected' in intent_info and len(intent_info.get('tools_selected', [])) > 1:
            # Create parallel calls for multiple independent tools
            tool_calls = []
            for tool_name in intent_info['tools_selected']:
                tool_calls.append({
                    'tool_name': tool_name,
                    'parameters': self._extract_parameters_from_message(message, tool_name)
                })
            return tool_calls
        
        return []
    
    async def _generate_llm_response(self, message: str, context: Dict[str, Any]) -> str:
        """
        Generate a response using LLM when no tools are needed
        
        Args:
            message: User message
            context: Additional context
            
        Returns:
            Generated response
        """
        try:
            # Use the response processor's LLM capabilities
            system_prompt = """You are an AI assistant for accounting and financial operations. 
You have access to tools for Sage Intacct including financial summaries, searches, reports, and more.

When users ask questions:
1. If it's a greeting, respond warmly and explain your capabilities
2. If it's about accounting/finance but vague, ask for clarification with specific examples
3. If it's off-topic, politely redirect to accounting/finance topics
4. Always be helpful and professional

Available tools include:
- Financial summaries and reports
- Searching across GL, AR, AP modules  
- Account balances and transactions
- Month-end close procedures
- Consolidated reporting"""
            
            # Call LLM through response processor
            response = await self.response_processor._call_llm(
                system_prompt=system_prompt,
                user_prompt=message
            )
            return response.strip()
                
        except Exception as e:
            logger.error(f"Failed to generate LLM response: {str(e)}")
            # Fallback response
            return "I can help with accounting and financial operations. Please let me know what specific information you're looking for."
    
    def _get_off_topic_acknowledgment(self, message: str) -> Optional[str]:
        """
        Check if message is about off-topic subjects and return acknowledgment
        
        Args:
            message: Lowercase user message
            
        Returns:
            Acknowledgment message or None if not off-topic
        """
        # Weather related
        if any(word in message for word in ['weather', 'temperature', 'forecast', 'rain', 'snow', 'sunny', 'cloudy']):
            return "I'm sorry, I can't help with weather information. I'm specialized in accounting and financial operations. I can help you with financial reports, account balances, or transaction searches."
        
        # Sports related
        if any(word in message for word in ['game', 'score', 'sports', 'football', 'basketball', 'soccer', 'baseball']):
            return "I don't have access to sports information. I focus on accounting and financial data. Is there anything related to your financial accounts I can help you with?"
        
        # News related
        if any(word in message for word in ['news', 'headlines', 'current events', 'politics']):
            return "I can't provide news or current events. However, I can help you with financial news from your accounting systems, like recent transactions or financial summaries."
        
        # Entertainment
        if any(word in message for word in ['movie', 'music', 'song', 'show', 'netflix', 'youtube']):
            return "I don't have information about entertainment. I'm designed to help with accounting tasks like generating reports, checking balances, or searching financial records."
        
        # Food/Restaurants
        if any(word in message for word in ['restaurant', 'food', 'recipe', 'cooking', 'lunch', 'dinner']):
            return "I can't help with food or restaurant recommendations. But I can help you track restaurant expenses or food-related transactions in your accounting system!"
        
        # General knowledge questions
        if any(phrase in message for phrase in ['what is', 'who is', 'where is', 'when was', 'how do i', 'tell me about']):
            # Check if it's not about accounting/financial topics
            accounting_terms = ['account', 'balance', 'revenue', 'expense', 'invoice', 'payment', 'transaction', 'report']
            if not any(term in message for term in accounting_terms):
                return "I understand you're looking for general information, but I'm specialized in accounting and financial operations. I can answer questions about your financial data, generate reports, or help with account management."
        
        return None
    
    def _extract_parameters_from_message(self, message: str, tool_name: str) -> Dict[str, Any]:
        """Extract relevant parameters from the message for a specific tool"""
        # This is a simplified version - in production, use NLP or LLM
        params = {}
        
        # Extract date ranges
        import re
        date_pattern = r'\b(\d{4}-\d{2}-\d{2})\b'
        dates = re.findall(date_pattern, message)
        if dates:
            if len(dates) >= 2:
                params['start_date'] = dates[0]
                params['end_date'] = dates[1]
            elif len(dates) == 1:
                params['end_date'] = dates[0]
        
        # Extract modules
        modules = []
        for module in ['GL', 'AR', 'AP']:
            if module in message.upper():
                modules.append(module)
        if modules:
            params['modules'] = modules
        
        return params
    
    async def process(self, message: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Process a user request through the dynamic orchestrator
        
        Args:
            message: The user's request (string or dict with text/context)
            
        Returns:
            Dict containing the orchestrated response
        """
        try:
            # Ensure initialized
            await self.initialize()
            
            # Parse message
            if isinstance(message, dict):
                text = message.get('text', '') or message.get('message', '')
                context = message.get('context', {})
            else:
                text = message
                context = {}
            
            # Process the request using direct MCP tool execution
            result = await self._process_with_llm(text, context)
            
            # Return response with metadata
            return {
                "response": result.get("response", ""),
                "metadata": {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "orchestrator_version": "3.0",  # Updated version for MCP client mode
                    "context": context,
                    "tools_executed": result.get("tools_executed", []),
                    "execution_details": result.get("execution_details", {})
                }
            }
            intent_info = await self._detect_intent(text, context)
            logger.info(f"Detected intent: {intent_info['primary_intent']} "
                       f"(confidence: {intent_info['confidence']:.2f}, "
                       f"action: {intent_info['confidence_action']['action']})")
            
            # Log scoring details in debug mode
            if os.getenv("DEBUG"):
                logger.debug(f"Confidence scoring details: {json.dumps(intent_info.get('scoring_details', {}), indent=2)}")
            
            # Handle based on confidence action
            confidence_action = intent_info['confidence_action']['action']
            
            if confidence_action == 'clarify':
                return await self._handle_low_confidence(text, intent_info)
            elif confidence_action == 'confirm_and_execute':
                return await self._handle_medium_confidence(text, intent_info)
            
            # Check for workflow opportunities first
            workflow_name = await self._detect_workflow_opportunity(text, intent_info)
            
            if workflow_name:
                # Execute pre-built workflow
                logger.info(f"Executing workflow: {workflow_name}")
                
                # Extract context for workflow
                workflow_context = context.copy()
                workflow_context.update(self._extract_parameters_from_message(text, workflow_name))
                
                # Handle specific workflow context requirements
                if workflow_name == 'customer_onboarding':
                    # Extract customer name from message
                    import re
                    customer_match = re.search(r'(?:customer|client|company)(?:\s+named?)?\s+([A-Za-z0-9\s&\-\.]+)', text, re.I)
                    if customer_match:
                        workflow_context['customer_name'] = customer_match.group(1).strip()
                
                try:
                    workflow_result = await self.execute_workflow(workflow_name, workflow_context)
                    
                    # Format workflow response
                    if workflow_result['status'] == 'completed':
                        response = f"I've completed the {workflow_name.replace('_', ' ')} workflow successfully.\n\n"
                        
                        # Add summary of completed steps
                        workflow_state = workflow_result['workflow']
                        completed_steps = [
                            step for step, result in workflow_state['results'].items()
                            if result['status'] == 'completed'
                        ]
                        
                        response += f"Completed {len(completed_steps)} steps:\n"
                        for step in completed_steps:
                            response += f"✓ {step.replace('_', ' ').title()}\n"
                        
                        # Add key results
                        if workflow_result.get('final_results'):
                            response += "\nKey Results:\n"
                            for step_id, result in workflow_result['final_results'].items():
                                if isinstance(result, dict) and 'report_type' in result:
                                    response += f"- Generated {result['report_type'].replace('_', ' ')}\n"
                    else:
                        response = f"The {workflow_name.replace('_', ' ')} workflow encountered an issue: {workflow_result.get('error', 'Unknown error')}"
                    
                    return {
                        "response": response,
                        "metadata": {
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                            "intent": intent_info['primary_intent'],
                            "confidence": intent_info['confidence'],
                            "workflow_executed": workflow_name,
                            "workflow_result": workflow_result,
                            "orchestrator_version": "2.0"
                        }
                    }
                
                except Exception as e:
                    logger.error(f"Workflow execution failed: {str(e)}", exc_info=True)
                    # Fall through to regular processing
            
            # Check for parallel execution opportunities
            parallel_tools = await self._detect_parallel_opportunities(text, intent_info)
            
            if parallel_tools:
                # Execute tools in parallel
                logger.info(f"Executing {len(parallel_tools)} tools in parallel")
                parallel_result = await self.execute_parallel_tools(parallel_tools)
                
                # Format response based on results
                if parallel_result['success']:
                    response_parts = ["I've completed the requested operations. Here are the results:\n"]
                    
                    # Add aggregated results
                    if parallel_result.get('aggregated'):
                        for category, data in parallel_result['aggregated'].items():
                            response_parts.append(f"\n{category.replace('_', ' ').title()}:")
                            if isinstance(data, dict):
                                for key, value in data.items():
                                    response_parts.append(f"  - {key}: {value}")
                            else:
                                response_parts.append(f"  {data}")
                    
                    response = "\n".join(response_parts)
                else:
                    # Handle partial failures
                    response = "I encountered some issues while processing your request:\n"
                    for error_id, error_msg in parallel_result['errors'].items():
                        response += f"\n- {error_id}: {error_msg}"
                    
                    if parallel_result['results']:
                        response += "\n\nHowever, I was able to complete some operations successfully."
                
                return {
                    "response": response,
                    "metadata": {
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "intent": intent_info['primary_intent'],
                        "confidence": intent_info['confidence'],
                        "confidence_action": intent_info['confidence_action']['action'],
                        "scoring_details": intent_info.get('scoring_details', {}),
                        "parallel_execution": True,
                        "execution_stats": parallel_result['metadata'],
                        "orchestrator_version": "2.0"
                    }
                }
            
            # Single tool execution (existing flow)
            selected_tools = await self._select_tools(
                intent_info['primary_intent'], 
                text
            )
            logger.info(f"Selected tools: {selected_tools}")
            
            # Execute through fast-agent orchestrator
            async with self.fast_agent.run() as agent:
                # Add intent and tool info to the message for the LLM
                enhanced_message = (
                    f"{text}\n\n"
                    f"[Intent: {intent_info['primary_intent']} "
                    f"(confidence: {intent_info['confidence']:.2f})]"
                )
                
                response = await agent["workspace_orchestrator"].send(enhanced_message)
            
            return {
                "response": response,
                "metadata": {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "intent": intent_info['primary_intent'],
                    "confidence": intent_info['confidence'],
                    "confidence_action": intent_info['confidence_action']['action'],
                    "scoring_details": intent_info.get('scoring_details', {}),
                    "tools_selected": selected_tools,
                    "orchestrator_version": "2.0"
                }
            }
            """
            
        except Exception as e:
            logger.error(f"Orchestrator error: {str(e)}", exc_info=True)
            return {
                "error": str(e),
                "response": f"I encountered an error while processing your request: {str(e)}",
                "metadata": {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "error_type": type(e).__name__
                }
            }    
    async def _handle_low_confidence(self, message: str, intent_info: Dict[str, Any]) -> Dict[str, Any]:
        """Handle low confidence scenarios with clarification"""
        # Generate clarification prompt using confidence scorer
        clarification = self.confidence_scorer.generate_clarification_prompt(
            message,
            intent_info['all_intents'],
            intent_info['confidence']
        )
        
        return {
            "response": clarification,
            "metadata": {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "intent": "clarification_needed",
                "confidence": intent_info['confidence'],
                "confidence_action": "clarify",
                "detected_intents": intent_info['all_intents'],
                "scoring_details": intent_info.get('scoring_details', {}),
                "orchestrator_version": "2.0"
            }
        }
    
    async def _handle_medium_confidence(self, message: str, intent_info: Dict[str, Any]) -> Dict[str, Any]:
        """Handle medium confidence scenarios with confirmation"""
        # Generate confirmation prompt
        confirmation = self.confidence_scorer.generate_clarification_prompt(
            message,
            intent_info['all_intents'],
            intent_info['confidence']
        )
        
        # Add execution readiness indicator
        confirmation += "\n\n✓ I'm ready to proceed once you confirm."
        
        return {
            "response": confirmation,
            "metadata": {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "intent": intent_info['primary_intent'],
                "confidence": intent_info['confidence'],
                "confidence_action": "confirm_and_execute",
                "detected_intents": intent_info['all_intents'],
                "scoring_details": intent_info.get('scoring_details', {}),
                "ready_to_execute": True,
                "orchestrator_version": "2.0"
            }
        }
        
        except Exception as e:
            logger.error(f"Error processing request: {str(e)}", exc_info=True)
            return {
                "response": f"I encountered an error: {str(e)}. Please try again or rephrase your request.",
                "metadata": {
                    "error": str(e),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            }
    
    async def get_available_tools(self) -> Dict[str, Any]:
        """Get catalog of all available tools"""
        return await self.discovery.get_tool_catalog()
    
    async def add_mcp_server(self, server_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a new MCP server at runtime
        
        Args:
            server_config: Configuration for the new server
            
        Returns:
            Status and discovered tools
        """
        try:
            # Register the server
            await self.registry.register_server(
                name=server_config['name'],
                config=server_config
            )
            
            # Reinitialize to include new server
            self._initialized = False
            await self.initialize()
            
            # Get tools from the new server
            tools = await self.registry.get_server_tools(server_config['name'])
            
            return {
                "status": "success",
                "server": server_config['name'],
                "tools_discovered": len(tools),
                "tools": [{"name": t.name, "description": t.description} for t in tools]
            }
            
        except Exception as e:
            logger.error(f"Failed to add MCP server: {str(e)}", exc_info=True)
            return {
                "status": "error",
                "error": str(e)
            }    
    async def remove_mcp_server(self, server_name: str) -> Dict[str, Any]:
        """Remove an MCP server"""
        try:
            await self.registry.unregister_server(server_name)
            
            # Reinitialize without the server
            self._initialized = False
            await self.initialize()
            
            return {
                "status": "success",
                "message": f"Server '{server_name}' removed successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to remove MCP server: {str(e)}", exc_info=True)
            return {
                "status": "error", 
                "error": str(e)
            }
    
    async def record_feedback(self, 
                            interaction_id: str,
                            user_feedback: str,
                            metadata: Dict[str, Any]) -> None:
        """
        Record user feedback for confidence learning
        
        Args:
            interaction_id: Unique interaction ID
            user_feedback: Feedback type (correct, incorrect, partial)
            metadata: Interaction metadata including intent and confidence
        """
        try:
            # Extract relevant data from metadata
            intent = metadata.get('intent', 'unknown')
            confidence = metadata.get('confidence', 0.0)
            confidence_action = metadata.get('confidence_action', 'unknown')
            
            # Record feedback
            self.confidence_scorer.record_feedback(
                interaction_id=interaction_id,
                user_input=metadata.get('user_input', ''),
                intent=intent,
                confidence_score=confidence,
                action_taken=confidence_action,
                user_feedback=user_feedback,
                additional_data={
                    'tools_selected': metadata.get('tools_selected', []),
                    'parallel_execution': metadata.get('parallel_execution', False),
                    'scoring_details': metadata.get('scoring_details', {})
                }
            )
            
            logger.info(f"Recorded feedback for interaction {interaction_id}: {user_feedback}")
            
        except Exception as e:
            logger.error(f"Failed to record feedback: {str(e)}", exc_info=True)


# Create singleton instance
orchestrator = DynamicOrchestrator()


# Wrapper class for compatibility with existing agent interface
class OrchestratorAgent:
    # Wrapper class to maintain compatibility with existing agent interface
    
    def __init__(self):
        self.agent_id = "orchestrator_agent"
        self.orchestrator = orchestrator
    
    async def send(self, message: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
        # Send message to orchestrator
        result = await self.orchestrator.process(message)
        result["agent_id"] = self.agent_id
        return result


# Create wrapped instance for backward compatibility
orchestrator_agent = OrchestratorAgent()

# CLI interface for testing
if __name__ == "__main__":
    async def main():
        # Run the orchestrator interactively
        print("\n🎯 Dynamic AI Workspace Orchestrator")
        print("=" * 50)
        print("\nI can help with any financial operation across GL, AR, AP, and more.")
        print("Just describe what you need in natural language!")
        print("\nType 'tools' to see available tools, or 'exit' to quit.\n")
        
        # Initialize orchestrator
        await orchestrator.initialize()
        
        while True:
            try:
                message = input("[USER] ").strip()
                if message.lower() in ['exit', 'quit']:
                    break
                elif message.lower() == 'tools':
                    catalog = await orchestrator.get_available_tools()
                    print("\n📋 Available Tools by Category:")
                    for category, tools in catalog['by_category'].items():
                        if tools:
                            print(f"\n{category}:")
                            for tool in tools[:3]:  # Show first 3 per category
                                print(f"  - {tool['name']}: {tool['description'][:60]}...")
                    print(f"\nTotal tools available: {catalog['total_tools']}\n")
                else:
                    result = await orchestrator.process(message)
                    print(f"\n[Orchestrator] {result['response']}\n")
                    
                    # Show metadata in debug mode
                    if os.getenv("DEBUG"):
                        print(f"[Debug] Intent: {result['metadata'].get('intent')}")
                        print(f"[Debug] Confidence: {result['metadata'].get('confidence')}")
                        print(f"[Debug] Tools: {result['metadata'].get('tools_selected')}\n")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"Error: {e}")
        
        print("\nGoodbye!")
    
    asyncio.run(main())