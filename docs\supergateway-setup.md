# Supergateway Setup for Independent MCP Servers

## Overview

This document explains how we use [Supergateway](https://github.com/supercorp-ai/supergateway) to expose stdio-based MCP servers as SSE endpoints, allowing them to run independently from the client applications.

## Problem Statement

- **Stdio MCP servers** require the client to spawn and manage the server process
- This conflicts with our architecture where MCP servers should run independently
- We need MCP servers to be accessible by multiple clients without lifecycle management

## Solution: Supergateway

Supergateway acts as a bridge that:
1. Spawns and manages the stdio MCP server
2. Exposes it via SSE (Server-Sent Events) and HTTP endpoints
3. Allows multiple clients to connect to the same server instance

## Setup Instructions

### 1. Install Supergateway

Supergateway is installed automatically via npx when needed, no manual installation required.

### 2. Run Intacct MCP Server with Supergateway

**Windows:**
```batch
scripts\run-intacct-supergateway.bat
```

**Linux/WSL:**
```bash
chmod +x scripts/run-intacct-supergateway.sh
./scripts/run-intacct-supergateway.sh
```

This will:
- Start the Intacct MCP server
- Expose it on `http://localhost:8001`
- SSE endpoint: `http://localhost:8001/sse`
- Message endpoint: `http://localhost:8001/message`

### 3. Configuration

The `fastagent.config.yaml` is already configured to connect to the Supergateway SSE endpoint:

```yaml
sage-intacct:
  command: npx
  args: ["-y", "supergateway", "--sse", "http://localhost:8001/sse"]
```

## How It Works

1. **Supergateway starts** and spawns the stdio MCP server as a subprocess
2. **Clients connect** to Supergateway's SSE endpoint instead of spawning the server
3. **Messages flow** through Supergateway between clients and the stdio server
4. **Server runs independently** - multiple clients can connect/disconnect without affecting the server

## Benefits

✅ **Independence**: MCP servers run independently from clients  
✅ **Multi-client**: Multiple clients can connect to the same server  
✅ **No lifecycle management**: Clients don't need to manage server processes  
✅ **Alignment**: Matches our decoupled architecture requirements  

## Testing

1. Start Supergateway: `scripts\run-intacct-supergateway.bat`
2. Start AI Workspace: `python run.py`
3. Use the chat endpoint - fast-agent will connect to the SSE endpoint
4. The MCP server remains running even if you stop/restart the AI Workspace

## Troubleshooting

**Port already in use:**
- Change the port in the script (e.g., 8002 instead of 8001)
- Update fastagent.config.yaml to match

**Connection refused:**
- Ensure Supergateway is running before starting the AI Workspace
- Check firewall settings for port 8001

**Authentication issues:**
- The Intacct MCP server handles its own authentication
- Ensure OAuth is properly configured in the MCP server

## Adding Other Stdio Servers

To add another stdio-based MCP server:

1. Create a new Supergateway script:
```bash
npx -y supergateway \
    --stdio "command to start your stdio server" \
    --port 8002 \
    --baseUrl http://localhost:8002 \
    --ssePath /sse \
    --messagePath /message
```

2. Update fastagent.config.yaml:
```yaml
your-server:
  command: npx
  args: ["-y", "supergateway", "--sse", "http://localhost:8002/sse"]
```

## Architecture Diagram

```
┌─────────────────┐     SSE      ┌──────────────┐     stdio     ┌─────────────┐
│   AI Workspace  │──────────────▶│  Supergateway│───────────────▶│  MCP Server │
│   (fast-agent)  │◀──────────────│   Port 8001  │◀───────────────│  (Intacct)  │
└─────────────────┘     HTTP      └──────────────┘                └─────────────┘
```

## References

- [Supergateway GitHub](https://github.com/supercorp-ai/supergateway)
- [MCP Documentation](https://modelcontextprotocol.io/)
- [SSE Specification](https://html.spec.whatwg.org/multipage/server-sent-events.html)