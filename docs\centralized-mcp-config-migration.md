# Centralized MCP Configuration Migration Guide

## Overview

This guide explains how to migrate from hardcoded MCP server configurations to our new centralized configuration system that follows the DRY principle.

## Current Problems (Before Migration)

1. **MCP server names hardcoded in multiple places**:
   - Each agent file: `servers=["sage-intacct"]`
   - Tool mapper initialization: `IntacctToolMapper("sage-intacct")`
   - Agent registration: `"mcp_server": "sage-intacct"`

2. **Configuration changes require multiple file updates**:
   - Switching from local to remote MCP server requires editing every agent
   - Adding a new MCP server means updating multiple files
   - No single source of truth

## New Centralized Solution

### 1. Single Configuration Source

All MCP configuration is now centralized in:
- `config/mcp_config.py` - Configuration manager
- `fastagent.config.yaml` - MCP server definitions

### 2. Automatic Configuration

Agents now automatically get their MCP configuration:

```python
# Old way (hardcoded)
@fast.agent(
    name="gl_agent",
    servers=["sage-intacct"],  # Hardcoded!
    instruction="..."
)

# New way (automatic)
class GLAgentV2(ConfiguredAgent):
    def __init__(self):
        super().__init__(
            agent_id="gl_agent",  # Config lookup key
            name="General Ledger Agent",
            instruction="..."
        )
        # MCP servers configured automatically!
```

### 3. Benefits

1. **True DRY**: Change MCP server in ONE place, affects ALL agents
2. **Runtime flexibility**: Switch between local/remote without restart
3. **Easy scaling**: Add new business systems with minimal code
4. **Type safety**: Pydantic models ensure configuration validity

## Migration Steps

### Step 1: Update `fastagent.config.yaml`

Ensure your config file has the correct MCP server names:

```yaml
mcp_servers:
  sage-intacct:  # This name is referenced centrally
    command: cmd
    args: ["/c", "..."]
    # ... rest of config
```

### Step 2: Update Business System Mappings

Edit `config/mcp_config.py` to define your business systems:

```python
self.business_systems = {
    "intacct": BusinessSystemConfig(
        system_name="intacct",
        mcp_server_name="sage-intacct",  # Maps to MCP server
        agents=["gl_agent", "ar_agent", "ap_agent"],
        tool_mapper_class="agents.intacct.intacct_tool_mapper.IntacctToolMapper"
    ),
    # Add more systems here
}
```

### Step 3: Migrate Agents

Convert agents to use `ConfiguredAgent` base class:

```python
# Before
from mcp_agent.core.fastagent import FastAgent

fast = FastAgent("GL Agent", config_path=config_file)
tool_mapper = IntacctToolMapper("sage-intacct")  # Hardcoded!

@fast.agent(servers=["sage-intacct"], ...)  # Hardcoded!
async def process(query: str):
    # ...

# After
from agents.base.configured_agent import ConfiguredAgent

class GLAgentV2(ConfiguredAgent):
    def __init__(self):
        super().__init__(
            agent_id="gl_agent",
            name="General Ledger Agent",
            instruction="..."
        )
        # Everything is configured automatically!
```

### Step 4: Update Agent Registration

The registration now just needs the agent instance:

```python
# Before
await manager.register_agent(
    agent_id="gl_agent",
    metadata={"mcp_server": "sage-intacct"}  # Hardcoded!
)

# After
await manager.register_agent(
    agent_id="gl_agent",
    metadata={"mcp_server": gl_agent_v2.mcp_servers[0]}  # From config!
)
```

## Runtime Configuration Changes

### Switching to Remote MCP Server

```python
from config.mcp_config import switch_to_remote_mcp

# Switch ALL agents using sage-intacct to remote endpoint
switch_to_remote_mcp("sage-intacct", "https://intacct-mcp.example.com/sse")

# All agents automatically use the new endpoint!
```

### Adding a New Business System

1. Add MCP server to `fastagent.config.yaml`:
```yaml
mcp_servers:
  quickbooks-online:
    command: npx
    args: ["@quickbooks/mcp-server"]
```

2. Add business system mapping:
```python
"quickbooks": BusinessSystemConfig(
    system_name="quickbooks",
    mcp_server_name="quickbooks-online",
    agents=["qb_gl_agent", "qb_ar_agent"],
    tool_mapper_class="agents.quickbooks.qb_tool_mapper.QBToolMapper"
)
```

3. Create agents using ConfiguredAgent - they automatically get the right MCP server!

## Testing the Migration

```python
# Test configuration loading
from config.mcp_config import get_mcp_config

config = get_mcp_config()

# Validate configuration
validation = config.validate_configuration()
assert validation['valid'], f"Config issues: {validation['issues']}"

# Check agent mappings
assert config.get_mcp_server_for_agent("gl_agent") == "sage-intacct"

# Test runtime changes
config.update_server_endpoint("sage-intacct", "https://remote.example.com")
assert config.get_server_config("sage-intacct").connection_type == "hosted"
```

## Summary

The centralized configuration system provides:

1. ✅ **Single source of truth**: All MCP configuration in one place
2. ✅ **DRY principle**: No more hardcoded server names
3. ✅ **Runtime flexibility**: Change configurations without code changes
4. ✅ **Automatic propagation**: Changes affect all agents immediately
5. ✅ **Easy scaling**: Add new business systems with minimal effort

This architecture ensures that switching between local and remote MCP servers, or adding new business systems, requires changes in only ONE location!
