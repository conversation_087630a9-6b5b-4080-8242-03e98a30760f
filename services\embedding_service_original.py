"""
Embedding Service for Tool Descriptions

Generates semantic embeddings for tools using sentence-transformers.
"""

import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import numpy as np
import structlog
from sentence_transformers import SentenceTransformer
import hashlib
import json

logger = structlog.get_logger(__name__)


class EmbeddingCache:
    """Simple in-memory cache for embeddings."""
    
    def __init__(self, ttl_hours: int = 24, max_size: int = 10000):
        self.cache: Dict[str, tuple[np.ndarray, datetime]] = {}
        self.ttl = timedelta(hours=ttl_hours)
        self.max_size = max_size
        
    def get(self, key: str) -> Optional[np.ndarray]:
        """Get embedding from cache if valid."""
        if key in self.cache:
            embedding, timestamp = self.cache[key]
            if datetime.now() - timestamp < self.ttl:
                return embedding
            else:
                # Expired, remove it
                del self.cache[key]
        return None
    
    def put(self, key: str, embedding: np.ndarray):
        """Add embedding to cache."""
        # Evict oldest if at capacity
        if len(self.cache) >= self.max_size:
            oldest_key = min(self.cache.keys(), 
                           key=lambda k: self.cache[k][1])
            del self.cache[oldest_key]
        
        self.cache[key] = (embedding, datetime.now())
    
    def clear(self):
        """Clear the cache."""
        self.cache.clear()


class ToolEmbedder:
    """
    Generates embeddings for tool descriptions and metadata.
    
    Uses sentence-transformers for high-quality semantic embeddings.
    """
    
    def __init__(
        self,
        model_name: str = "sentence-transformers/all-mpnet-base-v2",
        cache_enabled: bool = True,
        cache_ttl_hours: int = 24,
        cache_max_size: int = 10000
    ):
        self.model_name = model_name
        self.model = None
        self.dimension = 768  # Default for all-mpnet-base-v2
        
        # Initialize cache
        self.cache = EmbeddingCache(cache_ttl_hours, cache_max_size) if cache_enabled else None
        
        logger.info(
            "ToolEmbedder initialized", 
            model=model_name,
            dimension=self.dimension,
            cache_enabled=cache_enabled
        )
    
    def _ensure_model_loaded(self):
        """Lazy load the model on first use."""
        if self.model is None:
            logger.info("Loading embedding model", model=self.model_name)
            self.model = SentenceTransformer(self.model_name)
            # Update dimension based on actual model
            self.dimension = self.model.get_sentence_embedding_dimension()
            logger.info("Model loaded", dimension=self.dimension)
    
    def _create_embedding_text(self, tool: Dict[str, Any]) -> str:
        """
        Create rich text representation of a tool for embedding.
        
        Combines name, description, parameters, and examples into
        a comprehensive text that captures the tool's semantics.
        """
        parts = []
        
        # Tool name (most important)
        name = tool.get("name", "")
        if name:
            # Convert snake_case to readable format
            readable_name = name.replace("_", " ").title()
            parts.append(f"Tool: {readable_name}")
            parts.append(f"Function: {name}")
        
        # Description (very important)
        description = tool.get("description", "")
        if description:
            parts.append(f"Description: {description}")
        
        # Category and capabilities
        category = tool.get("category")
        if category:
            parts.append(f"Category: {category}")
        
        capabilities = tool.get("capabilities", [])
        if capabilities:
            parts.append(f"Capabilities: {', '.join(capabilities)}")
        
        # Parameters (important for understanding usage)
        parameters = tool.get("parameters", {})
        if parameters:
            # Extract parameter names and descriptions
            param_info = []
            
            # Handle different parameter formats
            if isinstance(parameters, dict):
                # Check for JSON schema format
                props = parameters.get("properties", parameters)
                required = parameters.get("required", [])
                
                for param_name, param_spec in props.items():
                    param_desc = param_spec.get("description", "")
                    param_type = param_spec.get("type", "")
                    
                    param_str = param_name
                    if param_type:
                        param_str += f" ({param_type})"
                    if param_desc:
                        param_str += f": {param_desc}"
                    if param_name in required:
                        param_str += " [required]"
                    
                    param_info.append(param_str)
            
            if param_info:
                parts.append(f"Parameters: {'; '.join(param_info)}")
        
        # Examples (helpful for understanding)
        examples = tool.get("examples", [])
        if examples:
            example_texts = []
            for example in examples[:3]:  # Limit to 3 examples
                if isinstance(example, str):
                    example_texts.append(example)
                elif isinstance(example, dict):
                    # Format example as text
                    example_text = example.get("description", "")
                    if not example_text and "input" in example:
                        example_text = f"Input: {json.dumps(example['input'])}"
                    if example_text:
                        example_texts.append(example_text)
            
            if example_texts:
                parts.append(f"Examples: {'; '.join(example_texts)}")
        
        # Keywords
        keywords = tool.get("keywords", [])
        if keywords:
            parts.append(f"Keywords: {', '.join(keywords)}")
        
        # Server/module context
        server = tool.get("server", tool.get("server_name", ""))
        if server:
            parts.append(f"Server: {server}")
        
        # Combine all parts
        embedding_text = " | ".join(parts)
        
        return embedding_text
    
    def _compute_cache_key(self, text: str) -> str:
        """Compute cache key for embedding text."""
        return hashlib.sha256(text.encode()).hexdigest()
    
    async def encode(self, text: str) -> np.ndarray:
        """
        Encode text into an embedding vector.
        
        Args:
            text: Text to encode
            
        Returns:
            Embedding vector as numpy array
        """
        self._ensure_model_loaded()
        
        # Check cache
        if self.cache:
            cache_key = self._compute_cache_key(text)
            cached = self.cache.get(cache_key)
            if cached is not None:
                return cached
        
        # Generate embedding
        # Run in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        embedding = await loop.run_in_executor(
            None, 
            lambda: self.model.encode(text, convert_to_numpy=True)
        )
        
        # Ensure correct shape
        if len(embedding.shape) == 1:
            embedding = embedding.reshape(1, -1)[0]
        
        # Cache the result
        if self.cache:
            self.cache.put(cache_key, embedding)
        
        return embedding
    
    async def encode_batch(self, texts: List[str]) -> np.ndarray:
        """
        Encode multiple texts in a batch.
        
        Args:
            texts: List of texts to encode
            
        Returns:
            Array of embeddings
        """
        self._ensure_model_loaded()
        
        # Separate cached and uncached
        uncached_texts = []
        uncached_indices = []
        results = [None] * len(texts)
        
        if self.cache:
            for i, text in enumerate(texts):
                cache_key = self._compute_cache_key(text)
                cached = self.cache.get(cache_key)
                if cached is not None:
                    results[i] = cached
                else:
                    uncached_texts.append(text)
                    uncached_indices.append(i)
        else:
            uncached_texts = texts
            uncached_indices = list(range(len(texts)))
        
        # Encode uncached texts
        if uncached_texts:
            loop = asyncio.get_event_loop()
            embeddings = await loop.run_in_executor(
                None,
                lambda: self.model.encode(uncached_texts, convert_to_numpy=True)
            )
            
            # Store results and update cache
            for i, idx in enumerate(uncached_indices):
                results[idx] = embeddings[i]
                if self.cache:
                    cache_key = self._compute_cache_key(uncached_texts[i])
                    self.cache.put(cache_key, embeddings[i])
        
        return np.array(results)
    
    async def encode_tool(self, tool: Dict[str, Any]) -> np.ndarray:
        """
        Encode a tool's metadata into an embedding.
        
        Args:
            tool: Tool metadata dictionary
            
        Returns:
            Embedding vector
        """
        embedding_text = self._create_embedding_text(tool)
        return await self.encode(embedding_text)
    
    async def encode_tools(self, tools: List[Dict[str, Any]]) -> np.ndarray:
        """
        Encode multiple tools in a batch.
        
        Args:
            tools: List of tool metadata dictionaries
            
        Returns:
            Array of embeddings
        """
        texts = [self._create_embedding_text(tool) for tool in tools]
        return await self.encode_batch(texts)
    
    def clear_cache(self):
        """Clear the embedding cache."""
        if self.cache:
            self.cache.clear()
            logger.info("Embedding cache cleared")