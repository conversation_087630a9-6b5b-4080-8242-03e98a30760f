"""
Tool Composition Engine - Build complex workflows from individual tools

This module provides classes to compose tools into workflows with data flow,
state management, error recovery, and templates for common patterns.
"""

import asyncio
import json
import logging
from enum import Enum
from typing import Dict, Any, List, Optional, Union, Callable, Set
from datetime import datetime, timezone
from dataclasses import dataclass, field
from functools import wraps
import uuid

logger = logging.getLogger(__name__)


class StepStatus(Enum):
    """Status of a workflow step"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    RETRYING = "retrying"


@dataclass
class WorkflowStep:
    """Represents a single step in a workflow"""
    id: str
    tool_name: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    depends_on: List[str] = field(default_factory=list)
    retry_count: int = 3
    timeout: float = 30.0
    condition: Optional[Callable[[Dict[str, Any]], bool]] = None
    transform: Optional[Callable[[Any], Any]] = None
    error_handler: Optional[Callable[[Exception, Dict[str, Any]], Any]] = None
    description: str = ""
    
    def __post_init__(self):
        if not self.id:
            self.id = f"step_{uuid.uuid4().hex[:8]}"


@dataclass
class StepResult:
    """Result of executing a workflow step"""
    step_id: str
    status: StepStatus
    result: Any = None
    error: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    retry_attempts: int = 0
    
    @property
    def duration(self) -> Optional[float]:
        """Calculate step duration in seconds"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None


class ToolWorkflow:
    """
    Defines a workflow composed of multiple tool steps with dependencies
    """
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.steps: Dict[str, WorkflowStep] = {}
        self.state: Dict[str, Any] = {}
        self.results: Dict[str, StepResult] = {}
        self._execution_order: List[str] = []
        
    def add_step(self, step: WorkflowStep) -> 'ToolWorkflow':
        """Add a step to the workflow"""
        if step.id in self.steps:
            raise ValueError(f"Step with id '{step.id}' already exists")
        
        self.steps[step.id] = step
        self._update_execution_order()
        return self
    
    def remove_step(self, step_id: str) -> 'ToolWorkflow':
        """Remove a step from the workflow"""
        if step_id not in self.steps:
            raise ValueError(f"Step with id '{step_id}' not found")
        
        # Remove step and update dependencies
        del self.steps[step_id]
        for step in self.steps.values():
            if step_id in step.depends_on:
                step.depends_on.remove(step_id)
        
        self._update_execution_order()
        return self
    
    def _update_execution_order(self):
        """Update execution order based on dependencies using topological sort"""
        visited = set()
        temp_visited = set()
        order = []
        
        def visit(step_id: str):
            if step_id in temp_visited:
                raise ValueError(f"Circular dependency detected at step '{step_id}'")
            if step_id in visited:
                return
                
            temp_visited.add(step_id)
            step = self.steps.get(step_id)
            if step:
                for dep_id in step.depends_on:
                    if dep_id in self.steps:
                        visit(dep_id)
            temp_visited.remove(step_id)
            visited.add(step_id)
            order.append(step_id)
        
        for step_id in self.steps:
            if step_id not in visited:
                visit(step_id)
        
        self._execution_order = order
    
    def get_ready_steps(self) -> List[str]:
        """Get steps that are ready to execute (all dependencies met)"""
        ready = []
        for step_id in self._execution_order:
            step = self.steps[step_id]
            
            # Skip if already processed
            if step_id in self.results:
                continue
            
            # Check if all dependencies are completed
            deps_met = all(
                dep_id in self.results and 
                self.results[dep_id].status == StepStatus.COMPLETED
                for dep_id in step.depends_on
            )
            
            if deps_met:
                ready.append(step_id)
        
        return ready
    
    def get_state(self) -> Dict[str, Any]:
        """Get current workflow state including results"""
        return {
            'name': self.name,
            'description': self.description,
            'state': self.state,
            'results': {
                step_id: {
                    'status': result.status.value,
                    'result': result.result,
                    'error': result.error,
                    'duration': result.duration
                }
                for step_id, result in self.results.items()
            },
            'pending_steps': [
                step_id for step_id in self._execution_order
                if step_id not in self.results
            ]
        }
    
    def reset(self):
        """Reset workflow state"""
        self.state.clear()
        self.results.clear()


class ToolChain:
    """
    Sequential tool execution with data flow between steps
    """
    
    def __init__(self, name: str):
        self.name = name
        self.workflow = ToolWorkflow(name, f"Sequential chain: {name}")
        self._prev_step_id: Optional[str] = None
    
    def add(
        self,
        tool_name: str,
        parameters: Optional[Dict[str, Any]] = None,
        transform: Optional[Callable[[Any], Any]] = None,
        **kwargs
    ) -> 'ToolChain':
        """Add a tool to the chain"""
        step_id = f"{self.name}_{len(self.workflow.steps)}"
        
        # Set up dependencies for sequential execution
        depends_on = [self._prev_step_id] if self._prev_step_id else []
        
        step = WorkflowStep(
            id=step_id,
            tool_name=tool_name,
            parameters=parameters or {},
            depends_on=depends_on,
            transform=transform,
            **kwargs
        )
        
        self.workflow.add_step(step)
        self._prev_step_id = step_id
        
        return self
    
    def get_workflow(self) -> ToolWorkflow:
        """Get the underlying workflow"""
        return self.workflow


class DataTransformer:
    """
    Registry for data transformation functions between tools
    """
    
    def __init__(self):
        self._transformers: Dict[Tuple[str, str], Callable] = {}
    
    def register(
        self,
        from_type: str,
        to_type: str,
        transformer: Callable[[Any], Any]
    ):
        """Register a transformation function"""
        self._transformers[(from_type, to_type)] = transformer
    
    def transform(self, from_type: str, to_type: str):
        """Decorator to register a transformation"""
        def decorator(func):
            self.register(from_type, to_type, func)
            return func
        return decorator
    
    def get_transformer(
        self,
        from_type: str,
        to_type: str
    ) -> Optional[Callable[[Any], Any]]:
        """Get a transformation function"""
        return self._transformers.get((from_type, to_type))
    
    def can_transform(self, from_type: str, to_type: str) -> bool:
        """Check if a transformation exists"""
        return (from_type, to_type) in self._transformers


# Global transformer instance
data_transformer = DataTransformer()


# Common transformations
@data_transformer.transform("customer_list", "customer_ids")
def extract_customer_ids(customers: List[Dict[str, Any]]) -> List[str]:
    """Extract customer IDs from customer objects"""
    return [c.get("id", c.get("customerId", "")) for c in customers if c]


@data_transformer.transform("financial_data", "summary_report")
def summarize_financials(data: Dict[str, Any]) -> Dict[str, Any]:
    """Transform raw financial data to summary report"""
    return {
        "total_revenue": sum(float(item.get("amount", 0)) for item in data.get("revenue_items", [])),
        "total_expenses": sum(float(item.get("amount", 0)) for item in data.get("expense_items", [])),
        "net_income": data.get("net_income", 0),
        "period": data.get("period", ""),
        "generated_at": datetime.now(timezone.utc).isoformat()
    }


@data_transformer.transform("invoice_list", "invoice_ids")
def extract_invoice_ids(invoices: List[Dict[str, Any]]) -> List[str]:
    """Extract invoice IDs from invoice objects"""
    return [inv.get("id", inv.get("invoiceId", "")) for inv in invoices if inv]


@data_transformer.transform("vendor_list", "vendor_ids")
def extract_vendor_ids(vendors: List[Dict[str, Any]]) -> List[str]:
    """Extract vendor IDs from vendor objects"""
    return [v.get("id", v.get("vendorId", "")) for v in vendors if v]


class WorkflowTemplates:
    """
    Pre-built workflow templates for common business processes
    """
    
    @staticmethod
    def month_end_close() -> ToolWorkflow:
        """Month-end close workflow template"""
        workflow = ToolWorkflow("month_end_close", "Complete month-end closing process")
        
        # Step 1: Validate period status
        workflow.add_step(WorkflowStep(
            id="validate_period",
            tool_name="mcp__sage-intacct__execute_month_end_close",
            parameters={"dry_run": True},
            description="Validate period is ready for close"
        ))
        
        # Step 2: Run GL reconciliation
        workflow.add_step(WorkflowStep(
            id="gl_reconciliation",
            tool_name="mcp__sage-intacct__get_financial_summary",
            parameters={"include_modules": ["GL"]},
            depends_on=["validate_period"],
            description="Reconcile GL accounts"
        ))
        
        # Step 3: Run AR validation
        workflow.add_step(WorkflowStep(
            id="ar_validation",
            tool_name="mcp__sage-intacct__get_financial_summary",
            parameters={"include_modules": ["AR"]},
            depends_on=["validate_period"],
            description="Validate AR balances"
        ))
        
        # Step 4: Run AP validation
        workflow.add_step(WorkflowStep(
            id="ap_validation",
            tool_name="mcp__sage-intacct__get_financial_summary",
            parameters={"include_modules": ["AP"]},
            depends_on=["validate_period"],
            description="Validate AP balances"
        ))
        
        # Step 5: Generate consolidated report
        workflow.add_step(WorkflowStep(
            id="consolidated_report",
            tool_name="mcp__sage-intacct__generate_consolidated_report",
            parameters={"report_type": "month_end_summary"},
            depends_on=["gl_reconciliation", "ar_validation", "ap_validation"],
            description="Generate consolidated month-end report"
        ))
        
        # Step 6: Execute close (if all validations pass)
        workflow.add_step(WorkflowStep(
            id="execute_close",
            tool_name="mcp__sage-intacct__execute_month_end_close",
            parameters={"dry_run": False},
            depends_on=["consolidated_report"],
            condition=lambda state: all(
                state.get(f"{step}_result", {}).get("status") == "success"
                for step in ["gl_reconciliation", "ar_validation", "ap_validation"]
            ),
            description="Execute month-end close"
        ))
        
        return workflow
    
    @staticmethod
    def customer_onboarding(customer_name: str) -> ToolWorkflow:
        """Customer onboarding workflow template"""
        workflow = ToolWorkflow("customer_onboarding", f"Onboard new customer: {customer_name}")
        
        # Step 1: Search for existing customer
        workflow.add_step(WorkflowStep(
            id="search_customer",
            tool_name="mcp__sage-intacct__search_across_modules",
            parameters={"query": customer_name, "modules": ["AR"]},
            description="Check if customer already exists"
        ))
        
        # Step 2: Create customer (if not exists)
        workflow.add_step(WorkflowStep(
            id="create_customer",
            tool_name="mcp__sage-intacct__create_customer",  # Placeholder
            parameters={"name": customer_name},
            depends_on=["search_customer"],
            condition=lambda state: len(state.get("search_customer_result", {}).get("results", [])) == 0,
            description="Create new customer record"
        ))
        
        # Step 3: Set credit terms
        workflow.add_step(WorkflowStep(
            id="set_credit_terms",
            tool_name="mcp__sage-intacct__update_customer",  # Placeholder
            parameters={"credit_limit": 10000, "payment_terms": "Net30"},
            depends_on=["create_customer"],
            description="Configure credit terms"
        ))
        
        # Step 4: Generate welcome report
        workflow.add_step(WorkflowStep(
            id="welcome_report",
            tool_name="mcp__sage-intacct__generate_consolidated_report",
            parameters={"report_type": "customer_summary"},
            depends_on=["set_credit_terms"],
            description="Generate customer welcome package"
        ))
        
        return workflow
    
    @staticmethod
    def invoice_processing() -> ToolWorkflow:
        """Invoice processing workflow template"""
        workflow = ToolWorkflow("invoice_processing", "Process pending invoices")
        
        # Step 1: Get pending invoices
        workflow.add_step(WorkflowStep(
            id="get_pending",
            tool_name="mcp__sage-intacct__search_across_modules",
            parameters={"query": "status:pending", "modules": ["AR"]},
            description="Retrieve pending invoices"
        ))
        
        # Step 2: Validate each invoice
        workflow.add_step(WorkflowStep(
            id="validate_invoices",
            tool_name="mcp__sage-intacct__validate_invoices",  # Placeholder
            depends_on=["get_pending"],
            transform=lambda result: extract_invoice_ids(result.get("results", [])),
            description="Validate invoice data"
        ))
        
        # Step 3: Apply payments
        workflow.add_step(WorkflowStep(
            id="apply_payments",
            tool_name="mcp__sage-intacct__apply_payments",  # Placeholder
            depends_on=["validate_invoices"],
            description="Apply available payments"
        ))
        
        # Step 4: Generate processing report
        workflow.add_step(WorkflowStep(
            id="processing_report",
            tool_name="mcp__sage-intacct__generate_consolidated_report",
            parameters={"report_type": "invoice_processing"},
            depends_on=["apply_payments"],
            description="Generate processing summary"
        ))
        
        return workflow
    
    @staticmethod
    def financial_reporting(period: str) -> ToolWorkflow:
        """Financial reporting workflow template"""
        workflow = ToolWorkflow("financial_reporting", f"Generate financial reports for {period}")
        
        # Step 1: Gather GL data
        workflow.add_step(WorkflowStep(
            id="gl_data",
            tool_name="mcp__sage-intacct__get_financial_summary",
            parameters={"period": period, "include_modules": ["GL"]},
            description="Gather GL financial data"
        ))
        
        # Step 2: Gather AR data
        workflow.add_step(WorkflowStep(
            id="ar_data",
            tool_name="mcp__sage-intacct__get_financial_summary",
            parameters={"period": period, "include_modules": ["AR"]},
            description="Gather AR financial data"
        ))
        
        # Step 3: Gather AP data
        workflow.add_step(WorkflowStep(
            id="ap_data",
            tool_name="mcp__sage-intacct__get_financial_summary",
            parameters={"period": period, "include_modules": ["AP"]},
            description="Gather AP financial data"
        ))
        
        # Step 4: Generate P&L
        workflow.add_step(WorkflowStep(
            id="pl_statement",
            tool_name="mcp__sage-intacct__generate_consolidated_report",
            parameters={"report_type": "profit_loss", "period": period},
            depends_on=["gl_data"],
            description="Generate P&L statement"
        ))
        
        # Step 5: Generate Balance Sheet
        workflow.add_step(WorkflowStep(
            id="balance_sheet",
            tool_name="mcp__sage-intacct__generate_consolidated_report",
            parameters={"report_type": "balance_sheet", "period": period},
            depends_on=["gl_data"],
            description="Generate balance sheet"
        ))
        
        # Step 6: Generate Cash Flow
        workflow.add_step(WorkflowStep(
            id="cash_flow",
            tool_name="mcp__sage-intacct__generate_consolidated_report",
            parameters={"report_type": "cash_flow", "period": period},
            depends_on=["gl_data", "ar_data", "ap_data"],
            description="Generate cash flow statement"
        ))
        
        # Step 7: Executive Summary
        workflow.add_step(WorkflowStep(
            id="executive_summary",
            tool_name="mcp__sage-intacct__generate_consolidated_report",
            parameters={"report_type": "executive_summary", "period": period},
            depends_on=["pl_statement", "balance_sheet", "cash_flow"],
            description="Generate executive summary"
        ))
        
        return workflow


class WorkflowExecutor:
    """
    Executes workflows with state management and error recovery
    """
    
    def __init__(self, tool_executor: Optional[Any] = None):
        """
        Initialize workflow executor
        
        Args:
            tool_executor: Tool executor instance (e.g., FastAgent or ParallelToolExecutor)
        """
        self.tool_executor = tool_executor
        self._checkpoints: Dict[str, Dict[str, Any]] = {}
    
    async def execute(
        self,
        workflow: ToolWorkflow,
        context: Optional[Dict[str, Any]] = None,
        checkpoint_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Execute a workflow with error recovery and state management
        
        Args:
            workflow: The workflow to execute
            context: Initial context/parameters
            checkpoint_id: Resume from checkpoint if provided
            
        Returns:
            Workflow execution results
        """
        # Initialize or restore state
        if checkpoint_id and checkpoint_id in self._checkpoints:
            logger.info(f"Resuming workflow '{workflow.name}' from checkpoint")
            saved_state = self._checkpoints[checkpoint_id]
            workflow.state = saved_state['state']
            workflow.results = saved_state['results']
        else:
            workflow.reset()
            workflow.state.update(context or {})
        
        try:
            # Execute workflow steps
            while True:
                # Get ready steps
                ready_steps = workflow.get_ready_steps()
                if not ready_steps:
                    break
                
                # Execute ready steps (potentially in parallel)
                step_results = await self._execute_steps(
                    workflow,
                    ready_steps
                )
                
                # Update workflow results
                for step_id, result in step_results.items():
                    workflow.results[step_id] = result
                    
                    # Store result in state for downstream steps
                    if result.status == StepStatus.COMPLETED:
                        workflow.state[f"{step_id}_result"] = result.result
                
                # Save checkpoint after each batch
                if checkpoint_id:
                    self._save_checkpoint(checkpoint_id, workflow)
            
            # Clean up checkpoint on success
            if checkpoint_id and checkpoint_id in self._checkpoints:
                del self._checkpoints[checkpoint_id]
            
            return {
                'status': 'completed',
                'workflow': workflow.get_state(),
                'final_results': {
                    step_id: result.result
                    for step_id, result in workflow.results.items()
                    if result.status == StepStatus.COMPLETED
                }
            }
            
        except Exception as e:
            logger.error(f"Workflow execution failed: {str(e)}", exc_info=True)
            return {
                'status': 'failed',
                'error': str(e),
                'workflow': workflow.get_state()
            }
    
    async def _execute_steps(
        self,
        workflow: ToolWorkflow,
        step_ids: List[str]
    ) -> Dict[str, StepResult]:
        """Execute a batch of workflow steps"""
        results = {}
        
        # Execute steps (could be parallelized)
        tasks = []
        for step_id in step_ids:
            step = workflow.steps[step_id]
            task = self._execute_single_step(workflow, step)
            tasks.append((step_id, task))
        
        # Wait for all tasks
        for step_id, task in tasks:
            try:
                result = await task
                results[step_id] = result
            except Exception as e:
                logger.error(f"Step '{step_id}' failed: {str(e)}")
                results[step_id] = StepResult(
                    step_id=step_id,
                    status=StepStatus.FAILED,
                    error=str(e),
                    started_at=datetime.now(timezone.utc)
                )
        
        return results
    
    async def _execute_single_step(
        self,
        workflow: ToolWorkflow,
        step: WorkflowStep
    ) -> StepResult:
        """Execute a single workflow step with retries"""
        result = StepResult(
            step_id=step.id,
            status=StepStatus.PENDING,
            started_at=datetime.now(timezone.utc)
        )
        
        # Check condition
        if step.condition and not step.condition(workflow.state):
            result.status = StepStatus.SKIPPED
            result.completed_at = datetime.now(timezone.utc)
            logger.info(f"Skipping step '{step.id}' due to condition")
            return result
        
        # Resolve parameters with workflow state
        resolved_params = self._resolve_parameters(
            step.parameters,
            workflow.state
        )
        
        # Execute with retries
        for attempt in range(step.retry_count):
            try:
                result.status = StepStatus.RUNNING
                result.retry_attempts = attempt
                
                # Execute the tool
                if self.tool_executor:
                    tool_result = await self._execute_tool(
                        step.tool_name,
                        resolved_params,
                        step.timeout
                    )
                else:
                    # Simulate execution for testing
                    await asyncio.sleep(0.1)
                    tool_result = {"simulated": True, "step": step.id}
                
                # Apply transformation if defined
                if step.transform:
                    tool_result = step.transform(tool_result)
                
                result.result = tool_result
                result.status = StepStatus.COMPLETED
                result.completed_at = datetime.now(timezone.utc)
                
                logger.info(f"Step '{step.id}' completed successfully")
                return result
                
            except Exception as e:
                logger.warning(f"Step '{step.id}' attempt {attempt + 1} failed: {str(e)}")
                
                if attempt < step.retry_count - 1:
                    result.status = StepStatus.RETRYING
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                else:
                    # Final failure
                    if step.error_handler:
                        try:
                            result.result = step.error_handler(e, workflow.state)
                            result.status = StepStatus.COMPLETED
                        except Exception as handler_error:
                            result.error = str(handler_error)
                            result.status = StepStatus.FAILED
                    else:
                        result.error = str(e)
                        result.status = StepStatus.FAILED
                    
                    result.completed_at = datetime.now(timezone.utc)
                    return result
    
    def _resolve_parameters(
        self,
        parameters: Dict[str, Any],
        state: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Resolve parameter references from workflow state"""
        resolved = {}
        
        for key, value in parameters.items():
            if isinstance(value, str) and value.startswith("{{") and value.endswith("}}"):
                # Extract reference
                ref = value[2:-2].strip()
                # Look up in state
                resolved[key] = state.get(ref, value)
            elif isinstance(value, dict):
                # Recursively resolve nested parameters
                resolved[key] = self._resolve_parameters(value, state)
            else:
                resolved[key] = value
        
        return resolved
    
    async def _execute_tool(
        self,
        tool_name: str,
        parameters: Dict[str, Any],
        timeout: float
    ) -> Any:
        """Execute a tool using the configured executor"""
        if not self.tool_executor:
            raise ValueError("No tool executor configured for workflow execution")
        
        try:
            # Use asyncio timeout for the tool execution
            async with asyncio.timeout(timeout):
                result = await self.tool_executor(tool_name, parameters)
                return result
        except asyncio.TimeoutError:
            raise TimeoutError(f"Tool '{tool_name}' execution timed out after {timeout} seconds")
    
    def _save_checkpoint(self, checkpoint_id: str, workflow: ToolWorkflow):
        """Save workflow state as checkpoint"""
        self._checkpoints[checkpoint_id] = {
            'state': workflow.state.copy(),
            'results': workflow.results.copy(),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    
    def list_checkpoints(self) -> List[Dict[str, Any]]:
        """List available checkpoints"""
        return [
            {
                'id': checkpoint_id,
                'timestamp': checkpoint['timestamp']
            }
            for checkpoint_id, checkpoint in self._checkpoints.items()
        ]