# AI Workspace Agents Documentation

Welcome to the AI Workspace Agents documentation. This directory contains all project documentation organized by topic.

## 📁 Documentation Structure

### [/business-systems](./business-systems/)
Documentation for working with multiple business systems and MCP servers.
- Multi-MCP architecture overview
- Adding new business systems guide
- Quick reference for developers

### [/PRD](./PRD/)
Product Requirements Documents for the project.
- Agent decoupling architecture PRD
- Task documents and specifications

## 🚀 Quick Links

- **Adding a new business system?** → [Business Systems Guide](./business-systems/adding-new-business-system-guide.md)
- **Architecture overview?** → [Multi-MCP Architecture](./business-systems/multi-mcp-architecture.md)
- **Project requirements?** → [PRD Directory](./PRD/)

## 📚 Key Documents

1. **Architecture & Migration**
   - [Direct MCP Client Architecture](./direct-mcp-client-architecture.md) - Current architecture overview
   - [FastAgent to MCP Migration Guide](./fastagent-to-mcp-migration-guide.md) - Step-by-step migration instructions

2. **For Developers**
   - [Adding New Business System - Complete Guide](./business-systems/adding-new-business-system-guide.md)
   - [Quick Reference - New Business System](./business-systems/quick-reference-new-business-system.md)
   - [Multi-MCP Architecture](./business-systems/multi-mcp-architecture.md)

3. **For Project Management**
   - [Agent Decoupling Architecture PRD](./PRD/agent-decoupling-architecture-prd.md)
   - [Task Document](./PRD/agent-decoupling-architecture-task-document.md)

## 🎯 Getting Started

1. **New to the project?** Start with the PRD documents
2. **Ready to code?** Check the business systems documentation
3. **Need examples?** Look at the Intacct implementation in `agents/intacct/`

---
*For the latest updates, check the Progress.md file in the project root.*
