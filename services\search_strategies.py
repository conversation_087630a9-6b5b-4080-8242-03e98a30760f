"""
Search Strategy Configuration and Management

Provides configurable multi-stage search strategies for the Intelligent Tool Filter.
Each strategy defines parameters for different stages of progressive search.
"""

from typing import List, Dict, Any, Optional, NamedTuple
from dataclasses import dataclass, field
from enum import Enum
import structlog

logger = structlog.get_logger(__name__)


class SearchStage(Enum):
    """Different stages of progressive search."""
    HIGH_PRECISION = "high_precision"
    RELAXED_PRECISION = "relaxed_precision"
    CATEGORY_BASED = "category_based"
    CONTEXTUAL = "contextual"
    BROAD_MATCH = "broad_match"


@dataclass
class SearchStageConfig:
    """Configuration for a single search stage."""
    stage: SearchStage
    threshold: float
    k: int  # Number of results to retrieve
    min_results: int  # Minimum results before moving to next stage
    weight_modifier: float = 1.0  # Modifier for similarity scores
    enabled: bool = True
    
    def __post_init__(self):
        """Validate configuration."""
        if not 0 <= self.threshold <= 1:
            raise ValueError(f"Threshold must be between 0 and 1, got {self.threshold}")
        if self.k <= 0:
            raise ValueError(f"k must be positive, got {self.k}")
        if self.min_results < 0:
            raise ValueError(f"min_results must be non-negative, got {self.min_results}")
        if self.weight_modifier <= 0:
            raise ValueError(f"weight_modifier must be positive, got {self.weight_modifier}")


@dataclass
class SearchStrategyProfile:
    """Complete search strategy profile with multiple stages."""
    name: str
    description: str
    stages: List[SearchStageConfig] = field(default_factory=list)
    max_total_results: int = 50  # Max results across all stages
    enable_dynamic_adjustment: bool = True
    
    def get_enabled_stages(self) -> List[SearchStageConfig]:
        """Get only enabled stages in order."""
        return [stage for stage in self.stages if stage.enabled]


class SearchStrategyManager:
    """Manages different search strategy profiles."""
    
    def __init__(self):
        self.profiles: Dict[str, SearchStrategyProfile] = {}
        self._init_default_profiles()
        self.active_profile = "balanced"
        
    def _init_default_profiles(self):
        """Initialize default search strategy profiles."""
        
        # Balanced profile - good for most use cases (aims for ~20 tools)
        self.profiles["balanced"] = SearchStrategyProfile(
            name="balanced",
            description="Balanced search strategy for providing ~20 relevant tools",
            stages=[
                SearchStageConfig(
                    stage=SearchStage.HIGH_PRECISION,
                    threshold=0.45,  # More generous to get relevant tools
                    k=30,           # Higher k to get more candidates
                    min_results=5   # Lower min to move to next stage faster
                ),
                SearchStageConfig(
                    stage=SearchStage.RELAXED_PRECISION,
                    threshold=0.25,  # Much lower threshold for broader selection
                    k=40,
                    min_results=3,
                    weight_modifier=0.9
                ),
                SearchStageConfig(
                    stage=SearchStage.CATEGORY_BASED,
                    threshold=0.15,  # Very low threshold to include category matches
                    k=30,
                    min_results=2,
                    weight_modifier=0.8
                ),
                SearchStageConfig(
                    stage=SearchStage.CONTEXTUAL,
                    threshold=0.1,   # Minimal threshold for context-based matching
                    k=20,
                    min_results=0,
                    weight_modifier=0.7
                )
            ],
            max_total_results=25  # Allow up to 25 tools, will be trimmed to 20
        )
        
        # Precise profile - prioritizes accuracy
        self.profiles["precise"] = SearchStrategyProfile(
            name="precise",
            description="High precision search with minimal fallback",
            stages=[
                SearchStageConfig(
                    stage=SearchStage.HIGH_PRECISION,
                    threshold=0.85,
                    k=15,
                    min_results=8
                ),
                SearchStageConfig(
                    stage=SearchStage.RELAXED_PRECISION,
                    threshold=0.75,
                    k=20,
                    min_results=5,
                    weight_modifier=0.85
                ),
                SearchStageConfig(
                    stage=SearchStage.CATEGORY_BASED,
                    threshold=0.65,
                    k=10,
                    min_results=0,
                    weight_modifier=0.75,
                    enabled=False  # Disabled for precise mode
                )
            ],
            enable_dynamic_adjustment=False
        )
        
        # Broad profile - ensures comprehensive results
        self.profiles["broad"] = SearchStrategyProfile(
            name="broad",
            description="Comprehensive search with multiple fallbacks",
            stages=[
                SearchStageConfig(
                    stage=SearchStage.HIGH_PRECISION,
                    threshold=0.75,
                    k=25,
                    min_results=15
                ),
                SearchStageConfig(
                    stage=SearchStage.RELAXED_PRECISION,
                    threshold=0.5,
                    k=40,
                    min_results=10,
                    weight_modifier=0.95
                ),
                SearchStageConfig(
                    stage=SearchStage.CATEGORY_BASED,
                    threshold=0.4,
                    k=20,
                    min_results=5,
                    weight_modifier=0.85
                ),
                SearchStageConfig(
                    stage=SearchStage.CONTEXTUAL,
                    threshold=0.3,
                    k=15,
                    min_results=5,
                    weight_modifier=0.75
                ),
                SearchStageConfig(
                    stage=SearchStage.BROAD_MATCH,
                    threshold=0.2,
                    k=10,
                    min_results=0,
                    weight_modifier=0.6
                )
            ],
            max_total_results=80
        )
        
        # Fast profile - optimized for speed
        self.profiles["fast"] = SearchStrategyProfile(
            name="fast",
            description="Fast search with minimal stages",
            stages=[
                SearchStageConfig(
                    stage=SearchStage.HIGH_PRECISION,
                    threshold=0.7,
                    k=30,
                    min_results=5
                ),
                SearchStageConfig(
                    stage=SearchStage.CONTEXTUAL,
                    threshold=0.4,
                    k=20,
                    min_results=0,
                    weight_modifier=0.8
                )
            ],
            max_total_results=40,
            enable_dynamic_adjustment=False
        )
        
    def get_profile(self, name: Optional[str] = None) -> SearchStrategyProfile:
        """Get a search strategy profile by name."""
        profile_name = name or self.active_profile
        if profile_name not in self.profiles:
            logger.warning(
                f"Unknown profile {profile_name}, using default",
                requested=profile_name,
                default=self.active_profile
            )
            profile_name = self.active_profile
        return self.profiles[profile_name]
    
    def set_active_profile(self, name: str):
        """Set the active search strategy profile."""
        if name not in self.profiles:
            raise ValueError(f"Unknown profile: {name}")
        self.active_profile = name
        logger.info(f"Active search profile set to: {name}")
    
    def add_custom_profile(self, profile: SearchStrategyProfile):
        """Add a custom search strategy profile."""
        self.profiles[profile.name] = profile
        logger.info(f"Added custom search profile: {profile.name}")
    
    def adjust_thresholds(self, profile_name: str, adjustments: Dict[SearchStage, float]):
        """Dynamically adjust thresholds for a profile."""
        profile = self.get_profile(profile_name)
        if not profile.enable_dynamic_adjustment:
            logger.warning(f"Dynamic adjustment disabled for profile: {profile_name}")
            return
            
        for stage_config in profile.stages:
            if stage_config.stage in adjustments:
                old_threshold = stage_config.threshold
                adjustment = adjustments[stage_config.stage]
                new_threshold = max(0.1, min(0.95, old_threshold + adjustment))
                stage_config.threshold = new_threshold
                
                logger.info(
                    f"Adjusted threshold for {stage_config.stage.value}",
                    profile=profile_name,
                    old=old_threshold,
                    new=new_threshold,
                    adjustment=adjustment
                )


@dataclass
class SearchMetrics:
    """Metrics for a search operation."""
    total_stages_executed: int = 0
    results_per_stage: Dict[SearchStage, int] = field(default_factory=dict)
    time_per_stage: Dict[SearchStage, float] = field(default_factory=dict)
    total_results: int = 0
    total_time: float = 0.0
    profile_used: str = ""
    dynamic_adjustments: Dict[SearchStage, float] = field(default_factory=dict)
    
    def add_stage_result(self, stage: SearchStage, result_count: int, time_taken: float):
        """Add results for a stage."""
        self.results_per_stage[stage] = result_count
        self.time_per_stage[stage] = time_taken
        self.total_stages_executed += 1
        self.total_time += time_taken
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of the search metrics."""
        return {
            "profile": self.profile_used,
            "total_stages": self.total_stages_executed,
            "total_results": self.total_results,
            "total_time_ms": int(self.total_time * 1000),
            "stages": [
                {
                    "stage": stage.value,
                    "results": self.results_per_stage.get(stage, 0),
                    "time_ms": int(self.time_per_stage.get(stage, 0) * 1000)
                }
                for stage in self.results_per_stage
            ],
            "adjustments": {
                stage.value: adj 
                for stage, adj in self.dynamic_adjustments.items()
            } if self.dynamic_adjustments else None
        }