"""
Intent detection module for the orchestrator

Handles analysis of user messages to detect intent, confidence scoring,
and pattern matching for financial operations.
"""
import re
import logging
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


class IntentDetector:
    """Detects user intent from messages for financial operations"""
    
    def __init__(self, confidence_scorer=None):
        self.confidence_scorer = confidence_scorer
        self._initialize_patterns()
    
    def _initialize_patterns(self):
        """Initialize intent detection patterns"""
        self.intent_patterns = {
            # GL Intents
            'gl_balance': {
                'keywords': ['balance', 'account', 'gl'],
                'patterns': ['what.*balance', 'show.*account', 'gl.*balance'],
                'confidence_boost': 0.1
            },
            'journal_entry': {
                'keywords': ['journal', 'entry', 'debit', 'credit', 'je'],
                'patterns': ['create.*journal', 'post.*entry', 'journal.*entry'],
                'confidence_boost': 0.2
            },
            'financial_statements': {
                'keywords': ['financial statement', 'p&l', 'balance sheet', 'cash flow', 'income statement'],
                'patterns': ['generate.*statement', 'show.*p&l', 'balance.*sheet'],
                'confidence_boost': 0.15
            },
            
            # AR Intents
            'customer_balance': {
                'keywords': ['customer', 'balance', 'ar', 'receivable'],
                'patterns': ['customer.*balance', 'ar.*balance', 'receivable.*amount'],
                'confidence_boost': 0.1
            },
            'create_invoice': {
                'keywords': ['invoice', 'create', 'issue', 'bill'],
                'patterns': ['create.*invoice', 'new.*invoice', 'issue.*invoice'],
                'confidence_boost': 0.2
            },
            
            # AP Intents
            'vendor_balance': {
                'keywords': ['vendor', 'balance', 'ap', 'payable'],
                'patterns': ['vendor.*balance', 'ap.*balance', 'payable.*amount'],
                'confidence_boost': 0.1
            },
            'payment_batch': {
                'keywords': ['payment batch', 'payment run', 'pay vendors', 'batch payment'],
                'patterns': ['create.*payment.*batch', 'payment.*run', 'pay.*vendor'],
                'confidence_boost': 0.25
            },
            
            # Analysis Intents
            'variance_analysis': {
                'keywords': ['variance', 'budget', 'actual', 'comparison'],
                'patterns': ['variance.*analysis', 'budget.*actual', 'compare.*budget'],
                'confidence_boost': 0.15
            },
            'ratio_calculation': {
                'keywords': ['ratio', 'liquidity', 'leverage', 'margin', 'profitability'],
                'patterns': ['financial.*ratio', 'calculate.*ratio', 'liquidity.*ratio'],
                'confidence_boost': 0.15
            },
            'trend_analysis': {
                'keywords': ['trend', 'pattern', 'over time', 'historical', 'growth'],
                'patterns': ['trend.*analysis', 'historical.*pattern', 'growth.*rate'],
                'confidence_boost': 0.15
            },
            'comprehensive_analysis': {
                'keywords': ['comprehensive', 'full analysis', 'complete review', 'financial analysis', 'financial review'],
                'patterns': ['comprehensive.*analysis', 'full.*review', 'complete.*analysis'],
                'confidence_boost': 0.2
            },
            
            # Report Intents
            'executive_summary': {
                'keywords': ['executive', 'summary', 'overview', 'high-level'],
                'patterns': ['executive.*summary', 'management.*report', 'high.*level.*summary'],
                'confidence_boost': 0.15
            },
            'financial_report': {
                'keywords': ['report', 'financial report', 'statement', 'dashboard'],
                'patterns': ['generate.*report', 'create.*report', 'financial.*report'],
                'confidence_boost': 0.1
            },
            'month_end_report': {
                'keywords': ['month-end report', 'period report', 'close report'],
                'patterns': ['month.*end.*report', 'close.*report', 'period.*report'],
                'confidence_boost': 0.2
            },
            
            # Validation Intents
            'period_validation': {
                'keywords': ['period', 'status', 'open', 'closed', 'locked'],
                'patterns': ['period.*status', 'is.*period.*open', 'check.*period'],
                'confidence_boost': 0.15
            },
            'data_integrity': {
                'keywords': ['integrity', 'data quality', 'consistency', 'reconciliation'],
                'patterns': ['data.*integrity', 'check.*consistency', 'validate.*data'],
                'confidence_boost': 0.15
            },
            'balance_validation': {
                'keywords': ['balance', 'validate', 'verify', 'check balance'],
                'patterns': ['validate.*balance', 'check.*balance', 'verify.*balance'],
                'confidence_boost': 0.15
            },
            
            # Complex Workflows
            'month_end_close': {
                'keywords': ['month-end', 'close', 'period close', 'close the books'],
                'patterns': ['month.*end.*close', 'close.*period', 'close.*books'],
                'confidence_boost': 0.3
            }
        }
        
        # Intent to capability mapping
        self.intent_to_capabilities = {
            'gl_balance': ['read', 'query'],
            'journal_entry': ['create', 'write'],
            'financial_statements': ['read', 'report'],
            'customer_balance': ['read', 'query'],
            'create_invoice': ['create', 'write'],
            'vendor_balance': ['read', 'query'],
            'payment_batch': ['create', 'process'],
            'variance_analysis': ['analyze', 'compare'],
            'ratio_calculation': ['analyze', 'calculate'],
            'trend_analysis': ['analyze', 'historical'],
            'comprehensive_analysis': ['analyze', 'report'],
            'executive_summary': ['report', 'summarize'],
            'financial_report': ['report', 'generate'],
            'month_end_report': ['report', 'process'],
            'period_validation': ['validate', 'check'],
            'data_integrity': ['validate', 'check'],
            'balance_validation': ['validate', 'verify'],
            'month_end_close': ['process', 'validate', 'report']
        }
        
        # Intent to category mapping
        self.intent_to_categories = {
            'gl_balance': ['general_ledger'],
            'journal_entry': ['general_ledger'],
            'financial_statements': ['general_ledger', 'reporting'],
            'customer_balance': ['accounts_receivable'],
            'create_invoice': ['accounts_receivable'],
            'vendor_balance': ['accounts_payable'],
            'payment_batch': ['accounts_payable'],
            'variance_analysis': ['analysis'],
            'ratio_calculation': ['analysis'],
            'trend_analysis': ['analysis'],
            'comprehensive_analysis': ['analysis', 'reporting'],
            'executive_summary': ['reporting'],
            'financial_report': ['reporting'],
            'month_end_report': ['reporting', 'validation'],
            'period_validation': ['validation'],
            'data_integrity': ['validation'],
            'balance_validation': ['validation'],
            'month_end_close': ['cross_module', 'validation']
        }
    
    async def detect_intent(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analyze user message to detect intent and confidence
        
        Returns:
            Dict with intent details, confidence score, and scoring details
        """
        message_lower = message.lower()
        detected_intents = []
        
        for intent_name, intent_config in self.intent_patterns.items():
            score = 0.0
            
            # Check keywords (40% weight)
            keyword_matches = sum(1 for kw in intent_config['keywords'] if kw in message_lower)
            if keyword_matches > 0:
                score += 0.4 * (keyword_matches / len(intent_config['keywords']))
            
            # Check patterns (30% weight)
            pattern_matches = sum(1 for pattern in intent_config['patterns'] 
                                if re.search(pattern, message_lower))
            if pattern_matches > 0:
                score += 0.3 * (pattern_matches / len(intent_config['patterns']))
            
            # Add confidence boost for specific intents
            score += intent_config.get('confidence_boost', 0)
            
            if score > 0:
                detected_intents.append({
                    'intent': intent_name,
                    'confidence': min(score, 1.0)
                })
        
        # Sort by confidence
        detected_intents.sort(key=lambda x: x['confidence'], reverse=True)
        
        # Use enhanced confidence scoring if available
        if detected_intents and self.confidence_scorer:
            primary_intent = detected_intents[0]
            
            # Calculate enhanced confidence score
            context = context or {}
            enhanced_confidence, scoring_details = self.confidence_scorer.calculate_confidence(
                primary_intent['intent'],
                message,
                context,
                detected_intents
            )
            
            return {
                'primary_intent': primary_intent['intent'],
                'confidence': enhanced_confidence,
                'all_intents': detected_intents,
                'scoring_details': scoring_details,
                'confidence_action': self.confidence_scorer.get_confidence_action(enhanced_confidence)
            }
        elif detected_intents:
            # Basic scoring without confidence scorer
            primary_intent = detected_intents[0]
            return {
                'primary_intent': primary_intent['intent'],
                'confidence': primary_intent['confidence'],
                'all_intents': detected_intents,
                'scoring_details': {},
                'confidence_action': {'action': 'execute', 'threshold': 0.7}
            }
        else:
            # No intent detected
            default_confidence = 0.3
            return {
                'primary_intent': 'general_query',
                'confidence': default_confidence,
                'all_intents': [],
                'scoring_details': {},
                'confidence_action': {'action': 'clarify', 'threshold': 0.5}
            }
    
    def get_capabilities_for_intent(self, intent: str) -> List[str]:
        """Get required capabilities for an intent"""
        return self.intent_to_capabilities.get(intent, ['read'])
    
    def get_categories_for_intent(self, intent: str) -> List[str]:
        """Get categories associated with an intent"""
        return self.intent_to_categories.get(intent, [])