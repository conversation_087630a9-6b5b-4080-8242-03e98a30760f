"""
Test Phase 5: Response Processing Integration

Simple integration test without pytest dependency
"""
import asyncio
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.response_processor import ResponseProcessor
from services.llm_service import LLMService


async def test_phase5():
    """Test Phase 5 response processing functionality"""
    print("\n=== Testing Phase 5: Response Processing ===\n")
    
    # Test 1: Response Processor
    print("1. Testing Response Processor...")
    processor = ResponseProcessor()
    
    # Test tool result formatting
    tool_results = [
        {
            "tool": "mcp__sage-intacct__get_financial_summary",
            "success": True,
            "result": {
                "content": {
                    "revenue": 1000000,
                    "expenses": 800000,
                    "profit": 200000
                }
            }
        }
    ]
    
    formatted = processor._format_tool_results(tool_results)
    assert len(formatted) == 1
    assert formatted[0]["success"] == True
    assert "revenue" in formatted[0]["data"]
    print("✓ Tool result formatting works")
    
    # Test response type determination
    response_type = processor._determine_response_type(tool_results)
    assert response_type == "data"
    print("✓ Response type detection works")
    
    # Test 2: LLM Service
    print("\n2. Testing LLM Service...")
    llm = LLMService()
    
    # Test pattern matching fallback
    mock_tools = {
        "health": type('Tool', (), {
            'name': 'health_check',
            'server_name': 'sage-intacct',
            'description': 'Check system health',
            'parameters': {}
        })(),
        "summary": type('Tool', (), {
            'name': 'get_financial_summary',
            'server_name': 'sage-intacct',
            'description': 'Get financial summary',
            'parameters': {}
        })()
    }
    
    # Test health check pattern
    analysis = llm._generate_fallback_analysis(
        "Check the system health",
        mock_tools
    )
    assert len(analysis["tool_calls"]) > 0
    assert "health_check" in analysis["tool_calls"][0]["tool"]
    print("✓ Health check pattern matching works")
    
    # Test financial query pattern
    analysis = llm._generate_fallback_analysis(
        "What are my YTD sales?",
        mock_tools
    )
    assert len(analysis["tool_calls"]) > 0
    assert "financial_summary" in analysis["tool_calls"][0]["tool"]
    print("✓ Financial query pattern matching works")
    
    # Test 3: Tool parameter extraction
    print("\n3. Testing parameter extraction...")
    
    # Test tool formatting for prompt
    formatted_tools = llm._format_tools_for_prompt(mock_tools)
    assert "health_check" in formatted_tools
    assert "financial_summary" in formatted_tools
    print("✓ Tool formatting works")
    
    # Test parameter simplification
    params = {
        "properties": {
            "query": {"type": "string", "description": "Search query"},
            "limit": {"type": "integer", "description": "Max results"}
        },
        "required": ["query"]
    }
    simplified = llm._simplify_parameters(params)
    assert "query" in simplified
    assert "(required)" in simplified["query"]
    print("✓ Parameter simplification works")
    
    # Test 4: Response formatting
    print("\n4. Testing response formatting...")
    
    # Test data formatting for LLM
    results = [
        {
            "success": True,
            "tool": "mcp__sage-intacct__list_enabled_modules",
            "data": ["GL", "AR", "AP", "CM"]
        }
    ]
    formatted_text = processor._format_results_for_llm(results)
    assert "sage-intacct - list_enabled_modules" in formatted_text
    assert "4 items" in formatted_text
    print("✓ Result formatting for LLM works")
    
    # Test 5: Error handling
    print("\n5. Testing error handling...")
    
    error_results = [
        {
            "tool": "mcp__sage-intacct__execute_month_end_close",
            "success": False,
            "error": "Period already closed"
        }
    ]
    
    response = await processor._handle_error_response(
        "Close the month",
        error_results
    )
    assert response["type"] == "error"
    assert "Period already closed" in response["response"]
    print("✓ Error handling works")
    
    print("\n✅ All Phase 5 tests passed!")
    print("\nPhase 5 Response Processing is complete and functional.")
    print("- Tool result handling ✓")
    print("- LLM response generation ✓")
    print("- Pattern matching fallback ✓")
    print("- Error handling ✓")


if __name__ == "__main__":
    asyncio.run(test_phase5())