"""
Tests for MCP Server Management API Endpoints
"""

import pytest
from httpx import AsyncClient
from unittest.mock import AsyncMock, Mo<PERSON>, patch
from datetime import datetime

from services.api_service import app
from config.dynamic_mcp_config import DynamicMCPServerConfig


@pytest.fixture
async def async_client():
    """Create async test client"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


class TestMCPServerEndpoints:
    """Test MCP server management endpoints"""
    
    @pytest.mark.asyncio
    async def test_add_mcp_server(self, async_client):
        """Test adding a new MCP server"""
        with patch('config.dynamic_mcp_config.get_dynamic_mcp_config') as mock_get_config:
            # Mock config
            mock_config = Mock()
            mock_config.add_server = AsyncMock(return_value=DynamicMCPServerConfig(
                name="test-mcp",
                command="python",
                args=["-m", "test_mcp"],
                env={"API_KEY": "test123"},
                connection_type="local",
                tags=["test"],
                source="api",
                added_at=datetime.now()
            ))
            mock_get_config.return_value = mock_config
            
            # Test request
            response = await async_client.post(
                "/mcp/servers",
                json={
                    "name": "test-mcp",
                    "command": "python",
                    "args": ["-m", "test_mcp"],
                    "env": {"API_KEY": "test123"},
                    "tags": ["test"]
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["name"] == "test-mcp"
            assert data["connection_type"] == "local"
            assert data["tags"] == ["test"]
            assert data["source"] == "api"
            
    @pytest.mark.asyncio
    async def test_add_duplicate_server(self, async_client):
        """Test adding a duplicate server returns 400"""
        with patch('config.dynamic_mcp_config.get_dynamic_mcp_config') as mock_get_config:
            # Mock config to raise ValueError
            mock_config = Mock()
            mock_config.add_server = AsyncMock(side_effect=ValueError("Server already exists"))
            mock_get_config.return_value = mock_config
            
            response = await async_client.post(
                "/mcp/servers",
                json={
                    "name": "duplicate",
                    "command": "python"
                }
            )
            
            assert response.status_code == 400
            assert "already exists" in response.json()["detail"]
            
    @pytest.mark.asyncio
    async def test_remove_mcp_server(self, async_client):
        """Test removing an MCP server"""
        with patch('config.dynamic_mcp_config.get_dynamic_mcp_config') as mock_get_config:
            # Mock config
            mock_config = Mock()
            mock_config.remove_server = AsyncMock(return_value=True)
            mock_get_config.return_value = mock_config
            
            response = await async_client.delete("/mcp/servers/test-mcp")
            
            assert response.status_code == 200
            assert response.json()["status"] == "success"
            
    @pytest.mark.asyncio
    async def test_remove_nonexistent_server(self, async_client):
        """Test removing non-existent server returns 404"""
        with patch('config.dynamic_mcp_config.get_dynamic_mcp_config') as mock_get_config:
            # Mock config
            mock_config = Mock()
            mock_config.remove_server = AsyncMock(return_value=False)
            mock_get_config.return_value = mock_config
            
            response = await async_client.delete("/mcp/servers/nonexistent")
            
            assert response.status_code == 404
            assert "not found" in response.json()["detail"]
            
    @pytest.mark.asyncio
    async def test_update_mcp_server(self, async_client):
        """Test updating an MCP server configuration"""
        with patch('config.dynamic_mcp_config.get_dynamic_mcp_config') as mock_get_config:
            # Mock config
            mock_config = Mock()
            mock_config.update_server = AsyncMock(return_value=DynamicMCPServerConfig(
                name="test-mcp",
                command="node",
                connection_type="hosted",
                endpoint="https://mcp.example.com",
                tags=["updated"],
                source="api",
                added_at=datetime.now()
            ))
            mock_get_config.return_value = mock_config
            
            response = await async_client.patch(
                "/mcp/servers/test-mcp",
                json={
                    "command": "node",
                    "connection_type": "hosted",
                    "endpoint": "https://mcp.example.com",
                    "tags": ["updated"]
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["command"] == "node"
            assert data["connection_type"] == "hosted"
            assert data["endpoint"] == "https://mcp.example.com"
            
    @pytest.mark.asyncio
    async def test_update_server_auth(self, async_client):
        """Test updating server authentication"""
        with patch('config.dynamic_mcp_config.get_dynamic_mcp_config') as mock_get_config:
            # Mock config
            mock_config = Mock()
            mock_config.update_server_auth = AsyncMock()
            mock_get_config.return_value = mock_config
            
            response = await async_client.post(
                "/mcp/servers/test-mcp/auth",
                json={
                    "auth_env": {
                        "API_KEY": "new_secret",
                        "TOKEN": "bearer_token"
                    },
                    "validate": True
                }
            )
            
            assert response.status_code == 200
            assert response.json()["status"] == "success"
            
            # Verify method was called with correct params
            mock_config.update_server_auth.assert_called_once_with(
                "test-mcp",
                auth_env={"API_KEY": "new_secret", "TOKEN": "bearer_token"},
                validate=True
            )
            
    @pytest.mark.asyncio
    async def test_list_mcp_servers(self, async_client):
        """Test listing MCP servers"""
        with patch('config.dynamic_mcp_config.get_dynamic_mcp_config') as mock_get_config:
            # Mock config
            mock_config = Mock()
            mock_config.list_servers = Mock(return_value=[
                {
                    "name": "server1",
                    "connection_type": "local",
                    "endpoint": None,
                    "health_check_enabled": True,
                    "tags": ["production"],
                    "source": "config",
                    "added_at": datetime.now()
                },
                {
                    "name": "server2",
                    "connection_type": "hosted",
                    "endpoint": "https://example.com",
                    "health_check_enabled": True,
                    "tags": ["test"],
                    "source": "api",
                    "added_at": datetime.now()
                }
            ])
            mock_get_config.return_value = mock_config
            
            response = await async_client.get("/mcp/servers")
            
            assert response.status_code == 200
            data = response.json()
            assert data["total"] == 2
            assert len(data["servers"]) == 2
            assert data["servers"][0]["name"] == "server1"
            assert data["servers"][1]["name"] == "server2"
            
    @pytest.mark.asyncio
    async def test_list_servers_with_tags(self, async_client):
        """Test listing servers filtered by tags"""
        with patch('config.dynamic_mcp_config.get_dynamic_mcp_config') as mock_get_config:
            # Mock config
            mock_config = Mock()
            mock_config.list_servers = Mock(return_value=[
                {
                    "name": "server1",
                    "connection_type": "local",
                    "endpoint": None,
                    "health_check_enabled": True,
                    "tags": ["production"],
                    "source": "config",
                    "added_at": None
                }
            ])
            mock_get_config.return_value = mock_config
            
            response = await async_client.get("/mcp/servers?tags=production,api")
            
            assert response.status_code == 200
            mock_config.list_servers.assert_called_once_with(tags=["production", "api"])


class TestMCPToolEndpoints:
    """Test MCP tool discovery endpoints"""
    
    @pytest.mark.asyncio
    async def test_list_all_tools(self, async_client):
        """Test listing all tools"""
        # Note: This would require mocking app.state.tool_discovery
        # For now, just test that the endpoint exists
        response = await async_client.get("/mcp/tools")
        # Will return 500 without proper setup, but confirms endpoint exists
        assert response.status_code in [200, 500]
        
    @pytest.mark.asyncio
    async def test_list_tools_with_filters(self, async_client):
        """Test listing tools with various filters"""
        response = await async_client.get(
            "/mcp/tools?category=financial&capability=read&server=test-mcp&limit=10"
        )
        assert response.status_code in [200, 500]
        
    @pytest.mark.asyncio
    async def test_search_tools(self, async_client):
        """Test searching for tools"""
        response = await async_client.get("/mcp/tools?query=customer")
        assert response.status_code in [200, 500]
        
    @pytest.mark.asyncio
    async def test_get_tool_details(self, async_client):
        """Test getting tool details"""
        response = await async_client.get("/mcp/tools/search_customers")
        assert response.status_code in [200, 404, 500]


class TestMCPHealthEndpoints:
    """Test MCP server health check endpoints"""
    
    @pytest.mark.asyncio
    async def test_check_server_health(self, async_client):
        """Test checking server health"""
        # Would require mocking app.state.mcp_registry
        response = await async_client.get("/mcp/servers/test-mcp/health")
        # Will return 503 without registry setup
        assert response.status_code in [200, 404, 503]