"""
Search Metrics Tracking and Analysis

Provides comprehensive tracking and analysis of search strategy performance.
Helps optimize search quality and identify areas for improvement.
"""

import json
import sqlite3
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import structlog
import numpy as np
from pathlib import Path

from .search_strategies import SearchStage, SearchMetrics

logger = structlog.get_logger(__name__)


@dataclass
class SearchQualityMetrics:
    """Aggregated search quality metrics."""
    avg_stages_executed: float
    avg_total_results: float
    avg_response_time_ms: float
    stage_success_rates: Dict[str, float]  # Stage -> success rate (0-1)
    stage_avg_results: Dict[str, float]
    stage_avg_time_ms: Dict[str, float]
    stage_counts: Dict[str, int]  # Stage -> usage count
    profile_usage: Dict[str, int]
    threshold_adjustments: int
    period_start: datetime
    period_end: datetime
    total_searches: int


class SearchMetricsTracker:
    """Tracks and analyzes search metrics for optimization."""
    
    def __init__(self, db_path: str = "data/search_metrics.db"):
        self.db_path = db_path
        self._ensure_db_directory()
        self._init_database()
        
    def _ensure_db_directory(self):
        """Ensure the database directory exists."""
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
        
    def _init_database(self):
        """Initialize the metrics database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS search_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    query_hash TEXT NOT NULL,
                    profile_used TEXT NOT NULL,
                    total_stages INTEGER NOT NULL,
                    total_results INTEGER NOT NULL,
                    total_time_ms INTEGER NOT NULL,
                    stages_data TEXT NOT NULL,  -- JSON
                    adjustments_data TEXT,      -- JSON
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_timestamp 
                ON search_metrics(timestamp)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_profile 
                ON search_metrics(profile_used)
            """)
            
            # Table for tracking search quality feedback
            conn.execute("""
                CREATE TABLE IF NOT EXISTS search_feedback (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    search_metric_id INTEGER NOT NULL,
                    tool_selected TEXT,
                    was_successful BOOLEAN,
                    user_satisfaction INTEGER,  -- 1-5 scale
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (search_metric_id) REFERENCES search_metrics(id)
                )
            """)
            
            conn.commit()
    
    def record_search(self, query: str, metrics: SearchMetrics) -> int:
        """Record a search operation's metrics."""
        # Simple hash for query grouping (privacy-preserving)
        query_hash = str(hash(query.lower().strip()) % 1000000)
        
        summary = metrics.get_summary()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                INSERT INTO search_metrics 
                (timestamp, query_hash, profile_used, total_stages, 
                 total_results, total_time_ms, stages_data, adjustments_data)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                datetime.now().isoformat(),
                query_hash,
                summary["profile"],
                summary["total_stages"],
                summary["total_results"],
                summary["total_time_ms"],
                json.dumps(summary["stages"]),
                json.dumps(summary.get("adjustments")) if summary.get("adjustments") else None
            ))
            
            return cursor.lastrowid
    
    def record_feedback(
        self,
        search_metric_id: int,
        tool_selected: Optional[str] = None,
        was_successful: Optional[bool] = None,
        user_satisfaction: Optional[int] = None
    ):
        """Record feedback for a search operation."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO search_feedback 
                (search_metric_id, tool_selected, was_successful, user_satisfaction)
                VALUES (?, ?, ?, ?)
            """, (search_metric_id, tool_selected, was_successful, user_satisfaction))
    
    def get_quality_metrics(
        self,
        hours: int = 24,
        profile: Optional[str] = None
    ) -> SearchQualityMetrics:
        """Get aggregated quality metrics for a time period."""
        start_time = datetime.now() - timedelta(hours=hours)
        
        with sqlite3.connect(self.db_path) as conn:
            # Base query
            query = """
                SELECT 
                    profile_used,
                    total_stages,
                    total_results,
                    total_time_ms,
                    stages_data,
                    adjustments_data
                FROM search_metrics
                WHERE timestamp >= ?
            """
            params = [start_time.isoformat()]
            
            if profile:
                query += " AND profile_used = ?"
                params.append(profile)
            
            rows = conn.execute(query, params).fetchall()
            
        if not rows:
            return SearchQualityMetrics(
                avg_stages_executed=0,
                avg_total_results=0,
                avg_response_time_ms=0,
                stage_success_rates={},
                stage_avg_results={},
                stage_avg_time_ms={},
                stage_counts={},
                profile_usage={},
                threshold_adjustments=0,
                period_start=start_time,
                period_end=datetime.now(),
                total_searches=0
            )
        
        # Aggregate metrics
        total_searches = len(rows)
        total_stages = sum(row[1] for row in rows)
        total_results = sum(row[2] for row in rows)
        total_time = sum(row[3] for row in rows)
        
        profile_counts = {}
        stage_results = {}
        stage_times = {}
        stage_counts = {}
        adjustment_count = 0
        
        for row in rows:
            profile_name = row[0]
            stages_data = json.loads(row[4])
            adjustments = json.loads(row[5]) if row[5] else None
            
            # Count profiles
            profile_counts[profile_name] = profile_counts.get(profile_name, 0) + 1
            
            # Count adjustments
            if adjustments:
                adjustment_count += 1
            
            # Aggregate stage data
            for stage_info in stages_data:
                stage_name = stage_info["stage"]
                results = stage_info["results"]
                time_ms = stage_info["time_ms"]
                
                if stage_name not in stage_results:
                    stage_results[stage_name] = []
                    stage_times[stage_name] = []
                    stage_counts[stage_name] = 0
                
                stage_results[stage_name].append(results)
                stage_times[stage_name].append(time_ms)
                stage_counts[stage_name] += 1
        
        # Calculate averages
        stage_avg_results = {
            stage: sum(results) / len(results)
            for stage, results in stage_results.items()
        }
        
        stage_avg_time_ms = {
            stage: sum(times) / len(times)
            for stage, times in stage_times.items()
        }
        
        # Calculate success rates (stages that returned results)
        stage_success_rates = {
            stage: sum(1 for r in results if r > 0) / len(results)
            for stage, results in stage_results.items()
        }
        
        return SearchQualityMetrics(
            avg_stages_executed=total_stages / total_searches,
            avg_total_results=total_results / total_searches,
            avg_response_time_ms=total_time / total_searches,
            stage_success_rates=stage_success_rates,
            stage_avg_results=stage_avg_results,
            stage_avg_time_ms=stage_avg_time_ms,
            stage_counts=stage_counts,
            profile_usage=profile_counts,
            threshold_adjustments=adjustment_count,
            period_start=start_time,
            period_end=datetime.now(),
            total_searches=total_searches
        )
    
    def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """Get recommendations for search strategy optimization."""
        recommendations = []
        
        # Get metrics for last 24 hours
        metrics = self.get_quality_metrics(hours=24)
        
        if metrics.total_searches < 10:
            return [{
                "type": "info",
                "message": "Not enough data for recommendations (need at least 10 searches)"
            }]
        
        # Check average response time
        if metrics.avg_response_time_ms > 100:
            recommendations.append({
                "type": "performance",
                "severity": "medium",
                "message": f"Average response time is {metrics.avg_response_time_ms:.0f}ms. Consider using 'fast' profile for better performance.",
                "action": "set_profile",
                "params": {"profile": "fast"}
            })
        
        # Check stage efficiency
        for stage, success_rate in metrics.stage_success_rates.items():
            if success_rate < 0.3:
                recommendations.append({
                    "type": "efficiency",
                    "severity": "low",
                    "message": f"Stage '{stage}' has low success rate ({success_rate:.1%}). Consider adjusting thresholds.",
                    "action": "adjust_threshold",
                    "params": {"stage": stage, "adjustment": -0.05}
                })
        
        # Check if we're over-searching
        if metrics.avg_stages_executed > 3:
            recommendations.append({
                "type": "efficiency",
                "severity": "medium",
                "message": f"Average {metrics.avg_stages_executed:.1f} stages per search. Consider using 'precise' profile.",
                "action": "set_profile",
                "params": {"profile": "precise"}
            })
        
        # Check result quality
        if metrics.avg_total_results < 5:
            recommendations.append({
                "type": "quality",
                "severity": "high",
                "message": f"Low average results ({metrics.avg_total_results:.1f}). Consider using 'broad' profile.",
                "action": "set_profile",
                "params": {"profile": "broad"}
            })
        
        return recommendations
    
    def get_stage_performance_report(self, hours: int = 168) -> Dict[str, Any]:
        """Get detailed performance report by stage."""
        metrics = self.get_quality_metrics(hours=hours)
        
        stages_report = []
        for stage in SearchStage:
            stage_name = stage.value
            if stage_name in metrics.stage_success_rates:
                stages_report.append({
                    "stage": stage_name,
                    "success_rate": metrics.stage_success_rates[stage_name],
                    "avg_results": metrics.stage_avg_results.get(stage_name, 0),
                    "avg_time_ms": metrics.stage_avg_time_ms.get(stage_name, 0),
                    "usage_count": metrics.stage_counts.get(stage_name, 0)
                })
        
        return {
            "period": {
                "start": metrics.period_start.isoformat(),
                "end": metrics.period_end.isoformat(),
                "hours": hours
            },
            "summary": {
                "total_searches": metrics.total_searches,
                "avg_stages_per_search": metrics.avg_stages_executed,
                "avg_response_time_ms": metrics.avg_response_time_ms,
                "profile_distribution": metrics.profile_usage
            },
            "stages": sorted(
                stages_report,
                key=lambda x: x["usage_count"],
                reverse=True
            ),
            "recommendations": self.get_optimization_recommendations()
        }
    
    def export_metrics(self, output_path: str, days: int = 7):
        """Export metrics to JSON file for analysis."""
        start_time = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            rows = conn.execute("""
                SELECT * FROM search_metrics
                WHERE timestamp >= ?
                ORDER BY timestamp
            """, (start_time.isoformat(),)).fetchall()
        
        metrics_data = []
        for row in rows:
            metrics_data.append({
                "id": row[0],
                "timestamp": row[1],
                "query_hash": row[2],
                "profile_used": row[3],
                "total_stages": row[4],
                "total_results": row[5],
                "total_time_ms": row[6],
                "stages": json.loads(row[7]),
                "adjustments": json.loads(row[8]) if row[8] else None
            })
        
        with open(output_path, 'w') as f:
            json.dump({
                "export_date": datetime.now().isoformat(),
                "period_days": days,
                "total_records": len(metrics_data),
                "metrics": metrics_data
            }, f, indent=2)
        
        logger.info(f"Exported {len(metrics_data)} metrics to {output_path}")