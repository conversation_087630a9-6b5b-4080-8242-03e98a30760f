{"permissions": {"allow": ["mcp__sage-intacct__search_across_modules", "Bash(python -m pytest tests/test_gl_orchestrator_migration.py -v)", "Bash(python3 -m pytest tests/test_gl_orchestrator_migration.py -v)", "Bash(pip install:*)", "Bash(pip3 install:*)", "Bash(python3 -m pip install:*)", "<PERSON><PERSON>(python3:*)", "Bash(ls:*)", "Bash(grep:*)", "Bash(rm:*)", "Bash(python tests/test_ar_orchestrator_migration.py)", "Bash(find:*)", "<PERSON><PERSON>(python:*)", "mcp__sage-intacct__list_enabled_modules", "<PERSON><PERSON>(diff:*)", "mcp__fast-agent_Docs__fetch_fast_agent_documentation", "mcp__fast-agent_Docs__search_fast_agent_documentation", "mcp__fast-agent_Docs__search_fast_agent_code", "mcp__fast-agent_Docs__fetch_generic_url_content", "<PERSON><PERSON>(source:*)", "Bash(uv pip install:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(mkdir:*)", "mcp__fastmcp_Docs__fetch_fastmcp_documentation", "mcp__fastmcp_Docs__search_fastmcp_documentation", "<PERSON><PERSON>(uv venv:*)", "mcp__fastmcp_Docs__search_fastmcp_code", "mcp__fastmcp_Docs__fetch_generic_url_content", "<PERSON><PERSON>(curl:*)", "Bash(ss:*)", "<PERSON><PERSON>(uv run:*)", "Bash(rg:*)", "<PERSON><PERSON>(set -a)", "Bash(set +a)", "<PERSON><PERSON>(sed:*)", "Bash(cp:*)", "mcp__sqlite-vec_Docs__fetch_sqlite_vec_documentation", "mcp__sqlite-vec_Docs__search_sqlite_vec_documentation", "mcp__crawl4ai-rag__smart_crawl_url", "mcp__crawl4ai-rag__crawl_single_page", "WebFetch(domain:raw.githubusercontent.com)", "<PERSON><PERSON>(jq:*)", "Bash(sqlite3:*)"], "deny": []}}