# Current Orchestrator Architecture

## Overview

The AI Workspace Agents system uses a sophisticated, LLM-driven orchestration architecture that dynamically discovers and executes tools from multiple MCP (Model Context Protocol) servers without any hardcoded rules or pattern matching. This document describes the current implementation as of June 2025.

## Core Architecture Components

### 1. Dynamic Orchestrator (`agents/orchestrator.py`)

The `DynamicOrchestrator` class serves as the central intelligence hub:

```python
class DynamicOrchestrator:
    """
    Dynamic orchestrator that discovers and uses tools from all registered MCP servers
    Now uses direct MCP client connections instead of FastAgent framework
    """
```

Key features:
- **No hardcoded tool mappings** - all tools discovered dynamically
- **Direct MCP client connections** - bypasses FastAgent framework
- **Single entry point** for all financial operations
- **LLM-driven decision making** - no pattern matching

### 2. Tool Discovery System

#### MCP Server Registry (`services/mcp_registry.py`)
- Manages registration and connection to multiple MCP servers
- Maintains server health and connection status
- Provides unified interface for tool execution

#### Tool Discovery Service (`services/tool_discovery.py`)
- Dynamically discovers all available tools from registered MCP servers
- Builds comprehensive tool catalog with metadata
- Categories tools by function and server
- No predefined tool lists

Example tool catalog structure:
```python
{
    "mcp__sage-intacct__search_across_modules": {
        "server_name": "sage-intacct",
        "name": "search_across_modules",
        "description": "Search across multiple Intacct modules",
        "parameters": {...},
        "category": "search"
    },
    "mcp__sage-intacct__get_financial_summary": {
        "server_name": "sage-intacct",
        "name": "get_financial_summary",
        "description": "Get financial summary across modules",
        "parameters": {...},
        "category": "reporting"
    }
    # ... dynamically discovered tools
}
```

### 3. Enhanced LLM Service (`services/enhanced_llm_service.py`)

The brain of the system - makes intelligent decisions in a single LLM call:

#### Key Capabilities:
1. **Unified Analysis and Response**
   - Combines intent analysis, tool selection, and response generation
   - Single LLM call for entire decision process
   - No fallback pattern matching

2. **Financial Terminology Understanding**
   - Built-in mappings for common financial terms
   - Examples: "debtors" → "accounts receivable", "creditors" → "accounts payable"
   - Helps LLM understand various ways users express financial concepts

3. **Comprehensive Context**
   - Sends complete tool catalog to LLM
   - Includes tool descriptions, parameters, and usage examples
   - Provides current date/time for temporal queries

#### LLM Response Types:

```json
// Tool Execution Response
{
    "response_type": "tool_execution",
    "analysis": "User wants to see customers with outstanding receivables",
    "intent": "accounts_receivable_query",
    "tool_calls": [
        {
            "tool": "mcp__sage-intacct__search_across_modules",
            "parameters": {
                "query": "outstanding receivables",
                "modules": ["AR"],
                "limit": 50
            },
            "purpose": "Find customers with outstanding balances"
        }
    ],
    "response_preview": "I'll search for customers with outstanding receivables.",
    "confidence": 0.95
}

// Conversational Response
{
    "response_type": "conversational",
    "analysis": "User is greeting the assistant",
    "intent": "greeting",
    "conversational_response": "Hello! I'm here to help with your financial operations.",
    "confidence": 0.95
}
```

### 4. Execution Flow

```mermaid
graph TD
    A[User Request] --> B[Dynamic Orchestrator]
    B --> C[Tool Discovery Service]
    C --> D[Build Tool Catalog]
    D --> E[Enhanced LLM Service]
    E --> F{LLM Decision}
    F -->|Tool Execution| G[Execute Tools via MCP]
    F -->|Conversational| H[Return Direct Response]
    G --> I[Response Processor]
    H --> I
    I --> J[Final Response to User]
```

Detailed flow:
1. **User Request**: Natural language query enters the system
2. **Tool Discovery**: System maintains current catalog of all available tools
3. **LLM Analysis**: Single LLM call analyzes request with full context
4. **Decision**: LLM determines if tools needed or conversational response
5. **Execution**: Tools executed directly via MCP registry if needed
6. **Response Processing**: Results formatted for user consumption

### 5. Supporting Services

#### Response Processor (`services/response_processor.py`)
- Formats tool execution results
- Handles multi-tool responses
- Creates user-friendly summaries

#### Parallel Executor (`services/parallel_executor.py`)
- Executes multiple tools concurrently when possible
- Manages dependencies between tool calls
- Optimizes execution time

#### Confidence Scorer (`services/confidence_scorer.py`)
- Provides confidence scoring for decisions
- Currently used minimally (system relies on LLM confidence)

## Configuration

### MCP Server Configuration (`mcp.config.yaml`)
```yaml
mcp:
  servers:
    sage-intacct:
      transport: sse
      url: "http://localhost:8001/sse"
      # ... configuration details
      
    "Sage Business Cloud Accounting":
      transport: sse
      endpoint: "https://sbca-mcp.provenlabs.xyz/sse"
      # ... configuration details

agents:
  orchestrator:
    name: "Dynamic Orchestration Agent"
    mcp_servers:
      - sage-intacct
      - "Sage Business Cloud Accounting"
```

## Key Architectural Decisions

### 1. No Hardcoded Rules
- System discovers capabilities dynamically
- No predefined tool mappings
- No pattern matching for intent detection
- Pure LLM-driven intelligence

### 2. Single LLM Call Architecture
- One comprehensive LLM call handles everything
- No multi-stage decision making
- Reduces latency and complexity
- Ensures consistent context

### 3. Financial Domain Expertise
- Built-in financial terminology understanding
- Context-aware date handling
- Business system integration focus

### 4. Scalable Tool Discovery
- Automatic discovery of new tools
- No code changes needed for new MCP servers
- Dynamic capability expansion

## Current Limitations

### 1. Token Limit Issues
When multiple MCP servers are connected (e.g., 159+ SBCA tools + Intacct tools), the tool catalog sent to the LLM can exceed token limits:
- Current limit: 30,000 tokens (GPT-4)
- With SBCA: 48,223 tokens requested
- Causes 429 rate limit errors

### 2. No Learning Mechanism
- System doesn't learn from usage patterns
- No optimization based on successful executions
- Each request processed independently

### 3. No Tool Filtering
- Entire tool catalog sent to LLM every time
- No intelligent pre-filtering based on request
- No semantic search for relevant tools

### 4. Limited Context Window
- No conversation memory between requests
- No user preference tracking
- No pattern recognition across sessions

## Integration Points

### 1. MCP Servers
- Direct stdio/SSE connections to MCP servers
- Health checking and automatic reconnection
- Tool discovery on server connection

### 2. API Service
- FastAPI service exposes orchestrator
- WebSocket support for real-time interaction
- RESTful endpoints for tool discovery

### 3. Frontend Communication
- JSON-based request/response format
- Metadata included for UI enhancements
- Error handling and status reporting

## Security and Reliability

### 1. API Key Management
- OpenAI API key required for LLM calls
- Secure configuration through environment variables
- No credentials in code

### 2. Error Handling
- Graceful degradation on LLM failures
- Comprehensive error messages
- Fallback responses for common issues

### 3. Health Monitoring
- MCP server health checks
- Connection status tracking
- Automatic retry mechanisms

## Future Architecture Considerations

To address current limitations, future enhancements should focus on:

1. **Capability-Based Tool Filtering**
   - Semantic search for relevant tools
   - Reduce token usage before LLM call
   - Maintain LLM-driven decision making

2. **Learning and Adaptation**
   - Track successful tool executions
   - Build usage patterns
   - Optimize tool selection over time

3. **Conversation Memory**
   - Add context persistence
   - Enable multi-turn interactions
   - Personalize responses

4. **Scalable Architecture**
   - Handle unlimited MCP servers
   - Manage token limits intelligently
   - Support multiple LLM providers

---

*This document reflects the current implementation as of June 2025. The system is actively evolving to address scalability challenges while maintaining its core principle of LLM-driven, rule-free orchestration.*