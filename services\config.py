"""
Configuration management for AI Workspace Agent Service
"""

from functools import lru_cache
from typing import List

from pydantic import Field, validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings loaded from environment variables"""
    
    model_config = SettingsConfigDict(
        env_file="config/api.env",
        env_file_encoding="utf-8",
        case_sensitive=False,
    )
    
    # Server Configuration
    api_host: str = Field(default="0.0.0.0", description="API host address")
    api_port: int = Field(default=8000, description="API port")
    api_env: str = Field(default="development", description="Environment")
    
    # CORS Configuration
    cors_origins: List[str] = Field(
        default=["http://localhost:3000"], 
        description="Allowed CORS origins"
    )
    
    # Logging
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="json", description="Log format")
    
    # FastAPI Configuration
    api_title: str = Field(
        default="AI Workspace Agent Service", 
        description="API title"
    )
    api_version: str = Field(default="0.1.0", description="API version")
    api_description: str = Field(
        default="Agent service for AI Workspace using fast-agent",
        description="API description"
    )
    
    # MCP Server Configuration
    intacct_api_key: str = Field(default="", description="Intacct API key")
    intacct_sender_id: str = Field(default="", description="Intacct sender ID")
    intacct_sender_password: str = Field(default="", description="Intacct sender password")
    intacct_company_id: str = Field(default="", description="Intacct company ID")
    intacct_user_id: str = Field(default="", description="Intacct user ID")
    intacct_user_password: str = Field(default="", description="Intacct user password")
    
    # Model Configuration
    default_model: str = Field(
        default="gpt-4-turbo-preview", 
        description="Default LLM model"
    )
    openai_api_key: str = Field(..., description="OpenAI API key (required)")
    anthropic_api_key: str = Field(default="", description="Anthropic API key")
    openai_temperature: float = Field(default=0.3, description="OpenAI model temperature")
    
    @validator('openai_api_key')
    def validate_openai_api_key(cls, v):
        if not v or v == "your_openai_key" or v == "sk-...":
            raise ValueError(
                "Valid OpenAI API key is required. Set OPENAI_API_KEY in config/api.env\n"
                "Get your key from: https://platform.openai.com/api-keys"
            )
        if not v.startswith("sk-"):
            raise ValueError(
                "Invalid OpenAI API key format. Key should start with 'sk-'"
            )
        return v
    
    # Performance Settings
    request_timeout_seconds: int = Field(
        default=30, 
        description="Request timeout in seconds"
    )
    max_concurrent_agents: int = Field(
        default=10, 
        description="Maximum concurrent agents"
    )
    agent_idle_timeout_seconds: int = Field(
        default=300, 
        description="Agent idle timeout in seconds"
    )
    
    # Orchestration Settings
    orchestrator_confidence_threshold: float = Field(
        default=0.8,
        description="Minimum confidence for automatic routing"
    )
    orchestrator_max_retries: int = Field(
        default=3,
        description="Max retries for tool execution"
    )
    orchestrator_timeout_seconds: int = Field(
        default=90,
        description="Timeout for orchestrator operations"
    )
    
    # Dynamic MCP Configuration
    mcp_config_dir: str = Field(
        default=".config",
        description="Directory for dynamic MCP configurations"
    )
    mcp_discovery_interval: int = Field(
        default=300,
        description="Seconds between tool discovery refreshes"
    )
    mcp_health_check_interval: int = Field(
        default=30,
        description="Seconds between health checks"
    )
    
    # Performance Settings - Additional
    max_concurrent_tools: int = Field(
        default=5,
        description="Max tools that can run in parallel"
    )
    tool_cache_ttl_seconds: int = Field(
        default=3600,
        description="Tool schema cache duration"
    )
    
    # Security
    jwt_secret_key: str = Field(
        default="change-me-in-production", 
        description="JWT secret key"
    )
    jwt_algorithm: str = Field(default="HS256", description="JWT algorithm")
    jwt_expiration_minutes: int = Field(
        default=60, 
        description="JWT expiration in minutes"
    )


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()
