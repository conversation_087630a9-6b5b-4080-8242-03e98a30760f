"""
Validate MCP server configuration and connectivity.

This script tests that the MCP servers can be started and 
that we can discover available tools.
"""
import asyncio
import logging
import os
import sys
from pathlib import Path
import yaml

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.config import Settings

logger = logging.getLogger(__name__)


async def validate_mcp_config():
    """Validate MCP server configuration."""
    config_path = project_root / "mcp.config.yaml"
    if not config_path.exists():
        logger.error("mcp.config.yaml not found")
        return False
    
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Check if sage-intacct server is configured
        if 'sage-intacct' not in config.get('mcp_servers', {}):
            logger.error("sage-intacct MCP server not configured")
            return False
            
        intacct_config = config['mcp_servers']['sage-intacct']
        
        # Verify command and args are present
        if not intacct_config.get('command') or not intacct_config.get('args'):
            logger.error("MCP server command or args missing")
            return False
            
        logger.info("[SUCCESS] MCP server configuration validated")
        logger.info(f"  Command: {intacct_config['command']}")
        logger.info(f"  Args: {' '.join(intacct_config['args'])}")
        
        # Check if MCP server path exists
        mcp_path = None
        for arg in intacct_config['args']:
            if "sage-intacct-mcp-server" in arg:
                # Extract path from cd command
                parts = arg.split(" && ")
                if parts:
                    path_part = parts[0].replace("cd /d ", "").strip()
                    mcp_path = Path(path_part)
                    break
        
        if mcp_path and mcp_path.exists():
            logger.info(f"[SUCCESS] MCP server found at: {mcp_path}")
        else:
            logger.warning("MCP server path not found or not accessible")
            
        return True
        
    except Exception as e:
        logger.error(f"Failed to validate configuration: {e}")
        return False


async def check_mcp_server_status():
    """Check if MCP server is accessible."""
    logger.info("\nChecking MCP server status...")
    logger.info("Note: MCP servers run independently with their own credentials")
    logger.info("This service only connects to them - no credentials needed here")
    
    # In a real implementation, you would:
    # 1. Try to connect to the MCP server
    # 2. Call a health check endpoint
    # 3. List available tools
    
    logger.info("[INFO] MCP server status check requires the server to be running")
    logger.info("[INFO] Start the Sage Intacct MCP server separately before using agents")
    
    return True


async def main():
    """Run all validation checks."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("MCP Server Configuration Validator")
    logger.info("=" * 50)
    
    # Validate configuration
    config_valid = await validate_mcp_config()
    
    # Check server status
    if config_valid:
        await check_mcp_server_status()
    
    logger.info("\n" + "=" * 50)
    if config_valid:
        logger.info("✅ Configuration is valid")
        logger.info("📝 Next steps:")
        logger.info("   1. Ensure Sage Intacct MCP server is running")
        logger.info("   2. The server should have its own credentials configured")
        logger.info("   3. This agent service will connect to it automatically")
    else:
        logger.error("❌ Configuration validation failed")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
