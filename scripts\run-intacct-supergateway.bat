@echo off
echo Starting Sage Intacct MCP Server with Supergateway...
echo.
echo This will expose the stdio MCP server as an SSE endpoint on http://localhost:8001
echo.

REM Set the working directory for the MCP server
set MCP_DIR=C:\Users\<USER>\Documents\GitHub\sage-intacct-mcp-server

REM Option 1: Run in development mode (no auth required)
echo Running in development mode...
npx -y supergateway ^
    --stdio "cmd /c cd /d %MCP_DIR% && set PYTHONPATH=%MCP_DIR% && python -m src.main --mode development" ^
    --port 8001 ^
    --baseUrl http://localhost:8001 ^
    --ssePath /sse ^
    --messagePath /message

REM Option 2: If you need production mode with auth, uncomment below and set your credentials
REM npx -y supergateway ^
REM     --stdio "cmd /c cd /d %MCP_DIR% && set PYTHONPATH=%MCP_DIR% && set INTACCT_CLIENT_ID=your-actual-client-id && set INTACCT_CLIENT_SECRET=your-actual-secret && python -m src.main --auth-mode env" ^
REM     --port 8001 ^
REM     --baseUrl http://localhost:8001 ^
REM     --ssePath /sse ^
REM     --messagePath /message

pause