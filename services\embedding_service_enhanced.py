"""
Enhanced Embedding Service for Tool Descriptions

Supports both OpenAI embeddings (fast, high-quality) and local sentence-transformers
(offline fallback). Automatically chooses the best available option.
"""

import asyncio
import os
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
import numpy as np
import structlog
import hashlib
import json

# Optional imports with fallbacks
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

logger = structlog.get_logger(__name__)


class EmbeddingCache:
    """Simple in-memory cache for embeddings."""
    
    def __init__(self, ttl_hours: int = 24, max_size: int = 10000):
        self.cache: Dict[str, tuple[np.ndarray, datetime]] = {}
        self.ttl = timedelta(hours=ttl_hours)
        self.max_size = max_size
        
    def get(self, key: str) -> Optional[np.ndarray]:
        """Get embedding from cache if valid."""
        if key in self.cache:
            embedding, timestamp = self.cache[key]
            if datetime.now() - timestamp < self.ttl:
                return embedding
            else:
                # Expired, remove it
                del self.cache[key]
        return None
    
    def put(self, key: str, embedding: np.ndarray):
        """Add embedding to cache."""
        # Evict oldest if at capacity
        if len(self.cache) >= self.max_size:
            oldest_key = min(self.cache.keys(), 
                           key=lambda k: self.cache[k][1])
            del self.cache[oldest_key]
        
        self.cache[key] = (embedding, datetime.now())
    
    def clear(self):
        """Clear the cache."""
        self.cache.clear()


class ToolEmbedder:
    """
    Enhanced tool embedder with OpenAI and local model support.
    
    Automatically chooses the best available embedding provider:
    1. OpenAI (if API key available) - Fast, high-quality
    2. Local sentence-transformers - Offline fallback
    """
    
    def __init__(
        self,
        provider: str = "auto",  # "auto", "openai", "local"
        model_name: Optional[str] = None,
        cache_enabled: bool = True,
        cache_ttl_hours: int = 24,
        cache_max_size: int = 10000,
        openai_api_key: Optional[str] = None
    ):
        self.provider = provider
        self.model_name = model_name
        self.model = None
        self.openai_client = None
        self.dimension = None
        
        # Initialize cache
        self.cache = EmbeddingCache(cache_ttl_hours, cache_max_size) if cache_enabled else None
        
        # Determine the best provider
        self._initialize_provider(openai_api_key)
        
        logger.info(
            "Enhanced ToolEmbedder initialized", 
            provider=self.active_provider,
            model=self.model_name,
            dimension=self.dimension,
            cache_enabled=cache_enabled
        )
    
    def _initialize_provider(self, openai_api_key: Optional[str] = None):
        """Initialize the best available embedding provider."""
        
        if self.provider == "auto":
            # Auto-select best available provider
            if self._can_use_openai(openai_api_key):
                self.active_provider = "openai"
                self.model_name = self.model_name or "text-embedding-3-small"
                self.dimension = 1536  # text-embedding-3-small dimension
            elif SENTENCE_TRANSFORMERS_AVAILABLE:
                self.active_provider = "local"
                self.model_name = self.model_name or "sentence-transformers/all-mpnet-base-v2"
                self.dimension = 768  # all-mpnet-base-v2 dimension
            else:
                raise RuntimeError("No embedding providers available. Install openai or sentence-transformers.")
        
        elif self.provider == "openai":
            if not self._can_use_openai(openai_api_key):
                raise RuntimeError("OpenAI provider requested but not available")
            self.active_provider = "openai"
            self.model_name = self.model_name or "text-embedding-3-small"
            self.dimension = 1536
        
        elif self.provider == "local":
            if not SENTENCE_TRANSFORMERS_AVAILABLE:
                raise RuntimeError("Local provider requested but sentence-transformers not available")
            self.active_provider = "local"
            self.model_name = self.model_name or "sentence-transformers/all-mpnet-base-v2"
            self.dimension = 768
        
        else:
            raise ValueError(f"Unknown provider: {self.provider}")
        
        # Initialize the selected provider
        if self.active_provider == "openai":
            self._initialize_openai(openai_api_key)
        elif self.active_provider == "local":
            # Local model will be lazy-loaded
            pass
    
    def _can_use_openai(self, api_key: Optional[str] = None) -> bool:
        """Check if OpenAI is available and configured."""
        if not OPENAI_AVAILABLE:
            return False
        
        # Check for API key
        key = api_key or os.getenv("OPENAI_API_KEY")
        return key is not None
    
    def _initialize_openai(self, api_key: Optional[str] = None):
        """Initialize OpenAI client."""
        key = api_key or os.getenv("OPENAI_API_KEY")
        if not key:
            raise RuntimeError("OpenAI API key not found")
        
        self.openai_client = openai.OpenAI(api_key=key)
        logger.info("OpenAI client initialized", model=self.model_name)
    
    def _ensure_local_model_loaded(self):
        """Lazy load the local model on first use."""
        if self.active_provider == "local" and self.model is None:
            logger.info("Loading local embedding model", model=self.model_name)
            self.model = SentenceTransformer(self.model_name)
            # Update dimension based on actual model
            self.dimension = self.model.get_sentence_embedding_dimension()
            logger.info("Local model loaded", dimension=self.dimension)
    
    def _create_embedding_text(self, tool: Dict[str, Any]) -> str:
        """
        Create rich text representation of a tool for embedding.
        
        Combines name, description, parameters, and examples into
        a comprehensive text that captures the tool's semantics.
        """
        parts = []
        
        # Tool name (most important)
        name = tool.get("name", "")
        if name:
            # Convert snake_case to readable format
            readable_name = name.replace("_", " ").title()
            parts.append(f"Tool: {readable_name}")
            parts.append(f"Function: {name}")
        
        # Description (very important)
        description = tool.get("description", "")
        if description:
            parts.append(f"Description: {description}")
        
        # Category and capabilities
        category = tool.get("category")
        if category:
            parts.append(f"Category: {category}")
        
        capabilities = tool.get("capabilities", [])
        if capabilities:
            parts.append(f"Capabilities: {', '.join(capabilities)}")
        
        # Parameters (important for understanding usage)
        parameters = tool.get("parameters", {})
        if parameters:
            # Extract parameter names and descriptions
            param_info = []
            
            # Handle different parameter formats
            if isinstance(parameters, dict):
                # Check for JSON schema format
                props = parameters.get("properties", parameters)
                required = parameters.get("required", [])
                
                for param_name, param_spec in props.items():
                    param_desc = param_spec.get("description", "")
                    param_type = param_spec.get("type", "")
                    
                    param_str = param_name
                    if param_type:
                        param_str += f" ({param_type})"
                    if param_desc:
                        param_str += f": {param_desc}"
                    if param_name in required:
                        param_str += " [required]"
                    
                    param_info.append(param_str)
            
            if param_info:
                parts.append(f"Parameters: {'; '.join(param_info)}")
        
        # Examples (helpful for understanding)
        examples = tool.get("examples", [])
        if examples:
            example_texts = []
            for example in examples[:3]:  # Limit to 3 examples
                if isinstance(example, str):
                    example_texts.append(example)
                elif isinstance(example, dict):
                    # Format example as text
                    example_text = example.get("description", "")
                    if not example_text and "input" in example:
                        example_text = f"Input: {json.dumps(example['input'])}"
                    if example_text:
                        example_texts.append(example_text)
            
            if example_texts:
                parts.append(f"Examples: {'; '.join(example_texts)}")
        
        # Keywords
        keywords = tool.get("keywords", [])
        if keywords:
            parts.append(f"Keywords: {', '.join(keywords)}")
        
        # Server/module context
        server = tool.get("server", tool.get("server_name", ""))
        if server:
            parts.append(f"Server: {server}")
        
        # Combine all parts
        embedding_text = " | ".join(parts)
        
        return embedding_text
    
    def _compute_cache_key(self, text: str) -> str:
        """Compute cache key for embedding text."""
        # Include provider and model in cache key to avoid conflicts
        key_data = f"{self.active_provider}:{self.model_name}:{text}"
        return hashlib.sha256(key_data.encode()).hexdigest()

    async def _encode_with_openai(self, texts: Union[str, List[str]]) -> np.ndarray:
        """Encode text(s) using OpenAI API."""
        if not self.openai_client:
            raise RuntimeError("OpenAI client not initialized")

        # Ensure texts is a list
        is_single = isinstance(texts, str)
        text_list = [texts] if is_single else texts

        try:
            # Call OpenAI API
            response = await asyncio.to_thread(
                self.openai_client.embeddings.create,
                input=text_list,
                model=self.model_name
            )

            # Extract embeddings
            embeddings = [np.array(item.embedding, dtype=np.float32) for item in response.data]

            if is_single:
                return embeddings[0]
            else:
                return np.array(embeddings)

        except Exception as e:
            logger.error("OpenAI embedding failed", error=str(e))
            # Try fallback to local model if available
            if SENTENCE_TRANSFORMERS_AVAILABLE:
                logger.info("Falling back to local model")
                return await self._encode_with_local(texts)
            else:
                raise

    async def _encode_with_local(self, texts: Union[str, List[str]]) -> np.ndarray:
        """Encode text(s) using local sentence-transformers model."""
        self._ensure_local_model_loaded()

        # Ensure texts is a list
        is_single = isinstance(texts, str)
        text_list = [texts] if is_single else texts

        # Generate embedding
        # Run in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        embeddings = await loop.run_in_executor(
            None,
            lambda: self.model.encode(text_list, convert_to_numpy=True)
        )

        # Ensure correct shape
        if is_single:
            if len(embeddings.shape) == 2:
                return embeddings[0]
            else:
                return embeddings
        else:
            if len(embeddings.shape) == 1:
                # Single embedding returned, reshape
                return embeddings.reshape(1, -1)
            else:
                return embeddings

    async def encode(self, text: str) -> np.ndarray:
        """
        Encode text into an embedding vector.

        Args:
            text: Text to encode

        Returns:
            Embedding vector as numpy array
        """
        # Check cache
        if self.cache:
            cache_key = self._compute_cache_key(text)
            cached = self.cache.get(cache_key)
            if cached is not None:
                return cached

        # Generate embedding using active provider
        if self.active_provider == "openai":
            embedding = await self._encode_with_openai(text)
        else:
            embedding = await self._encode_with_local(text)

        # Cache the result
        if self.cache:
            self.cache.put(cache_key, embedding)

        return embedding

    async def encode_batch(self, texts: List[str]) -> np.ndarray:
        """
        Encode multiple texts in a batch.

        Args:
            texts: List of texts to encode

        Returns:
            Array of embeddings
        """
        if not texts:
            return np.array([])

        # Separate cached and uncached
        uncached_texts = []
        uncached_indices = []
        results = [None] * len(texts)

        if self.cache:
            for i, text in enumerate(texts):
                cache_key = self._compute_cache_key(text)
                cached = self.cache.get(cache_key)
                if cached is not None:
                    results[i] = cached
                else:
                    uncached_texts.append(text)
                    uncached_indices.append(i)
        else:
            uncached_texts = texts
            uncached_indices = list(range(len(texts)))

        # Encode uncached texts
        if uncached_texts:
            if self.active_provider == "openai":
                embeddings = await self._encode_with_openai(uncached_texts)
            else:
                embeddings = await self._encode_with_local(uncached_texts)

            # Store results and update cache
            for i, idx in enumerate(uncached_indices):
                results[idx] = embeddings[i]
                if self.cache:
                    cache_key = self._compute_cache_key(uncached_texts[i])
                    self.cache.put(cache_key, embeddings[i])

        return np.array(results)

    async def encode_tool(self, tool: Dict[str, Any]) -> np.ndarray:
        """
        Encode a tool's metadata into an embedding.

        Args:
            tool: Tool metadata dictionary

        Returns:
            Embedding vector
        """
        embedding_text = self._create_embedding_text(tool)
        return await self.encode(embedding_text)

    async def encode_tools(self, tools: List[Dict[str, Any]]) -> np.ndarray:
        """
        Encode multiple tools in a batch.

        Args:
            tools: List of tool metadata dictionaries

        Returns:
            Array of embeddings
        """
        texts = [self._create_embedding_text(tool) for tool in tools]
        return await self.encode_batch(texts)

    def clear_cache(self):
        """Clear the embedding cache."""
        if self.cache:
            self.cache.clear()
            logger.info("Embedding cache cleared")

    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about the active provider."""
        return {
            "provider": self.active_provider,
            "model": self.model_name,
            "dimension": self.dimension,
            "openai_available": OPENAI_AVAILABLE,
            "local_available": SENTENCE_TRANSFORMERS_AVAILABLE,
            "cache_enabled": self.cache is not None
        }
