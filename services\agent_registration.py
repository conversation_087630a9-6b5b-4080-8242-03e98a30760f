"""
Agent Registration Module

Registers all available agents with the AgentManager.
Supports both hardcoded registration and dynamic discovery.
"""

import asyncio
from typing import Dict, Any, Optional
import structlog

from services.agent_manager import get_agent_manager, AgentResponse, AgentMessage
from config.model_config import get_model_for_agent

# Import the main orchestrator
try:
    from agents.orchestrator import DynamicOrchestrator, orchestrator
    ORCHESTRATOR_AVAILABLE = True
except ImportError:
    ORCHESTRATOR_AVAILABLE = False
    DynamicOrchestrator = None
    orchestrator = None

logger = structlog.get_logger(__name__)











# ServiceContainer will handle registry sharing

# Create a global orchestrator instance
_orchestrator_instance = None

def get_orchestrator():
    """Get or create the orchestrator instance"""
    global _orchestrator_instance
    if _orchestrator_instance is None and ORCHESTRATOR_AVAILABLE:
        # Use the ServiceContainer approach
        from services.service_container import get_service_container
        service_container = get_service_container()
        _orchestrator_instance = DynamicOrchestrator(service_container=service_container)
    return _orchestrator_instance


async def orchestrator_agent_handler(message: AgentMessage) -> AgentResponse:
    """
    Handler wrapper for Dynamic Orchestrator.
    
    Translates between AgentManager message format and Orchestrator interface.
    """
    try:
        orchestrator = get_orchestrator()
        if not orchestrator:
            raise RuntimeError("Orchestrator not available")
            
        # Initialize if needed
        if not orchestrator._initialized:
            await orchestrator.initialize()
        
        # Send to orchestrator
        result = await orchestrator.process(message.content)
        
        # Convert result to AgentResponse
        return AgentResponse(
            agent_id="orchestrator",
            status="success" if not result.get("error") else "error",
            content=result.get("response", result.get("content")),
            correlation_id=message.correlation_id,
            metadata=result.get("metadata", {}),
            error=result.get("error") if result.get("error") else None
        )
        
    except Exception as e:
        logger.error("Orchestrator handler error", error=str(e), exc_info=True)
        return AgentResponse(
            agent_id="orchestrator",
            status="error",
            content=None,
            correlation_id=message.correlation_id,
            error=str(e)
        )


async def register_all_agents():
    """
    Register all available agents with the AgentManager.
    
    This function should be called during application startup.
    """
    manager = get_agent_manager()
    
    # AR Agent has been migrated to orchestrator
    
    # AP Agent has been migrated to orchestrator
    
    # Analysis Agent has been migrated to orchestrator
    
    # Report Agent has been migrated to orchestrator
    
    # Validation Agent has been migrated to orchestrator
    
    # Register Dynamic Orchestrator
    await manager.register_agent(
        agent_id="orchestrator",
        name="Dynamic Orchestrator",
        description="Intelligent orchestrator that dynamically routes requests to appropriate MCP tools",
        handler=orchestrator_agent_handler,
        capabilities=[
            "Dynamic intent detection and routing",
            "Multi-tool workflow coordination",
            "Intelligent error handling and recovery",
            "Context-aware tool selection",
            "Parallel tool execution",
            "Complex financial operation orchestration"
        ],
        metadata={
            "module": "orchestrator",
            "mcp_servers": ["sage-intacct"],  # Can work with multiple servers
            "model": get_model_for_agent("orchestrator"),
            "workflow_type": "dynamic",
            "features": ["intent_detection", "tool_discovery", "parallel_execution"]
        }
    )
    
    logger.info("Dynamic Orchestrator registered successfully")
    
    # Perform initial health check
    health = await manager.health_check()
    logger.info(
        "Agent registration complete",
        total_agents=health["total_agents"],
        healthy_agents=health["healthy_agents"]
    )


async def unregister_all_agents():
    """
    Unregister all agents from the AgentManager.
    
    This function should be called during application shutdown.
    """
    manager = get_agent_manager()
    agents = await manager.list_agents()
    
    for agent in agents:
        await manager.unregister_agent(agent.id)
        logger.info(f"Agent {agent.id} unregistered")
    
    logger.info("All agents unregistered")


# For testing individual agents
if __name__ == "__main__":
    async def test_registration():
        """Test agent registration"""
        await register_all_agents()
        
        # Test orchestrator
        manager = get_agent_manager()
        response = await manager.send_message(
            "orchestrator",
            "Show me overdue invoices for customer ABC Corp",
            metadata={"user_id": "test"}
        )
        
        print(f"Orchestrator Response: {response.content}")
        print(f"Orchestrator Status: {response.status}")
        
        # Check health
        health = await manager.health_check()
        print(f"Health: {health}")
        
        # Cleanup
        await unregister_all_agents()
    
    asyncio.run(test_registration())
