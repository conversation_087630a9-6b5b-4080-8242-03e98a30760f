"""
Dynamic Orchestrator Agent - Single entry point for all financial operations

This agent dynamically discovers and uses tools from registered MCP servers
to handle any financial request without hardcoded routing logic.

Refactored to use modular components and direct MCP client connections.
"""
import asyncio
import json
import logging
import os
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timezone
from pathlib import Path

# Import centralized model configuration
from config.model_config import get_model_for_agent

# Import our services
from services.mcp_registry import MCPServerRegistry as MCPRegistry
from services.tool_discovery import ToolDiscoveryService
from services.confidence_scorer import ConfidenceScorer
from services.llm_service import LLMService
from services.enhanced_llm_service import EnhancedLLMService

# Import orchestrator modules
from agents.orchestrator_modules import (
    IntentDetector,
    ToolExecutor,
    ResponseHandler
)

# Configure logging
logger = logging.getLogger(__name__)

# Try to import filtering service, fall back to regular if not available
try:
    from services.enhanced_llm_service_with_filtering import EnhancedLLMServiceWithFiltering
    FILTERING_AVAILABLE = True
except ImportError:
    logger.warning("Intelligent filtering not available - dependencies missing. Using regular service.")
    FILTERING_AVAILABLE = False


def load_system_prompt() -> str:
    """Load the orchestrator system prompt from file"""
    prompt_path = Path(__file__).parent.parent / "prompts" / "orchestrator-system-prompt.md"
    if prompt_path.exists():
        with open(prompt_path, 'r', encoding='utf-8') as f:
            content = f.read()
            # Extract the core system prompt between the markers
            start_marker = "## Core System Prompt"
            end_marker = "## Tool Description Templates"
            if start_marker in content and end_marker in content:
                start_idx = content.index(start_marker) + len(start_marker)
                end_idx = content.index(end_marker)
                # Extract the prompt between triple backticks
                prompt_section = content[start_idx:end_idx].strip()
                if "```" in prompt_section:
                    # Find content between backticks
                    parts = prompt_section.split("```")
                    if len(parts) >= 2:
                        return parts[1].strip()
            return content
    else:
        # Fallback to embedded prompt
        return """
You are the Sage Intacct AI Workspace Orchestrator - an intelligent financial operations assistant that EXECUTES tools to fulfill user requests.

## Your Core Responsibilities

1. **Intent Analysis**: Analyze user requests to determine their intent and required tools
2. **Tool Execution**: ACTUALLY EXECUTE the appropriate tools - don't just describe what you would do
3. **Workflow Coordination**: Execute multi-step processes by chaining tools intelligently
4. **Context Management**: Maintain context across tool calls within a workflow
5. **Error Recovery**: Handle tool failures gracefully with fallback strategies

## IMPORTANT: Tool Execution

When a user makes a request:
- EXECUTE the appropriate tools immediately by calling them
- DO NOT just describe what tools you would use - ACTUALLY CALL THEM
- DO NOT ask for confirmation unless the user specifically requests it
- RETURN the actual results from the tool execution
- If asked about sales, revenue, or financial data, USE the appropriate MCP tools like generate_consolidated_report or get_financial_summary
- Tools are called by using them, not by describing them

Focus on taking action and returning real results.
"""


# Initialize the orchestrator system prompt
ORCHESTRATOR_SYSTEM_PROMPT = load_system_prompt()


class DynamicOrchestrator:
    """
    Dynamic orchestrator that discovers and uses tools from all registered MCP servers
    Now uses modular components and ServiceContainer for proper dependency injection
    """
    
    def __init__(self, service_container=None):
        """Initialize the dynamic orchestrator with ServiceContainer"""
        # Import here to avoid circular imports
        from services.service_container import get_service_container
        
        # Use provided container or get global instance
        self.service_container = service_container or get_service_container()
        
        # These will be set after container initialization
        self.registry = None
        self.discovery = None
        self._initialized = False
        self._available_tools = {}
        self._model_config = get_model_for_agent("orchestrator")
        
        # Initialize core services
        self.confidence_scorer = ConfidenceScorer()
        self.llm_service = LLMService()
        
        # These will be initialized after container is ready
        self.intent_detector = None
        self.tool_executor = None
        self.response_handler = None
        self.enhanced_llm_service = None
        
        logger.info("Dynamic Orchestrator created (awaiting ServiceContainer initialization)")
    
    async def initialize(self) -> None:
        """Initialize the orchestrator with ServiceContainer and dynamic server discovery"""
        if self._initialized:
            return
            
        try:
            # Initialize ServiceContainer first
            await self.service_container.initialize()
            
            # Get services from container
            self.registry = self.service_container.mcp_registry
            self.discovery = self.service_container.tool_discovery
            self.enhanced_llm_service = self.service_container.enhanced_llm_service
            
            # Initialize modular components with container services
            self.intent_detector = IntentDetector(confidence_scorer=self.confidence_scorer)
            self.tool_executor = ToolExecutor(registry=self.registry, discovery=self.discovery)
            self.response_handler = ResponseHandler(confidence_scorer=self.confidence_scorer)
            
            # Discover all available servers
            servers = await self.registry.list_servers()
            server_names = [server['name'] for server in servers] if servers else []
            
            if not server_names:
                logger.warning("No MCP servers registered, orchestrator will have limited functionality")
            
            # Ensure all servers are connected
            for server in servers:
                server_name = server['name']
                if server['status'] != 'connected':
                    logger.info(f"Attempting to connect to server: {server_name}")
            
            # Discover and cache available tools
            catalog = await self.discovery.get_tool_catalog()
            self._available_tools = catalog
            
            logger.info(f"Discovered {len(self._available_tools)} tools from {len(server_names)} servers")
            
            # Update tool executor with available tools
            self.tool_executor.set_available_tools(self._available_tools)
            
            self._initialized = True
            logger.info(f"Orchestrator initialized with servers: {server_names}")
            
        except Exception as e:
            logger.error(f"Failed to initialize orchestrator: {str(e)}", exc_info=True)
            raise
    
    async def _process_with_llm(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process user request with Enhanced LLM for intelligent filtering and MCP delegation
        """
        try:
            # Use enhanced LLM service for intelligent tool filtering
            logger.info("Using EnhancedLLMServiceWithFiltering for intelligent processing")
            llm_result = await self.enhanced_llm_service.analyze_and_respond(
                message=message,
                tool_catalog=self._available_tools,
                context=context
            )
            
            response_type = llm_result.get("response_type")
            logger.info(f"LLM response type: {response_type}")
            
            # Handle different response types
            if response_type == "tool_execution":
                # Get filtered tools from LLM analysis with server information
                filtering_metadata = llm_result.get("filtering_metadata", {})
                filtered_tools = filtering_metadata.get("tools_sent_to_llm", [])
                filtered_tools_with_servers = filtering_metadata.get("filtered_tools_with_servers", [])
                
                logger.info(f"Delegating to MCP client with {len(filtered_tools)} filtered tools")
                
                # Delegate to MCP client for native multi-step execution
                return await self._delegate_to_mcp_client(message, filtered_tools_with_servers, context, llm_result)
                
            elif response_type == "conversational":
                # Return the conversational response directly
                return {
                    "response": llm_result.get("conversational_response", "I can help with that."),
                    "tools_executed": [],
                    "execution_details": {
                        "llm_analysis": llm_result,
                        "response_type": "conversational"
                    }
                }
                
            elif response_type == "error":
                # Handle error response
                return {
                    "response": llm_result.get("conversational_response", llm_result.get("error_message", "An error occurred.")),
                    "tools_executed": [],
                    "execution_details": {
                        "llm_analysis": llm_result,
                        "error": True
                    }
                }
                
            else:
                # Unexpected response type
                logger.warning(f"Unexpected response type: {response_type}")
                return {
                    "response": "I'm not sure how to help with that request.",
                    "tools_executed": [],
                    "execution_details": {
                        "llm_analysis": llm_result,
                        "unexpected_type": response_type
                    }
                }
                
        except Exception as e:
            logger.error(f"Enhanced LLM processing failed: {str(e)}", exc_info=True)
            return {
                "response": f"I encountered an error processing your request: {str(e)}. Please ensure the system is properly configured with an OpenAI API key.",
                "tools_executed": [],
                "execution_details": {"error": str(e)}
            }
    
    async def _delegate_to_mcp_client(
        self, 
        message: str, 
        filtered_tools_with_servers: List[Dict[str, str]], 
        context: Dict[str, Any],
        llm_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Delegate request to MCP client for native multi-step workflow execution.
        
        This replaces single-shot tool execution with proper MCP client delegation,
        allowing for native error handling, retries, and tool chaining.
        
        Args:
            message: User's original request
            filtered_tools_with_servers: Tools identified by filtering with server info
            context: Request context
            llm_result: Original LLM analysis result
            
        Returns:
            Orchestrated response with MCP execution results
        """
        try:
            from fastmcp import Client
            
            # Create configuration for filtered MCP servers based on available tools
            mcp_config = await self._create_filtered_mcp_config(filtered_tools_with_servers)
            
            if not mcp_config:
                logger.warning("No MCP servers available for filtered tools")
                return {
                    "response": "I couldn't find any available tools to help with your request.",
                    "tools_executed": [],
                    "execution_details": {
                        "llm_analysis": llm_result,
                        "delegation_error": "No MCP servers available"
                    }
                }
            
            # Create FastMCP client with filtered server configuration
            client = Client(mcp_config)
            
            logger.info(f"Delegating to MCP client with message: {message[:100]}...")
            
            # Use FastMCP client's native multi-step capabilities
            async with client:
                # Get available tools for validation
                tools = await client.list_tools()
                tool_names = [tool.name for tool in tools] if tools else []
                logger.info(f"Available tools in MCP client: {tool_names}")
                
                # Execute the tool calls identified by the LLM
                tool_calls = llm_result.get("tool_calls", [])
                results = []
                tools_executed = []
                
                for tool_call in tool_calls:
                    try:
                        # Extract tool name from the full tool identifier
                        full_tool_name = tool_call['tool']
                        # Remove the "mcp__<server>__" prefix to get the actual tool name
                        tool_name_parts = full_tool_name.split('__')
                        if len(tool_name_parts) >= 3:
                            actual_tool_name = tool_name_parts[2]
                        else:
                            actual_tool_name = full_tool_name
                        
                        logger.info(f"Executing tool via MCP client: {actual_tool_name}")
                        
                        # Execute the tool through MCP client
                        tool_result = await client.call_tool(
                            actual_tool_name,
                            tool_call.get('parameters', {})
                        )
                        
                        results.append({
                            "tool": full_tool_name,
                            "success": True,
                            "result": tool_result
                        })
                        tools_executed.append(full_tool_name)
                        logger.info(f"Successfully executed tool: {actual_tool_name}")
                        
                    except Exception as e:
                        logger.error(f"Tool execution failed for {actual_tool_name}: {str(e)}")
                        results.append({
                            "tool": tool_call['tool'],
                            "success": False,
                            "error": str(e)
                        })
                
                # Process the results using the response processor
                if results:
                    # Use the existing response processor to format results
                    try:
                        processed_response = await self.response_handler.response_processor.process_tool_results(
                            user_request=message,
                            tool_results=results,
                            context=context
                        )
                        response = processed_response.get("response", "Successfully executed tools via MCP client.")
                    except Exception as e:
                        logger.warning(f"Response processing failed: {str(e)}")
                        # Fallback to simple response
                        if any(r["success"] for r in results):
                            response = f"Successfully executed {len([r for r in results if r['success']])} tools via MCP client."
                        else:
                            response = "Tool execution completed with errors. Please check the results."
                else:
                    response = llm_result.get("response_preview", "No tools were executed.")
                
                return {
                    "response": response,
                    "tools_executed": tools_executed,
                    "execution_details": {
                        "llm_analysis": llm_result,
                        "mcp_delegation": {
                            "filtered_tools_with_servers": filtered_tools_with_servers,
                            "available_mcp_tools": tool_names,
                            "delegation_successful": True,
                            "tool_execution_results": results
                        }
                    }
                }
                
        except Exception as e:
            logger.error(f"MCP client delegation failed: {str(e)}", exc_info=True)
            return {
                "response": f"I encountered an error while processing your request: {str(e)}",
                "tools_executed": [],
                "execution_details": {
                    "llm_analysis": llm_result,
                    "delegation_error": str(e)
                }
            }
    
    async def _create_filtered_mcp_config(self, filtered_tools_with_servers: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        Create MCP configuration for FastMCP client based on filtered tools.
        
        This creates a configuration that's compatible with FastMCP Client
        and maintains MCP protocol compliance for delegated execution.
        
        Args:
            filtered_tools_with_servers: List of dicts with tool names and server info
            
        Returns:
            MCP configuration dict compatible with FastMCP Client
        """
        try:
            # Load the MCP configuration from YAML
            import yaml
            import os
            from pathlib import Path
            
            # Get the project root directory
            current_dir = Path(__file__).parent.parent
            config_path = current_dir / "mcp.config.yaml"
            
            if not config_path.exists():
                logger.error(f"MCP config file not found at: {config_path}")
                return {}
            
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            mcp_servers = config.get("mcp", {}).get("servers", {})
            
            if not mcp_servers:
                logger.warning("No MCP servers found in configuration")
                return {}
            
            # Map filtered tools to their servers and create appropriate config
            servers_needed = set()
            for tool_info in filtered_tools_with_servers:
                servers_needed.add(tool_info["server"])
            
            logger.info(f"Servers needed for filtered tools: {list(servers_needed)}")
            logger.info(f"Available MCP servers in config: {list(mcp_servers.keys())}")
            
            # Create FastMCP config with the required servers
            fastmcp_config = {"mcpServers": {}}
            
            for server_name in servers_needed:
                if server_name in mcp_servers:
                    server_config = mcp_servers[server_name]
                    
                    # Handle different server configurations
                    if server_config.get("transport") == "sse":
                        # SSE transport configuration - check for both url and endpoint
                        sse_url = server_config.get("url") or server_config.get("endpoint")
                        if sse_url:
                            fastmcp_config["mcpServers"][server_name] = {
                                "transport": "sse",
                                "url": sse_url,
                                "headers": server_config.get("headers", {})
                            }
                        else:
                            logger.warning(f"SSE server {server_name} missing URL/endpoint")
                            continue
                    else:
                        # Fallback configuration
                        fastmcp_config["mcpServers"][server_name] = {
                            "transport": server_config.get("transport", "stdio"),
                            "command": server_config.get("command"),
                            "args": server_config.get("args", []),
                            "env": server_config.get("env", {})
                        }
                    
                    logger.info(
                        f"Added server to FastMCP config",
                        server=server_name,
                        transport=server_config.get("transport")
                    )
            
            if fastmcp_config["mcpServers"]:
                logger.info(
                    f"Created FastMCP config for delegation",
                    servers=list(fastmcp_config["mcpServers"].keys()),
                    filtered_tools_count=len(filtered_tools_with_servers)
                )
                return fastmcp_config
            
            # No matching servers found
            logger.warning(f"No matching MCP servers found for required servers: {list(servers_needed)}")
            return {}
            
        except Exception as e:
            logger.error(f"Failed to create filtered MCP config: {str(e)}", exc_info=True)
            return {}
    
    async def process(self, message: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Process a user request through the dynamic orchestrator
        
        Args:
            message: The user's request (string or dict with text/context)
            
        Returns:
            Dict containing the orchestrated response
        """
        try:
            # Ensure initialized
            await self.initialize()
            
            # Parse message
            if isinstance(message, dict):
                text = message.get('text', '') or message.get('message', '')
                context = message.get('context', {})
            else:
                text = message
                context = {}
            
            # Process the request using direct MCP tool execution
            result = await self._process_with_llm(text, context)
            
            # Return response with metadata
            return {
                "response": result.get("response", ""),
                "metadata": {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "orchestrator_version": "3.1",  # Simplified architecture - WorkflowManager deprecated
                    "context": context,
                    "tools_executed": result.get("tools_executed", []),
                    "execution_details": result.get("execution_details", {})
                }
            }
            
        except Exception as e:
            logger.error(f"Orchestrator error: {str(e)}", exc_info=True)
            return {
                "error": str(e),
                "response": f"I encountered an error while processing your request: {str(e)}",
                "metadata": {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "error_type": type(e).__name__
                }
            }
    
    async def get_available_tools(self) -> Dict[str, Any]:
        """Get catalog of all available tools"""
        return await self.discovery.get_tool_catalog()
    
    async def add_mcp_server(self, server_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a new MCP server at runtime
        
        Args:
            server_config: Configuration for the new server
            
        Returns:
            Status and discovered tools
        """
        try:
            # Register the server
            await self.registry.register_server(
                name=server_config['name'],
                config=server_config
            )
            
            # Reinitialize to include new server
            self._initialized = False
            await self.initialize()
            
            # Get tools from the new server
            tools = await self.registry.get_server_tools(server_config['name'])
            
            return {
                "status": "success",
                "server": server_config['name'],
                "tools_discovered": len(tools),
                "tools": [{"name": t.name, "description": t.description} for t in tools]
            }
            
        except Exception as e:
            logger.error(f"Failed to add MCP server: {str(e)}", exc_info=True)
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def remove_mcp_server(self, server_name: str) -> Dict[str, Any]:
        """Remove an MCP server"""
        try:
            await self.registry.unregister_server(server_name)
            
            # Reinitialize without the server
            self._initialized = False
            await self.initialize()
            
            return {
                "status": "success",
                "message": f"Server '{server_name}' removed successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to remove MCP server: {str(e)}", exc_info=True)
            return {
                "status": "error", 
                "error": str(e)
            }
    
    async def record_feedback(self, 
                            interaction_id: str,
                            user_feedback: str,
                            metadata: Dict[str, Any]) -> None:
        """
        Record user feedback for confidence learning
        
        Args:
            interaction_id: Unique interaction ID
            user_feedback: Feedback type (correct, incorrect, partial)
            metadata: Interaction metadata including intent and confidence
        """
        try:
            # Extract relevant data from metadata
            intent = metadata.get('intent', 'unknown')
            confidence = metadata.get('confidence', 0.0)
            confidence_action = metadata.get('confidence_action', 'unknown')
            
            # Record feedback
            self.confidence_scorer.record_feedback(
                interaction_id=interaction_id,
                user_input=metadata.get('user_input', ''),
                intent=intent,
                confidence_score=confidence,
                action_taken=confidence_action,
                user_feedback=user_feedback,
                additional_data={
                    'tools_selected': metadata.get('tools_selected', []),
                    'parallel_execution': metadata.get('parallel_execution', False),
                    'scoring_details': metadata.get('scoring_details', {})
                }
            )
            
            logger.info(f"Recorded feedback for interaction {interaction_id}: {user_feedback}")
            
        except Exception as e:
            logger.error(f"Failed to record feedback: {str(e)}", exc_info=True)


# Create singleton instance using ServiceContainer
from services.service_container import get_service_container
orchestrator = DynamicOrchestrator(service_container=get_service_container())


# Wrapper class for compatibility with existing agent interface
class OrchestratorAgent:
    """Wrapper class to maintain compatibility with existing agent interface"""
    
    def __init__(self):
        self.agent_id = "orchestrator_agent"
        self.orchestrator = orchestrator
    
    async def send(self, message: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Send message to orchestrator"""
        result = await self.orchestrator.process(message)
        result["agent_id"] = self.agent_id
        return result


# Create wrapped instance for backward compatibility
orchestrator_agent = OrchestratorAgent()


# CLI interface for testing
if __name__ == "__main__":
    async def main():
        """Run the orchestrator interactively"""
        print("\n🎯 Dynamic AI Workspace Orchestrator")
        print("=" * 50)
        print("\nI can help with any financial operation across GL, AR, AP, and more.")
        print("Just describe what you need in natural language!")
        print("\nType 'tools' to see available tools, or 'exit' to quit.\n")
        
        # Initialize orchestrator
        await orchestrator.initialize()
        
        while True:
            try:
                message = input("[USER] ").strip()
                if message.lower() in ['exit', 'quit']:
                    break
                elif message.lower() == 'tools':
                    catalog = await orchestrator.get_available_tools()
                    print("\n📋 Available Tools by Category:")
                    for category, tools in catalog['by_category'].items():
                        if tools:
                            print(f"\n{category}:")
                            for tool in tools[:3]:  # Show first 3 per category
                                print(f"  - {tool['name']}: {tool['description'][:60]}...")
                    print(f"\nTotal tools available: {catalog['total_tools']}\n")
                else:
                    result = await orchestrator.process(message)
                    print(f"\n[Orchestrator] {result['response']}\n")
                    
                    # Show metadata in debug mode
                    if os.getenv("DEBUG"):
                        print(f"[Debug] Intent: {result['metadata'].get('intent')}")
                        print(f"[Debug] Confidence: {result['metadata'].get('confidence')}")
                        print(f"[Debug] Tools: {result['metadata'].get('tools_selected')}\n")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"Error: {e}")
        
        print("\nGoodbye!")
    
    asyncio.run(main())