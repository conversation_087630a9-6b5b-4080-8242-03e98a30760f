# Adding a New Business System Guide

This guide will walk you through adding a new business system (MCP server) to the AI Workspace Agents project. We'll use Sage Business Cloud Accounting (SBCA) as an example, but the same process applies to any business system like QuickBooks, Xero, etc.

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Step-by-Step Instructions](#step-by-step-instructions)
4. [Testing Your Implementation](#testing-your-implementation)
5. [Troubleshooting](#troubleshooting)
6. [Best Practices](#best-practices)

## Overview

Our architecture supports multiple business systems through:
- **MCP Servers**: External servers that connect to business systems
- **Agents**: AI-powered modules that handle specific business functions
- **Tool Mappers**: Translation layers between agent commands and MCP tools
- **Centralized Configuration**: Single source of truth for all MCP server configurations
- **REST/WebSocket API**: Frontend communication layer

### Architecture Flow
```
Frontend → REST/WebSocket API → Agent Manager → Agent → Tool Mapper → MCP Server → Business System
                                                    ↓
                                          Centralized MCP Config
```

### New Centralized Configuration System

As of the latest update, we use a centralized configuration system that follows the DRY principle:
- All MCP server configurations are defined in one place
- Agents automatically get their MCP server configuration
- Runtime switching between local/remote servers is supported
- No hardcoded server names in agent code

## Prerequisites

Before starting, ensure you have:
- Python 3.11+ installed
- The MCP server for your business system
- API credentials for the business system
- Basic understanding of Python async/await
- Familiarity with the project structure

## Step-by-Step Instructions

### Step 1: Create Directory Structure

First, create a new directory for your business system under `agents/`:

```bash
# Navigate to the project root
cd ai-workspace-agents

# Create directory for your business system
mkdir agents/sbca

# Create __init__.py file
touch agents/sbca/__init__.py
```

**Directory structure:**
```
agents/
├── base/           # Base classes (already exists)
├── intacct/        # Sage Intacct (already exists)
└── sbca/           # Your new business system
    └── __init__.py
```

### Step 2: Create the Tool Mapper

The tool mapper translates between agent tool names and MCP server tool names.

**Create `agents/sbca/sbca_tool_mapper.py`:**

```python
"""
SBCA Tool Mapper

Maps agent tool names to SBCA MCP server tool names.
"""

from typing import Dict, Optional, Any
from agents.base import BaseToolMapper


class SBCAToolMapper(BaseToolMapper):
    """
    Maps agent tool names to SBCA MCP server tool names.
    
    This handles the translation between what our agents expect
    and what the SBCA MCP server provides.
    """
    
    def _initialize_mappings(self):
        """Initialize the tool name mappings for SBCA MCP server"""
        
        # Define mappings from agent tool names to MCP tool names
        # Format: "agent_tool_name": "mcp_server_tool_name"
        self._tool_map = {
            # Sales Module Tools
            "sbca-sales.create_invoice": "create_sales_invoice",
            "sbca-sales.get_invoice": "get_sales_invoice_by_id",
            "sbca-sales.list_invoices": "get_sales_invoices",
            "sbca-sales.create_quote": "create_sales_quote",
            "sbca-sales.get_customer": "get_contact_by_id",
            "sbca-sales.list_customers": "get_contacts",
            
            # Inventory Module Tools
            "sbca-inventory.get_stock": "get_stock_items",
            "sbca-inventory.update_stock": "update_stock_item",
            "sbca-inventory.create_product": "create_product",
            "sbca-inventory.get_product": "get_product_by_id",
            
            # Common Tools
            "sbca-common.search": "search_all_records",
            "sbca-common.get_summary": "get_business_summary",
        }
        
        # Mark tools that are not yet available in the MCP server
        self._unavailable_tools = {
            "sbca-sales.bulk_invoice": "Bulk invoice creation not yet supported",
            "sbca-inventory.stock_forecast": "Stock forecasting coming soon",
        }
        
        # Parameter mappings if MCP server uses different parameter names
        self.param_mappings = {
            "get_contacts": {
                "customer_type": "contact_type_id",  # Agent uses 'customer_type', MCP uses 'contact_type_id'
                "search_term": "search",
            },
            "create_sales_invoice": {
                "customer_id": "contact_id",
                "line_items": "invoice_lines",
            }
        }
    
    def get_tool_description(self, agent_tool_name: str) -> Optional[Dict[str, Any]]:
        """Get detailed description of a tool's capabilities"""
        
        descriptions = {
            # Sales Tools
            "sbca-sales.create_invoice": "Create a new sales invoice in SBCA",
            "sbca-sales.get_invoice": "Retrieve a specific invoice by ID",
            "sbca-sales.list_invoices": "List all sales invoices with filtering",
            "sbca-sales.create_quote": "Create a new sales quote",
            "sbca-sales.get_customer": "Get detailed customer information",
            "sbca-sales.list_customers": "List all customers with search",
            
            # Inventory Tools
            "sbca-inventory.get_stock": "Get current stock levels",
            "sbca-inventory.update_stock": "Update stock quantities",
            "sbca-inventory.create_product": "Create a new product",
            "sbca-inventory.get_product": "Get product details",
            
            # Common Tools
            "sbca-common.search": "Search across all SBCA modules",
            "sbca-common.get_summary": "Get business summary dashboard",
        }
        
        description = descriptions.get(agent_tool_name, "Tool description not available")
        
        return {
            "name": agent_tool_name,
            "description": description,
            "available": self.is_tool_available(agent_tool_name),
            "mcp_tool": self._tool_map.get(agent_tool_name)
        }
    
    def map_parameters(self, mcp_tool: str, agent_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Map agent parameters to MCP server parameters.
        
        Override this if your MCP server uses different parameter names.
        """
        if mcp_tool not in self.param_mappings:
            return agent_params
        
        mappings = self.param_mappings[mcp_tool]
        mcp_params = {}
        
        for agent_key, agent_value in agent_params.items():
            if agent_key in mappings:
                mcp_key = mappings[agent_key]
                mcp_params[mcp_key] = agent_value
            else:
                mcp_params[agent_key] = agent_value
        
        return mcp_params


# Create a singleton instance
sbca_tool_mapper = SBCAToolMapper("sbca")
```

### Step 3: Create Your First Agent

You have two options for creating agents:

#### Option A: Using Centralized Configuration (Recommended)

With the centralized configuration system, agents become much simpler:

```python
"""
SBCA Sales Agent using Centralized Configuration

No hardcoded MCP server names or manual tool mapper initialization!
"""

from typing import Dict, Any, Optional
from datetime import datetime, timezone
from agents.base.configured_agent import ConfiguredAgent
import structlog

logger = structlog.get_logger(__name__)


class SBCASalesAgent(ConfiguredAgent):
    """SBCA Sales Agent with automatic MCP configuration"""
    
    def __init__(self):
        instruction = """You are an expert SBCA Sales Agent responsible for:
        
        1. Creating and managing sales invoices
        2. Creating and managing quotes
        3. Customer relationship management
        4. Sales reporting and analytics
        
        Always be helpful and provide clear explanations of what you're doing.
        When creating invoices or quotes, confirm details with the user.
        Format monetary amounts appropriately."""
        
        # That's it! MCP server and tool mapper configured automatically
        super().__init__(
            agent_id="sbca_sales_agent",
            name="SBCA Sales Agent",
            instruction=instruction
        )
    
    async def gather_data(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Gather sales data for orchestrator workflows"""
        data_type = params.get("type", "summary")
        
        # Use the automatically configured tool mapper
        if self.tool_mapper:
            tool_name = self.tool_mapper.map_tool_name("sbca.sales_summary")
            # Implementation here
        
        return {"data": "Sales data"}


# Create singleton instance
sbca_sales_agent = SBCASalesAgent()
```

#### Option B: Traditional Approach

Let's create a Sales Agent for SBCA using the traditional approach:

**Create `agents/sbca/sales_agent.py`:**

```python
"""
SBCA Sales Agent

Handles sales operations including invoicing, quotes, and customer management.
"""

import os
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
import structlog
from mcp_agent.core.fastagent import FastAgent
from agents.base import BaseAgent, AgentConfig
from .sbca_tool_mapper import sbca_tool_mapper

# Set up logging
logger = structlog.get_logger(__name__)

# Get the project root directory (three levels up from sbca/)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
config_file = os.path.join(project_root, "fastagent.config.yaml")

# Create FastAgent instance
fast = FastAgent("SBCA Sales Agent", config_path=config_file)


@fast.agent(
    name="sbca_sales_agent",
    instruction="""You are an expert SBCA Sales Agent responsible for:
    
    1. Creating and managing sales invoices
    2. Creating and managing quotes
    3. Customer relationship management
    4. Sales reporting and analytics
    
    Always be helpful and provide clear explanations of what you're doing.
    When creating invoices or quotes, confirm details with the user.
    Format monetary amounts appropriately.
    """,
    servers=["sbca"],  # Reference to MCP server in config
    model="claude-3-sonnet-********",
    use_history=True,
    human_input=False,
)
class SBCASalesAgent(BaseAgent):
    """
    SBCA Sales Agent implementation.
    
    Handles all sales-related operations in Sage Business Cloud Accounting.
    """
    
    def __init__(self):
        config = AgentConfig(
            agent_id="sbca_sales_agent",
            name="SBCA Sales Agent",
            description="Handles sales operations in Sage Business Cloud Accounting",
            mcp_server="sbca",
            capabilities=[
                "Create sales invoices",
                "Create sales quotes", 
                "Manage customers",
                "View sales reports",
                "Process refunds",
                "Handle credit notes"
            ],
            metadata={
                "module": "sales",
                "version": "1.0.0"
            }
        )
        super().__init__(config)
        self.tool_mapper = sbca_tool_mapper
        self._initialized = True
    
    async def initialize(self) -> None:
        """Initialize the agent (already done in __init__)"""
        pass
    
    def get_tool_mapper(self):
        """Get the tool mapper for this agent"""
        return self.tool_mapper
    
    def _analyze_intent(self, message: str) -> Dict[str, Any]:
        """
        Analyze the intent of the user's message.
        
        This helps route the request to the appropriate functionality.
        """
        message_lower = message.lower()
        
        # Invoice-related intents
        if any(word in message_lower for word in ['invoice', 'bill', 'charge']):
            if 'create' in message_lower or 'new' in message_lower:
                return {"type": "create_invoice", "confidence": 0.9}
            elif 'list' in message_lower or 'show' in message_lower:
                return {"type": "list_invoices", "confidence": 0.9}
            else:
                return {"type": "invoice_query", "confidence": 0.8}
        
        # Quote-related intents
        if any(word in message_lower for word in ['quote', 'estimate', 'proposal']):
            if 'create' in message_lower or 'new' in message_lower:
                return {"type": "create_quote", "confidence": 0.9}
            else:
                return {"type": "quote_query", "confidence": 0.8}
        
        # Customer-related intents
        if any(word in message_lower for word in ['customer', 'client', 'contact']):
            if 'add' in message_lower or 'create' in message_lower:
                return {"type": "create_customer", "confidence": 0.9}
            else:
                return {"type": "customer_query", "confidence": 0.8}
        
        # Sales report intents
        if any(word in message_lower for word in ['report', 'summary', 'analytics']):
            return {"type": "sales_report", "confidence": 0.8}
        
        return {"type": "general", "confidence": 0.5}
    
    def _enhance_message(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Enhance the message with additional context.
        
        Adds relevant information to help the agent provide better responses.
        """
        enhanced = super()._enhance_message(message, context)
        
        # Add SBCA-specific context
        enhanced += "\n\nSBCA Context:"
        enhanced += "\n- Default currency: GBP"  # Or from config
        enhanced += "\n- VAT enabled: Yes"
        enhanced += "\n- Business type: Small Business"
        
        return enhanced
    
    async def send(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Send a message to the agent and get a response.
        
        This is the main entry point for agent communication.
        """
        try:
            # Analyze intent
            intent = self._analyze_intent(message)
            logger.info(f"SBCA Sales Agent intent: {intent}")
            
            # Enhance message with context
            enhanced_message = self._enhance_message(message, context)
            
            # Call the decorated agent function
            response = await sbca_sales_agent_func(enhanced_message)
            
            # Format the response
            return self._format_response(response, intent)
            
        except Exception as e:
            logger.error(f"SBCA Sales Agent error: {e}", exc_info=True)
            return {
                "content": f"I encountered an error: {str(e)}",
                "metadata": {
                    "error": True,
                    "error_type": type(e).__name__
                },
                "error": str(e)
            }
    
    def _format_response(self, response: str, intent: Dict[str, Any]) -> Dict[str, Any]:
        """Format the response with SBCA-specific metadata"""
        result = super()._format_response(response, intent)
        
        # Add SBCA-specific metadata
        result["metadata"]["business_system"] = "SBCA"
        result["metadata"]["module"] = "sales"
        
        # Add suggested actions based on intent
        if intent["type"] == "create_invoice":
            result["metadata"]["suggested_actions"] = [
                "View invoice",
                "Send to customer",
                "Create credit note"
            ]
        elif intent["type"] == "customer_query":
            result["metadata"]["suggested_actions"] = [
                "View invoices",
                "Create invoice",
                "View statement"
            ]
        
        return result


# Get the decorated function reference
sbca_sales_agent_func = SBCASalesAgent.__dict__["send"].__wrapped__

# Create agent instance
sbca_sales_agent_instance = SBCASalesAgent()
```

### Step 4: Update the __init__.py File

Update `agents/sbca/__init__.py` to export your agents:

```python
"""
SBCA agents module.

Contains all Sage Business Cloud Accounting specific agents.
"""

from .sales_agent import sbca_sales_agent_instance, SBCASalesAgent
from .sbca_tool_mapper import SBCAToolMapper, sbca_tool_mapper

__all__ = [
    'sbca_sales_agent_instance',
    'SBCASalesAgent',
    'SBCAToolMapper',
    'sbca_tool_mapper'
]

# You can add more agents as you create them:
# from .inventory_agent import sbca_inventory_agent_instance, SBCAInventoryAgent
# from .accounting_agent import sbca_accounting_agent_instance, SBCAAccountingAgent
```

### Step 5: Configure the MCP Server

#### Option A: Using Centralized Configuration (Recommended)

1. Add your MCP server configuration to `fastagent.config.yaml`:

```yaml
# Add this to the mcp_servers section
sbca:
  command: python
  args: ["-m", "sage_sbca_mcp.main", "--mode", "production"]
  cwd: "C:\\Users\\<USER>\\Documents\\GitHub\\sage-sbca-mcp-server"
  env:
    # OAuth Configuration
    SBCA_CLIENT_ID: ${SBCA_CLIENT_ID}
    SBCA_CLIENT_SECRET: ${SBCA_CLIENT_SECRET}
    SBCA_REDIRECT_URI: ${SBCA_REDIRECT_URI:http://localhost:8081/callback}
    SBCA_COUNTRY: ${SBCA_COUNTRY:GB}
    
    # Python path
    PYTHONPATH: "C:\\Users\\<USER>\\Documents\\GitHub\\sage-sbca-mcp-server"
  
  health_check:
    enabled: true
    interval: 30
    timeout: 10
```

2. Update `config/mcp_config.py` to register your business system:

```python
# In the _load_configuration method, add:
"sbca": BusinessSystemConfig(
    system_name="sbca",
    mcp_server_name="sbca",  # References the MCP server above
    agents=["sbca_sales_agent", "sbca_inventory_agent", "sbca_accounting_agent"],
    tool_mapper_class="agents.sbca.sbca_tool_mapper.SBCAToolMapper",
    is_composite=True
),
```

Now your agents will automatically get the correct MCP server configuration!

#### Option B: Legacy Configuration (for reference)

If not using centralized configuration, add to the agents section:

```yaml
# Add this to the agents section
sbca_sales_agent:
  name: "SBCA Sales Agent"
  mcp_servers: ["sbca"]
  timeout: 30
  capabilities:
    - "Sales invoice creation"
    - "Quote management"
    - "Customer management"
    - "Sales reporting"
```

### Step 6: Add Environment Variables

Create or update your `.env` file with SBCA credentials:

```bash
# SBCA OAuth Credentials
SBCA_CLIENT_ID=your_client_id_here
SBCA_CLIENT_SECRET=your_client_secret_here
SBCA_REDIRECT_URI=http://localhost:8081/callback
SBCA_COUNTRY=GB
```

### Step 7: Register Your Agent

The agent will be automatically discovered, but you can also manually register it.

**Option 1: Automatic Discovery (Recommended)**

The `AgentDiscovery` system will find your agent automatically when the API service starts.

**Option 2: Manual Registration**

Add to `services/agent_registration.py`:

```python
# Import your agent
try:
    from agents.sbca import sbca_sales_agent_instance
    SBCA_AGENTS_AVAILABLE = True
except ImportError:
    SBCA_AGENTS_AVAILABLE = False
    sbca_sales_agent_instance = None

# In register_all_agents() function, add:
if SBCA_AGENTS_AVAILABLE and sbca_sales_agent_instance:
    await manager.register_agent(
        agent_id="sbca_sales_agent",
        name="SBCA Sales Agent",
        description="Handles sales operations in SBCA",
        handler=create_agent_handler(sbca_sales_agent_instance),
        capabilities=sbca_sales_agent_instance.config.capabilities,
        metadata={
            "module": "sales",
            "mcp_server": "sbca",
            "model": "claude-3-sonnet-********"
        }
    )
    logger.info("SBCA Sales Agent registered successfully")
```

## Testing Your Implementation

### Step 1: Unit Tests

Create `tests/agents/test_sbca_sales_agent.py`:

```python
"""
Tests for SBCA Sales Agent
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from agents.sbca.sales_agent import SBCASalesAgent, sbca_sales_agent_instance


class TestSBCASalesAgent:
    """Test suite for SBCA Sales Agent"""
    
    @pytest.fixture
    def agent(self):
        """Create agent instance for testing"""
        return SBCASalesAgent()
    
    def test_agent_initialization(self, agent):
        """Test agent initializes correctly"""
        assert agent.config.agent_id == "sbca_sales_agent"
        assert agent.config.mcp_server == "sbca"
        assert "Create sales invoices" in agent.config.capabilities
    
    def test_analyze_intent_create_invoice(self, agent):
        """Test intent analysis for invoice creation"""
        intent = agent._analyze_intent("Create a new invoice for ABC Company")
        assert intent["type"] == "create_invoice"
        assert intent["confidence"] >= 0.9
    
    def test_analyze_intent_customer_query(self, agent):
        """Test intent analysis for customer queries"""
        intent = agent._analyze_intent("Show me customer details for ABC Company")
        assert intent["type"] == "customer_query"
        assert intent["confidence"] >= 0.8
    
    @pytest.mark.asyncio
    async def test_send_message(self, agent):
        """Test sending a message to the agent"""
        # Mock the fast-agent response
        with patch.object(agent, 'send') as mock_send:
            mock_send.return_value = {
                "content": "I'll create that invoice for you.",
                "metadata": {"intent_type": "create_invoice"},
                "error": None
            }
            
            response = await mock_send("Create an invoice for $1000")
            
            assert response["content"] is not None
            assert response["error"] is None
            assert "intent_type" in response["metadata"]

# Run with: pytest tests/agents/test_sbca_sales_agent.py
```

### Step 2: Integration Test

Create a simple test script to verify everything works:

```python
"""
Test SBCA integration
"""

import asyncio
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.agent_manager import get_agent_manager
from services.agent_registration import register_all_agents


async def test_sbca_agent():
    """Test SBCA agent integration"""
    
    # Register all agents
    await register_all_agents()
    
    # Get agent manager
    manager = get_agent_manager()
    
    # Test SBCA agent
    response = await manager.send_message(
        "sbca_sales_agent",
        "Show me today's sales invoices",
        metadata={"user_id": "test_user"}
    )
    
    print(f"Response: {response.content}")
    print(f"Status: {response.status}")
    print(f"Metadata: {response.metadata}")


if __name__ == "__main__":
    asyncio.run(test_sbca_agent())
```

### Step 3: API Testing

Start the API service and test via REST:

```bash
# Start the API service
python services/api_service.py

# In another terminal, test with curl:
curl -X POST http://localhost:8000/agents/sbca_sales_agent/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Create an invoice for ABC Company for $500",
    "context": {"user_id": "test_user"}
  }'
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Import Errors
**Problem:** `ModuleNotFoundError: No module named 'agents.sbca'`

**Solution:** 
- Ensure you created `__init__.py` in the sbca directory
- Check that you're in the correct directory
- Verify Python path includes the project root

#### 2. MCP Server Connection Failed
**Problem:** Agent can't connect to MCP server

**Solution:**
- Verify MCP server is running
- Check credentials in environment variables
- Ensure config path in fastagent.config.yaml is correct
- Check MCP server logs for errors

#### 3. Tool Mapping Issues
**Problem:** Agent says tool is not available

**Solution:**
- Check tool name in tool mapper matches MCP server
- Verify tool is not in `_unavailable_tools`
- Use MCP server documentation to confirm tool names

#### 4. Agent Not Found
**Problem:** API returns "Agent not found"

**Solution:**
- Ensure agent is registered (check logs)
- Verify agent_id matches in all places
- Restart API service after adding new agent

### Debugging Tips

1. **Enable Debug Logging:**
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

2. **Check Agent Registration:**
```python
# In Python console
from services.agent_manager import get_agent_manager
manager = get_agent_manager()
agents = await manager.list_agents()
print([agent.id for agent in agents])
```

3. **Test Tool Mapper:**
```python
from agents.sbca.sbca_tool_mapper import sbca_tool_mapper
print(sbca_tool_mapper.get_available_tools())
```

## Best Practices

### 1. Naming Conventions

- **Agent IDs:** `{system}_{function}_agent` (e.g., `sbca_sales_agent`)
- **Tool Names:** `{system}-{module}.{action}` (e.g., `sbca-sales.create_invoice`)
- **File Names:** Use snake_case (e.g., `sales_agent.py`)
- **Class Names:** Use PascalCase (e.g., `SBCASalesAgent`)

### 2. Error Handling

Always handle errors gracefully:

```python
try:
    response = await some_mcp_operation()
except MCPError as e:
    logger.error(f"MCP error: {e}")
    return {
        "content": "I encountered an issue with the business system.",
        "error": str(e)
    }
except Exception as e:
    logger.error(f"Unexpected error: {e}", exc_info=True)
    return {
        "content": "An unexpected error occurred.",
        "error": str(e)
    }
```

### 3. Documentation

Always document:
- What each agent does
- Available tools and their purposes
- Any business logic or special behaviors
- Integration requirements

### 4. Testing

- Write unit tests for intent analysis
- Test tool mapping thoroughly
- Create integration tests for end-to-end flows
- Test error scenarios

### 5. Security

- Never hardcode credentials
- Use environment variables
- Validate all inputs
- Follow principle of least privilege

## Next Steps

After successfully adding your first agent:

1. **Add More Agents:** Create inventory, accounting, or other agents
2. **Enhance Tool Mapping:** Add more tools as needed
3. **Create Workflows:** Build multi-agent workflows
4. **Add Frontend Integration:** Connect to the web app
5. **Monitor Performance:** Add metrics and logging

## Getting Help

If you need help:

1. Check existing agents (intacct) for examples
2. Review base classes for available methods
3. Check MCP server documentation
4. Ask the team for guidance

Remember: The goal is to create agents that are:
- Easy to understand
- Well-documented  
- Properly tested
- Following established patterns

Good luck with your implementation! 🚀
