# Tool Inventory for AI Workspace Agents

## Overview
This document provides a comprehensive inventory of all tools currently used by the AI Workspace agents and their mapping to MCP server tools.

**Generated**: June 3, 2025  
**Purpose**: Support migration from static agents to dynamic orchestration

---

## Current Agent Architecture

### Active Agents
1. **GL Agent** (`gl_agent.py`) - General Ledger operations
2. **AR Agent** (`ar_agent.py`) - Accounts Receivable operations  
3. **AP Agent** (`ap_agent.py`) - Accounts Payable operations
4. **Analysis Agent** (`analysis_agent.py`) - Financial analysis
5. **Report Agent** (`report_agent.py`) - Report generation
6. **Validation Agent** (`validation_agent.py`) - Data validation
7. **Orchestrator Agent** (`orchestrator_agent.py`) - Workflow coordination

---

## MCP Server Tools Available

Based on the Sage Intacct MCP server analysis:

### Core Tools
- `search_across_modules` - Search across multiple Intacct modules
- `get_financial_summary` - Get financial summary across modules
- `execute_month_end_close` - Execute month-end close procedures
- `generate_consolidated_report` - Generate consolidated reports
- `list_enabled_modules` - List all enabled modules
- `health_check` - Check server health status

### Module Status
Currently available modules in the MCP server:
- **AP** - Accounts Payable (not enabled)
- **AR** - Accounts Receivable (not enabled)  
- **GL** - General Ledger (not enabled)

⚠️ **Note**: All modules show as available but not enabled. Need to investigate module enablement.

---

## Agent to MCP Tool Mapping

### GL Agent Tools
**Current Agent Methods**:
- `get_account_balances()`
- `get_trial_balance()`
- `create_journal_entry()`
- `get_financial_statements()`
- `get_gl_transactions()`

**MCP Tools Required**:
- GL module tools (need to enable GL module)
- `get_financial_summary` (partial coverage)
- `generate_consolidated_report` (for statements)

### AR Agent Tools
**Current Agent Methods**:
- `get_customer_balances()`
- `create_invoice()`
- `apply_payment()`
- `get_aging_report()`
- `get_customer_details()`

**MCP Tools Required**:
- AR module tools (need to enable AR module)
- `search_across_modules` (for customer search)

### AP Agent Tools
**Current Agent Methods**:
- `get_vendor_balances()`
- `create_bill()`
- `process_payment()`
- `get_ap_aging()`
- `get_vendor_details()`

**MCP Tools Required**:
- AP module tools (need to enable AP module)
- `search_across_modules` (for vendor search)

### Analysis Agent Tools
**Current Agent Methods**:
- `calculate_ratios()`
- `variance_analysis()`
- `trend_analysis()`
- `budget_comparison()`

**MCP Tools Required**:
- `get_financial_summary`
- `generate_consolidated_report`
- Custom calculation logic (may need wrapper)

### Report Agent Tools
**Current Agent Methods**:
- `generate_financial_statements()`
- `create_executive_summary()`
- `format_reports()`

**MCP Tools Required**:
- `generate_consolidated_report`
- Custom formatting logic (may need wrapper)

### Validation Agent Tools
**Current Agent Methods**:
- `check_period_status()`
- `validate_balances()`
- `check_data_integrity()`

**MCP Tools Required**:
- `execute_month_end_close` (dry run mode)
- Module-specific validation tools

---

## Tool Dependencies and Execution Patterns

### Common Patterns
1. **Sequential Dependencies**: Many operations require checking period status first
2. **Data Aggregation**: Multiple tool calls often needed to gather complete data
3. **Cross-Module Operations**: Financial statements require data from GL, AR, and AP

### Critical Dependencies
- Period validation before any posting operations
- Balance verification before journal entries
- Customer/vendor existence checks before transactions

---

## Gaps and Requirements

### Missing MCP Tools
1. **Module-specific tools not accessible** - Need to enable GL, AR, AP modules
2. **Calculation tools** - Financial ratios, variance analysis
3. **Formatting tools** - Report generation and formatting
4. **Validation tools** - Period checks, balance validations

### Required Mappings
1. **Tool Name Mapping Layer** - Agent method names differ from MCP tool names
2. **Parameter Translation** - Agent parameters may differ from MCP parameters
3. **Response Transformation** - Standardize responses across tools

---

## Next Steps
1. Enable GL, AR, and AP modules in the MCP server
2. Create comprehensive tool discovery after module enablement
3. Build mapping layer for tool name translation
4. Implement wrappers for missing functionality
