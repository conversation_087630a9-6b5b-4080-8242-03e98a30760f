# Multi-MCP Server Architecture Guide

## Overview

This architecture supports multiple business systems (MCP servers) with their dedicated agents while maintaining clean separation and scalability.

## Directory Structure

```
ai-workspace-agents/
├── agents/
│   ├── base/                     # Base classes for all agents
│   │   ├── base_agent.py         # Abstract base agent class
│   │   └── tool_mapper.py        # Abstract tool mapper class
│   │
│   ├── intacct/                  # Sage Intacct agents
│   │   ├── gl_agent.py
│   │   ├── ar_agent.py
│   │   ├── ap_agent.py
│   │   └── intacct_tool_mapper.py
│   │
│   ├── sbca/                     # Sage SBCA agents
│   │   ├── sales_agent.py
│   │   ├── inventory_agent.py
│   │   ├── customer_agent.py
│   │   └── sbca_tool_mapper.py
│   │
│   └── quickbooks/               # QuickBooks agents (example)
│       ├── accounting_agent.py
│       └── qb_tool_mapper.py
```

## Adding a New Business System

### 1. Create MCP Server Directory
```bash
mkdir agents/new_system
```

### 2. Create Tool Mapper
```python
# agents/new_system/new_system_tool_mapper.py
from agents.base import BaseToolMapper

class NewSystemToolMapper(BaseToolMapper):
    def _initialize_mappings(self):
        # Map agent tool names to MCP tool names
        self._tool_map = {
            "create_invoice": "new_system_create_sales_invoice",
            "get_balance": "new_system_get_account_balance",
            # ... more mappings
        }
        
        # Mark unavailable tools
        self._unavailable_tools = {"complex_report"}
```

### 3. Create Agent
```python
# agents/new_system/sales_agent.py
from agents.base import BaseAgent, AgentConfig
from .new_system_tool_mapper import NewSystemToolMapper

class NewSystemSalesAgent(BaseAgent):
    def __init__(self):
        config = AgentConfig(
            agent_id="new_system_sales",
            name="New System Sales Agent",
            description="Handles sales operations in New System",
            mcp_server="new_system",
            capabilities=[
                "Invoice creation",
                "Quote management"
            ]
        )
        super().__init__(config)
        self.tool_mapper = NewSystemToolMapper("new_system")
    
    async def send(self, message: str, context=None):
        # Agent implementation
        pass

# Create instance for discovery
new_system_sales_instance = NewSystemSalesAgent()
```

### 4. Update Configuration
```yaml
# fastagent.config.yaml
mcp_servers:
  new_system:
    command: python
    args: ["-m", "new_system_mcp.server"]
    cwd: "path/to/new-system-mcp-server"
    env:
      NEW_SYSTEM_API_KEY: ${NEW_SYSTEM_API_KEY}
    health_check:
      enabled: true

agents:
  new_system_sales:
    name: "New System Sales Agent"
    mcp_servers: ["new_system"]
    capabilities:
      - "Invoice creation"
      - "Quote management"
```

### 5. Register with Discovery
The agent discovery system will automatically find and register your new agents on startup.

## Benefits of This Architecture

1. **Clear Separation** - Each business system has its own directory
2. **Consistent Interface** - All agents inherit from BaseAgent
3. **Tool Mapping** - Each system can have different tool naming conventions
4. **Auto-Discovery** - New agents are automatically discovered and registered
5. **Configuration Management** - Single config file manages all MCP servers
6. **Scalability** - Easy to add new systems without modifying core code

## Cross-System Integration

For operations that span multiple systems:

```python
# agents/orchestrators/multi_system_orchestrator.py
class MultiSystemOrchestrator(BaseAgent):
    def __init__(self):
        config = AgentConfig(
            agent_id="multi_system_orchestrator",
            name="Multi-System Orchestrator",
            mcp_server="orchestrator",  # Can access multiple MCPs
            capabilities=[
                "Cross-system data sync",
                "Multi-system reporting"
            ]
        )
        super().__init__(config)
    
    async def sync_customer_data(self, customer_id):
        # Get data from Intacct
        intacct_data = await self.query_agent("ar_agent", f"get customer {customer_id}")
        
        # Update in SBCA
        await self.query_agent("sbca_customer_agent", f"update customer {customer_id} with {intacct_data}")
```

## Testing with Multiple MCP Servers

When running tests with actual MCP servers on Claude Desktop:

1. Start all MCP servers
2. Configure environment variables for each system
3. Run the API service
4. Test individual agents or cross-system workflows

## Environment Variables

Create separate `.env` files for each system:

```bash
# .env.intacct
INTACCT_CLIENT_ID=your_client_id
INTACCT_CLIENT_SECRET=your_secret

# .env.sbca
SBCA_CLIENT_ID=your_sbca_client_id
SBCA_CLIENT_SECRET=your_sbca_secret

# .env.quickbooks
QB_CLIENT_ID=your_qb_client_id
QB_CLIENT_SECRET=your_qb_secret
```

Load all environments when starting the service.
