"""
Dynamic MCP Configuration Manager

Extends the base MCP configuration with runtime management capabilities.
Supports adding/removing servers, updating endpoints, and persisting changes.
"""

import os
import yaml
import json
import asyncio
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from datetime import datetime
from pydantic import BaseModel, Field, ValidationError
import structlog
from threading import Lock

from .mcp_config import (
    MCPConfiguration, 
    MCPServerConfig as BaseMCPServerConfig,
    BusinessSystemConfig,
    get_mcp_config
)
from services.mcp_registry import MCPServerRegistry

logger = structlog.get_logger(__name__)


class DynamicMCPServerConfig(BaseMCPServerConfig):
    """Extended MCP server configuration with runtime metadata"""
    added_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    source: str = "config"  # config, api, manual
    persistent: bool = True  # Whether to save to config file
    tags: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ConfigurationPersistence:
    """Handles saving and loading dynamic configuration changes"""
    
    def __init__(self, config_dir: Path):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self.dynamic_config_file = self.config_dir / "dynamic_mcp_servers.yaml"
        self.backup_dir = self.config_dir / "backups"
        self.backup_dir.mkdir(exist_ok=True)
        
    def save_servers(self, servers: Dict[str, DynamicMCPServerConfig]):
        """Save dynamic server configurations to persistent storage"""
        try:
            # Create backup of existing config
            if self.dynamic_config_file.exists():
                backup_name = f"dynamic_mcp_servers_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml"
                backup_path = self.backup_dir / backup_name
                self.dynamic_config_file.rename(backup_path)
                
                # Keep only last 10 backups
                backups = sorted(self.backup_dir.glob("dynamic_mcp_servers_*.yaml"))
                for old_backup in backups[:-10]:
                    old_backup.unlink()
            
            # Save current configuration
            config_data = {
                "version": "1.0",
                "last_updated": datetime.now().isoformat(),
                "servers": {}
            }
            
            for name, server in servers.items():
                if server.persistent:
                    config_data["servers"][name] = {
                        "command": server.command,
                        "args": server.args,
                        "env": server.env,
                        "health_check": server.health_check,
                        "connection_type": server.connection_type,
                        "endpoint": server.endpoint,
                        "tags": server.tags,
                        "metadata": server.metadata,
                        "added_at": server.added_at.isoformat() if server.added_at else None,
                        "source": server.source
                    }
            
            with open(self.dynamic_config_file, 'w') as f:
                yaml.dump(config_data, f, default_flow_style=False)
                
            logger.info(
                "Saved dynamic MCP configuration",
                servers=len(config_data["servers"]),
                file=str(self.dynamic_config_file)
            )
            
        except Exception as e:
            logger.error("Failed to save dynamic configuration", error=str(e))
            raise
            
    def load_servers(self) -> Dict[str, DynamicMCPServerConfig]:
        """Load dynamic server configurations from persistent storage"""
        servers = {}
        
        if not self.dynamic_config_file.exists():
            return servers
            
        try:
            with open(self.dynamic_config_file, 'r') as f:
                config_data = yaml.safe_load(f)
                
            if not config_data or "servers" not in config_data:
                return servers
                
            for name, server_data in config_data["servers"].items():
                try:
                    # Convert ISO datetime strings back to datetime objects
                    if server_data.get("added_at"):
                        server_data["added_at"] = datetime.fromisoformat(server_data["added_at"])
                    
                    servers[name] = DynamicMCPServerConfig(
                        name=name,
                        **server_data
                    )
                except ValidationError as e:
                    logger.error(
                        "Invalid server configuration",
                        server=name,
                        error=str(e)
                    )
                    
            logger.info(
                "Loaded dynamic MCP configuration",
                servers=len(servers),
                file=str(self.dynamic_config_file)
            )
            
        except Exception as e:
            logger.error("Failed to load dynamic configuration", error=str(e))
            
        return servers


class DynamicMCPConfig(MCPConfiguration):
    """
    Runtime MCP server management with persistence.
    
    Features:
    - Add/remove servers without restart
    - Update server endpoints dynamically
    - Handle authentication changes
    - Persist configuration changes
    - Validate configurations
    - Manage server lifecycle
    """
    
    def __init__(self, config_dir: Optional[str] = None):
        # Initialize base configuration
        super().__init__()
        
        # Dynamic configuration state
        self._dynamic_servers: Dict[str, DynamicMCPServerConfig] = {}
        self._config_lock = Lock()
        self._registry: Optional[MCPServerRegistry] = None
        
        # Setup persistence
        config_dir = config_dir or os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            ".config"
        )
        self._persistence = ConfigurationPersistence(config_dir)
        
        # Load any previously saved dynamic servers
        self._load_dynamic_servers()
        
    def _load_dynamic_servers(self):
        """Load dynamic servers from persistent storage"""
        dynamic_servers = self._persistence.load_servers()
        
        with self._config_lock:
            # Merge dynamic servers with base configuration
            for name, server in dynamic_servers.items():
                if name not in self.mcp_servers:
                    self._dynamic_servers[name] = server
                    self.mcp_servers[name] = server
                    logger.info(
                        "Restored dynamic MCP server",
                        server=name,
                        source=server.source
                    )
                    
    def set_registry(self, registry: MCPServerRegistry):
        """Set the MCP registry for server lifecycle management"""
        self._registry = registry
        
    async def initialize_registry(self):
        """Register all configured servers with the registry"""
        if not self._registry:
            logger.warning("No registry set, skipping server registration")
            return
            
        # Register all servers from configuration
        for server_name, server_config in self.mcp_servers.items():
            try:
                logger.info(f"Registering configured server: {server_name}")
                success = await self._registry.register_server(server_config)
                if success:
                    logger.info(f"Successfully registered server: {server_name}")
                else:
                    logger.warning(f"Failed to register server: {server_name}")
            except Exception as e:
                logger.error(f"Error registering server {server_name}: {e}")
        
    async def add_server(
        self,
        name: str,
        command: str,
        args: List[str] = None,
        env: Dict[str, str] = None,
        health_check: Dict[str, Any] = None,
        connection_type: str = "local",
        endpoint: Optional[str] = None,
        tags: List[str] = None,
        metadata: Dict[str, Any] = None,
        persistent: bool = True,
        validate: bool = True
    ) -> DynamicMCPServerConfig:
        """
        Add a new MCP server at runtime.
        
        Args:
            name: Unique server name
            command: Command to run the server
            args: Command arguments
            env: Environment variables
            health_check: Health check configuration
            connection_type: Type of connection (local, hosted, sse)
            endpoint: Server endpoint (for hosted/sse)
            tags: Tags for categorization
            metadata: Additional metadata
            persistent: Whether to persist this configuration
            validate: Whether to validate the configuration
            
        Returns:
            The created server configuration
            
        Raises:
            ValueError: If server already exists or validation fails
        """
        with self._config_lock:
            if name in self.mcp_servers:
                raise ValueError(f"MCP server '{name}' already exists")
                
            # Create server configuration
            server_config = DynamicMCPServerConfig(
                name=name,
                command=command,
                args=args or [],
                env=env or {},
                health_check=health_check or {
                    "enabled": True,
                    "interval": 30,
                    "timeout": 10
                },
                connection_type=connection_type,
                endpoint=endpoint,
                tags=tags or [],
                metadata=metadata or {},
                added_at=datetime.now(),
                source="api",
                persistent=persistent
            )
            
            # Validate if requested
            if validate:
                await self._validate_server_config(server_config)
            
            # Add to configurations
            self._dynamic_servers[name] = server_config
            self.mcp_servers[name] = server_config
            
            # Register with MCP registry if available
            if self._registry:
                await self._registry.register_server(name, server_config.dict())
                
            # Persist if requested
            if persistent:
                self._persistence.save_servers(self._dynamic_servers)
                
            logger.info(
                "Added MCP server",
                server=name,
                type=connection_type,
                persistent=persistent
            )
            
            return server_config
            
    async def remove_server(self, name: str, force: bool = False) -> bool:
        """
        Remove an MCP server at runtime.
        
        Args:
            name: Server name to remove
            force: Force removal even if server is in use
            
        Returns:
            True if removed, False if not found
            
        Raises:
            ValueError: If server is in use and force=False
        """
        with self._config_lock:
            if name not in self.mcp_servers:
                return False
                
            # Check if server is in use by any agents
            if not force:
                agents_using = [
                    agent_id for agent_id, server_name in self.agent_mappings.items()
                    if server_name == name
                ]
                if agents_using:
                    raise ValueError(
                        f"Cannot remove server '{name}' - in use by agents: {agents_using}"
                    )
            
            # Remove from registry if available
            if self._registry:
                await self._registry.unregister_server(name)
                
            # Remove from configurations
            self.mcp_servers.pop(name, None)
            removed_server = self._dynamic_servers.pop(name, None)
            
            # Update agent mappings
            self.agent_mappings = {
                agent_id: server_name
                for agent_id, server_name in self.agent_mappings.items()
                if server_name != name
            }
            
            # Persist changes
            if removed_server and removed_server.persistent:
                self._persistence.save_servers(self._dynamic_servers)
                
            logger.info("Removed MCP server", server=name, forced=force)
            return True
            
    async def update_server(
        self,
        name: str,
        command: Optional[str] = None,
        args: Optional[List[str]] = None,
        env: Optional[Dict[str, str]] = None,
        endpoint: Optional[str] = None,
        connection_type: Optional[str] = None,
        health_check: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> DynamicMCPServerConfig:
        """
        Update an existing MCP server configuration.
        
        Args:
            name: Server name to update
            Various optional parameters to update
            
        Returns:
            Updated server configuration
            
        Raises:
            ValueError: If server doesn't exist
        """
        with self._config_lock:
            if name not in self.mcp_servers:
                raise ValueError(f"MCP server '{name}' not found")
                
            server = self.mcp_servers[name]
            
            # Convert to DynamicMCPServerConfig if needed
            if not isinstance(server, DynamicMCPServerConfig):
                server = DynamicMCPServerConfig(**server.dict())
                self.mcp_servers[name] = server
                self._dynamic_servers[name] = server
            
            # Update fields
            if command is not None:
                server.command = command
            if args is not None:
                server.args = args
            if env is not None:
                server.env.update(env)
            if endpoint is not None:
                server.endpoint = endpoint
            if connection_type is not None:
                server.connection_type = connection_type
            if health_check is not None:
                server.health_check.update(health_check)
            if tags is not None:
                server.tags = tags
            if metadata is not None:
                server.metadata.update(metadata)
                
            server.modified_at = datetime.now()
            
            # Update in registry if available
            if self._registry:
                await self._registry.update_server(name, server.dict())
                
            # Persist changes
            if server.persistent:
                self._persistence.save_servers(self._dynamic_servers)
                
            logger.info("Updated MCP server", server=name)
            return server
            
    async def update_server_auth(
        self,
        name: str,
        auth_env: Dict[str, str],
        validate: bool = True
    ):
        """
        Update authentication environment variables for a server.
        
        Args:
            name: Server name
            auth_env: Authentication environment variables
            validate: Whether to validate the new auth
        """
        if validate:
            # Could implement auth validation here
            pass
            
        await self.update_server(name, env=auth_env)
        
    def list_servers(self, tags: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        List all registered MCP servers.
        
        Args:
            tags: Filter by tags if provided
            
        Returns:
            List of server information dictionaries
        """
        servers = []
        
        for name, server in self.mcp_servers.items():
            # Filter by tags if requested
            if tags and isinstance(server, DynamicMCPServerConfig):
                if not any(tag in server.tags for tag in tags):
                    continue
                    
            server_info = {
                "name": name,
                "connection_type": server.connection_type,
                "endpoint": server.endpoint,
                "health_check_enabled": server.health_check.get("enabled", True),
                "source": getattr(server, "source", "config"),
                "tags": getattr(server, "tags", []),
                "added_at": getattr(server, "added_at", None)
            }
            
            # Add registry status if available
            if self._registry:
                status = self._registry.get_server_status(name)
                if status:
                    server_info["status"] = status
                    
            servers.append(server_info)
            
        return servers
        
    async def _validate_server_config(self, server: DynamicMCPServerConfig):
        """Validate server configuration"""
        # Basic validation
        if server.connection_type == "hosted" and not server.endpoint:
            raise ValueError("Hosted servers require an endpoint")
            
        if server.connection_type == "local" and not server.command:
            raise ValueError("Local servers require a command")
            
        # Could add more validation here (e.g., test connection)
        
    def export_configuration(self) -> Dict[str, Any]:
        """Export the complete configuration for backup or transfer"""
        return {
            "base_servers": {
                name: server.dict() 
                for name, server in self.mcp_servers.items()
                if name not in self._dynamic_servers
            },
            "dynamic_servers": {
                name: server.dict() 
                for name, server in self._dynamic_servers.items()
            },
            "business_systems": {
                name: system.dict() 
                for name, system in self.business_systems.items()
            },
            "agent_mappings": self.agent_mappings,
            "exported_at": datetime.now().isoformat()
        }
        
    def import_configuration(self, config_data: Dict[str, Any], merge: bool = True):
        """Import configuration from exported data"""
        with self._config_lock:
            if not merge:
                # Clear existing dynamic servers
                self._dynamic_servers.clear()
                
            # Import dynamic servers
            for name, server_data in config_data.get("dynamic_servers", {}).items():
                if name not in self.mcp_servers or not merge:
                    try:
                        server = DynamicMCPServerConfig(name=name, **server_data)
                        self._dynamic_servers[name] = server
                        self.mcp_servers[name] = server
                    except Exception as e:
                        logger.error(
                            "Failed to import server",
                            server=name,
                            error=str(e)
                        )
                        
            # Persist imported configuration
            self._persistence.save_servers(self._dynamic_servers)
            
            logger.info(
                "Imported configuration",
                servers=len(config_data.get("dynamic_servers", {})),
                merge=merge
            )


# Singleton instance
_dynamic_config: Optional[DynamicMCPConfig] = None


def get_dynamic_mcp_config() -> DynamicMCPConfig:
    """Get the singleton dynamic MCP configuration instance"""
    global _dynamic_config
    if _dynamic_config is None:
        _dynamic_config = DynamicMCPConfig()
    return _dynamic_config


# Convenience functions
async def add_mcp_server(name: str, **kwargs) -> DynamicMCPServerConfig:
    """Add a new MCP server"""
    config = get_dynamic_mcp_config()
    return await config.add_server(name, **kwargs)


async def remove_mcp_server(name: str, force: bool = False) -> bool:
    """Remove an MCP server"""
    config = get_dynamic_mcp_config()
    return await config.remove_server(name, force)


async def update_mcp_server(name: str, **kwargs) -> DynamicMCPServerConfig:
    """Update an MCP server"""
    config = get_dynamic_mcp_config()
    return await config.update_server(name, **kwargs)


def list_mcp_servers(tags: Optional[List[str]] = None) -> List[Dict[str, Any]]:
    """List all MCP servers"""
    config = get_dynamic_mcp_config()
    return config.list_servers(tags)