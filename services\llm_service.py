"""
LLM Service for Orchestrator

Provides LLM integration for analyzing user requests and determining tool calls.
Part of Phase 5: Response Processing.
"""
import json
import logging
import os
from typing import Dict, Any, List, Optional, Tuple
import aiohttp
from datetime import datetime

from config.model_config import get_model_for_agent, get_model_config
from services.config import get_settings

logger = logging.getLogger(__name__)


class LLMService:
    """
    Service for LLM-based analysis and tool selection
    """
    
    def __init__(self, model_name: Optional[str] = None):
        """
        Initialize LLM service
        
        Args:
            model_name: Optional model override
        """
        self.model_config = get_model_config()
        self.model_name = model_name or get_model_for_agent("orchestrator")
        
        # Get API key from validated settings
        settings = get_settings()
        self.api_key = settings.openai_api_key
        self.base_url = "https://api.openai.com/v1/chat/completions"
        
        logger.info(f"LLM service initialized with model: {self.model_name}")
        logger.info(f"API key configured: {'Yes' if self.api_key and self.api_key.startswith('sk-') else 'No'}")
    
    async def analyze_request(self, 
                            user_request: str,
                            available_tools: Dict[str, Any],
                            context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analyze user request and determine required tool calls
        
        Args:
            user_request: The user's request
            available_tools: Dictionary of available tools
            context: Additional context
            
        Returns:
            Analysis result with tool calls
        """
        logger.info(f"Analyzing request: {user_request[:100]}...")
        logger.debug(f"Available tools count: {len(available_tools)}")
        logger.debug(f"Context: {context}")
        
        try:
            # Build tool descriptions for prompt
            tools_desc = self._format_tools_for_prompt(available_tools)
            logger.debug(f"Formatted tools description length: {len(tools_desc)} chars")
            
            # Create analysis prompt
            system_prompt = self._get_analysis_system_prompt()
            user_prompt = self._build_analysis_prompt(user_request, tools_desc, context)
            logger.debug(f"System prompt length: {len(system_prompt)} chars")
            logger.debug(f"User prompt length: {len(user_prompt)} chars")
            
            # Call LLM
            logger.info("Calling LLM for request analysis...")
            response = await self._call_llm(system_prompt, user_prompt)
            logger.info(f"LLM response received, length: {len(response)} chars")
            logger.debug(f"LLM raw response: {response[:500]}...")
            
            # Parse response
            analysis = self._parse_llm_response(response)
            logger.info(f"Parsed analysis: {json.dumps(analysis, indent=2)}")
            
            # Validate tool calls
            tool_calls_count = len(analysis.get("tool_calls", []))
            logger.info(f"Tool calls before validation: {tool_calls_count}")
            validated_calls = self._validate_tool_calls(analysis.get("tool_calls", []), available_tools)
            analysis["tool_calls"] = validated_calls
            logger.info(f"Tool calls after validation: {len(validated_calls)}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"Failed to analyze request: {str(e)}", exc_info=True)
            logger.info("Falling back to pattern-based analysis")
            # Return fallback analysis
            return self._generate_fallback_analysis(user_request, available_tools)
    
    def _get_analysis_system_prompt(self) -> str:
        """Get system prompt for request analysis"""
        return """You are an AI assistant that analyzes user requests and determines which tools to call.

Your job is to:
1. Understand what the user is asking for
2. Identify which tools need to be called
3. Determine the correct parameters for each tool
4. Specify the order of execution if multiple tools are needed

IMPORTANT RULES:
- ALWAYS respond with valid JSON
- If the user asks about financial data (sales, revenue, etc.), use the appropriate reporting tools
- For status checks, use health_check or list_enabled_modules tools
- For searches, use search_across_modules with appropriate query
- For reports, use generate_consolidated_report with proper date ranges

Response Format:
{
    "analysis": "Brief explanation of what the user wants",
    "intent": "primary intent category",
    "tool_calls": [
        {
            "tool": "full_tool_name",
            "parameters": {},
            "purpose": "why this tool is needed",
            "depends_on": []
        }
    ],
    "confidence": 0.95,
    "needs_confirmation": false
}"""
    
    def _build_analysis_prompt(self, request: str, tools_desc: str, context: Optional[Dict[str, Any]]) -> str:
        """Build the analysis prompt"""
        prompt_parts = [
            f"User Request: {request}",
            "",
            "Available Tools:",
            tools_desc,
            ""
        ]
        
        if context:
            prompt_parts.extend([
                "Context:",
                json.dumps(context, indent=2),
                ""
            ])
        
        prompt_parts.extend([
            "Analyze this request and determine:",
            "1. What the user is trying to accomplish",
            "2. Which tools should be called",
            "3. What parameters each tool needs",
            "4. The order of execution (if multiple tools)",
            "",
            "Respond with the JSON format specified in the system prompt."
        ])
        
        return "\n".join(prompt_parts)
    
    def _format_tools_for_prompt(self, tools: Dict[str, Any]) -> str:
        """Format tool information for the prompt"""
        formatted_tools = []
        
        # Group by category for better organization
        tools_by_category = {}
        for tool_name, tool_info in tools.items():
            category = tool_info.category if hasattr(tool_info, 'category') else 'general'
            if category not in tools_by_category:
                tools_by_category[category] = []
            
            tools_by_category[category].append({
                "name": f"mcp__{tool_info.server_name}__{tool_info.name}",
                "description": tool_info.description,
                "parameters": self._simplify_parameters(tool_info.parameters)
            })
        
        # Format for prompt
        for category, cat_tools in sorted(tools_by_category.items()):
            formatted_tools.append(f"\n{category.upper()}:")
            for tool in cat_tools:
                formatted_tools.append(f"- {tool['name']}: {tool['description']}")
                if tool['parameters']:
                    formatted_tools.append(f"  Parameters: {json.dumps(tool['parameters'])}")
        
        return "\n".join(formatted_tools)
    
    def _simplify_parameters(self, params: Any) -> Dict[str, str]:
        """Simplify parameter schema for prompt"""
        if not params:
            return {}
        
        # Handle different parameter formats
        if isinstance(params, dict):
            if "properties" in params:
                # JSON Schema format
                simplified = {}
                for prop, schema in params.get("properties", {}).items():
                    param_type = schema.get("type", "string")
                    required = prop in params.get("required", [])
                    desc = schema.get("description", "")
                    simplified[prop] = f"{param_type}{' (required)' if required else ''}: {desc}"
                return simplified
            else:
                return params
        
        return {}
    
    async def _call_llm(self, system_prompt: str, user_prompt: str) -> str:
        """Call LLM API"""
        logger.debug(f"API key available: {bool(self.api_key)}")
        logger.debug(f"API key starts with 'sk-': {self.api_key.startswith('sk-') if self.api_key else False}")
        
        if not self.api_key:
            logger.warning("No OpenAI API key found, using fallback")
            return "{}"
        
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": self.model_name,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "temperature": 0.3,  # Lower temperature for more consistent tool selection
                "max_tokens": 1000,
                "response_format": {"type": "json_object"}  # Ensure JSON response
            }
            
            logger.info(f"Calling OpenAI API with model: {self.model_name}")
            logger.debug(f"API URL: {self.base_url}")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.base_url, json=payload, headers=headers) as response:
                    logger.info(f"OpenAI API response status: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        content = data["choices"][0]["message"]["content"]
                        logger.info(f"OpenAI API call successful, response length: {len(content)}")
                        logger.debug(f"Response content: {content[:200]}...")
                        return content
                    else:
                        error_text = await response.text()
                        logger.error(f"LLM API error: {response.status} - {error_text}")
                        logger.error(f"Failed payload: {json.dumps(payload, indent=2)}")
                        return "{}"
                        
        except aiohttp.ClientError as e:
            logger.error(f"Network error calling LLM: {str(e)}", exc_info=True)
            return "{}"
        except Exception as e:
            logger.error(f"Failed to call LLM: {str(e)}", exc_info=True)
            logger.error(f"Exception type: {type(e).__name__}")
            return "{}"
    
    def _parse_llm_response(self, response: str) -> Dict[str, Any]:
        """Parse LLM response"""
        try:
            return json.loads(response)
        except json.JSONDecodeError:
            logger.error(f"Failed to parse LLM response as JSON: {response}")
            return {
                "analysis": "Failed to parse response",
                "tool_calls": [],
                "error": "Invalid JSON response"
            }
    
    def _validate_tool_calls(self, tool_calls: List[Dict[str, Any]], available_tools: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Validate and clean tool calls"""
        validated = []
        
        for call in tool_calls:
            tool_name = call.get("tool", "")
            
            # Check if tool exists
            tool_found = False
            for available_name, tool_info in available_tools.items():
                full_name = f"mcp__{tool_info.server_name}__{tool_info.name}"
                if tool_name == full_name or tool_name == available_name:
                    tool_found = True
                    call["tool"] = full_name  # Ensure full name
                    break
            
            if tool_found:
                # Validate parameters if needed
                params = call.get("parameters", {})
                # Remove None values
                clean_params = {k: v for k, v in params.items() if v is not None}
                call["parameters"] = clean_params
                validated.append(call)
            else:
                logger.warning(f"Tool not found: {tool_name}")
        
        return validated
    
    def _generate_fallback_analysis(self, request: str, available_tools: Dict[str, Any]) -> Dict[str, Any]:
        """Generate fallback analysis using pattern matching"""
        request_lower = request.lower()
        tool_calls = []
        
        # Accounts Receivable / Debtors
        if any(word in request_lower for word in ['debtor', 'debtors', 'owe', 'owed', 'receivable', 'outstanding', 'unpaid']):
            logger.info("Detected accounts receivable query in fallback analysis")
            for tool_name, tool_info in available_tools.items():
                if "search" in tool_info.name:
                    tool_calls.append({
                        "tool": f"mcp__{tool_info.server_name}__{tool_info.name}",
                        "parameters": {"query": "outstanding receivables debtors", "modules": ["AR"], "limit": 50},
                        "purpose": "Search for customers who owe money (debtors)"
                    })
                    break
        
        # Pattern-based tool selection
        elif any(word in request_lower for word in ['sales', 'revenue', 'ytd', 'year to date', 'income']):
            # Find financial summary tool
            for tool_name, tool_info in available_tools.items():
                if "financial_summary" in tool_info.name:
                    tool_calls.append({
                        "tool": f"mcp__{tool_info.server_name}__{tool_info.name}",
                        "parameters": {},
                        "purpose": "Get financial summary data"
                    })
                    break
        
        if any(word in request_lower for word in ['health', 'status', 'check', 'connectivity']):
            for tool_name, tool_info in available_tools.items():
                if "health_check" in tool_info.name:
                    tool_calls.append({
                        "tool": f"mcp__{tool_info.server_name}__{tool_info.name}",
                        "parameters": {},
                        "purpose": "Check system health status"
                    })
                    break
        
        if any(word in request_lower for word in ['search', 'find', 'look for']):
            # Extract search query
            query = request
            for word in ['search', 'find', 'look for']:
                query = query.lower().replace(word, '').strip()
            
            for tool_name, tool_info in available_tools.items():
                if "search" in tool_info.name:
                    tool_calls.append({
                        "tool": f"mcp__{tool_info.server_name}__{tool_info.name}",
                        "parameters": {"query": query, "limit": 20},
                        "purpose": "Search across modules"
                    })
                    break
        
        if any(word in request_lower for word in ['report', 'financial report', 'consolidated']):
            for tool_name, tool_info in available_tools.items():
                if "consolidated_report" in tool_info.name:
                    # Default to current month
                    now = datetime.now()
                    tool_calls.append({
                        "tool": f"mcp__{tool_info.server_name}__{tool_info.name}",
                        "parameters": {
                            "report_type": "financial_summary",
                            "start_date": now.strftime('%Y-%m-01'),
                            "end_date": now.strftime('%Y-%m-%d'),
                            "format": "json"
                        },
                        "purpose": "Generate financial report"
                    })
                    break
        
        if any(word in request_lower for word in ['modules', 'enabled', 'available']):
            for tool_name, tool_info in available_tools.items():
                if "list_enabled_modules" in tool_info.name:
                    tool_calls.append({
                        "tool": f"mcp__{tool_info.server_name}__{tool_info.name}",
                        "parameters": {},
                        "purpose": "List enabled modules"
                    })
                    break
        
        return {
            "analysis": f"Pattern-based analysis of request: {request[:100]}...",
            "intent": "general_query",
            "tool_calls": tool_calls,
            "confidence": 0.7,
            "needs_confirmation": False,
            "fallback": True
        }
    
    async def extract_intent(self, message: str, context: Optional[Dict[str, Any]] = None) -> Tuple[str, float]:
        """
        Extract primary intent from user message
        
        Returns:
            Tuple of (intent, confidence)
        """
        # This is a simplified version - in production, use full LLM analysis
        message_lower = message.lower()
        
        intent_patterns = {
            'financial_report': (['report', 'financial', 'statement'], 0.9),
            'search': (['search', 'find', 'look for'], 0.85),
            'health_check': (['health', 'status', 'check'], 0.9),
            'module_list': (['modules', 'enabled', 'available'], 0.9),
            'revenue_query': (['sales', 'revenue', 'income'], 0.85),
            'general_query': ([], 0.5)  # Default
        }
        
        for intent, (keywords, base_confidence) in intent_patterns.items():
            if any(keyword in message_lower for keyword in keywords):
                return intent, base_confidence
        
        return 'general_query', 0.5