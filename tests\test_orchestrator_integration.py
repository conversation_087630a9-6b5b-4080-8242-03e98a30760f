"""
Integration test for Dynamic Orchestrator
Tests the orchestrator with mock MCP registry and tool discovery
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from unittest.mock import AsyncMock, Mock, patch
from agents.orchestrator import DynamicOrchestrator


async def test_orchestrator_integration():
    """Test orchestrator with mocked dependencies"""
    print("Testing Dynamic Orchestrator Integration...")
    
    # Create mock registry
    mock_registry = AsyncMock()
    mock_registry.list_servers = AsyncMock(return_value={
        "sage-intacct": {"name": "sage-intacct", "type": "tcp"}
    })
    
    # Create mock tool schemas
    mock_tools = [
        <PERSON>ck(name="get_gl_balance", description="Get GL account balance", 
             parameters={"account_number": {"type": "string", "required": True}}),
        <PERSON><PERSON>(name="create_journal_entry", description="Create journal entry",
             parameters={"entries": {"type": "array", "required": True}}),
        <PERSON><PERSON>(name="get_customer_balance", description="Get customer balance",
             parameters={"customer_id": {"type": "string", "required": True}}),
        <PERSON><PERSON>(name="create_invoice", description="Create AR invoice",
             parameters={"customer_id": {"type": "string", "required": True}}),
        Mock(name="get_vendor_balance", description="Get vendor balance",
             parameters={"vendor_id": {"type": "string", "required": True}}),
        Mock(name="create_payment_batch", description="Create payment batch",
             parameters={"vendor_ids": {"type": "array", "required": True}})
    ]
    
    mock_registry.get_server_tools = AsyncMock(return_value=mock_tools)
    
    # Create mock discovery service
    mock_discovery = AsyncMock()
    mock_discovery.get_tool_catalog = AsyncMock(return_value={
        "total_tools": len(mock_tools),
        "by_category": {
            "general_ledger": [
                {"full_name": "sage-intacct:get_gl_balance", "name": "get_gl_balance",
                 "description": "Get GL account balance", "category": "general_ledger"}
            ],
            "accounts_receivable": [
                {"full_name": "sage-intacct:create_invoice", "name": "create_invoice",
                 "description": "Create AR invoice", "category": "accounts_receivable"}
            ]
        }
    })
    
    mock_discovery.search_tools = AsyncMock(return_value=[
        {"full_name": "sage-intacct:get_gl_balance", "description": "Get GL account balance"}
    ])
    
    # Patch the imports
    with patch('agents.orchestrator.MCPRegistry', return_value=mock_registry):
        with patch('agents.orchestrator.ToolDiscoveryService', return_value=mock_discovery):
            orchestrator = DynamicOrchestrator()
            
            # Test 1: Intent Detection
            print("\n1. Testing Intent Detection:")
            test_cases = [
                ("What's the balance in account 1000?", "gl_balance"),
                ("Create a journal entry", "journal_entry"),
                ("Show customer balance", "customer_balance"),
                ("Process vendor payments", "payment_batch")
            ]
            
            for msg, expected_intent in test_cases:
                intent_info = await orchestrator._detect_intent(msg)
                print(f"   '{msg}' -> {intent_info['primary_intent']} (confidence: {intent_info['confidence']:.2f})")
            
            # Test 2: Tool Catalog
            print("\n2. Testing Tool Catalog:")
            catalog = await orchestrator.get_available_tools()
            print(f"   Total tools: {catalog['total_tools']}")
            for category, tools in catalog['by_category'].items():
                if tools:
                    print(f"   {category}: {len(tools)} tools")
            
            # Test 3: Tool Selection
            print("\n3. Testing Tool Selection:")
            selected_tools = await orchestrator._select_tools('gl_balance', 'get account balance')
            print(f"   Selected tools for GL balance: {selected_tools}")
            
            # Test 4: Low Confidence Handling
            print("\n4. Testing Low Confidence Handling:")
            intent_info = {
                'primary_intent': 'general_query',
                'confidence': 0.4,
                'all_intents': []
            }
            result = await orchestrator._handle_low_confidence("show data", intent_info)
            print(f"   Response includes clarification: {'clarify' in result['response']}")
            
            print("\nAll integration tests passed!")


if __name__ == "__main__":
    asyncio.run(test_orchestrator_integration())
