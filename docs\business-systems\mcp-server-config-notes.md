# MCP Server Configuration Notes

## Important Configuration Differences

This document explains the differences between generic examples and actual Claude Desktop MCP server configurations.

### 1. Sage Intacct Configuration

**Claude Desktop Config:**
```json
"sage-intacct": {
  "command": "cmd",
  "args": ["/c", "cd /d C:\\Users\\<USER>\\Documents\\GitHub\\sage-intacct-mcp-server && python -m src.main"],
  "env": {
    "PYTHONPATH": "C:\\Users\\<USER>\\Documents\\GitHub\\sage-intacct-mcp-server"
  }
}
```

**Key Points:**
- Server name: `"sage-intacct"` (with hyphen)
- Uses Windows `cmd /c` to change directory and run Python
- No module arguments needed (composite server runs all modules by default)
- MCP server handles its own authentication credentials
- This agent service only needs the connection information

### 2. Sage Business Cloud Accounting (SBCA) Configuration

**Claude Desktop Config:**
```json
"Sage Business Cloud Accounting": {
  "command": "npx",
  "args": ["-y", "supergateway", "--sse", "https://sbca-mcp.provenlabs.xyz/sse"]
}
```

**Key Points:**
- Server name: Full name `"Sage Business Cloud Accounting"` (not abbreviated)
- Uses `npx supergateway` for SSE (Server-Sent Events) connection
- Connects to hosted service at `https://sbca-mcp.provenlabs.xyz/sse`
- No local server installation required
- No environment variables needed (authentication handled by SSE endpoint)

### 3. Configuration Implications

#### For Agents:
- Must reference exact server names in `servers` array
- Example: `servers=["sage-intacct"]` not `servers=["intacct"]`

#### For Tool Mappers:
- MCP server name passed to BaseToolMapper must match
- Example: `IntacctToolMapper("sage-intacct")`

#### For Registration:
- Metadata must use correct server names
- Example: `"mcp_server": "sage-intacct"`

### 4. MCP Server Independence

**Sage Intacct**: 
- Runs as an independent service with its own credentials
- Credentials are configured in the MCP server's environment (not here)
- This agent service only needs to know how to connect to it

**SBCA**: 
- Hosted service handles all authentication
- No credentials needed in this agent service

### 5. Testing Considerations

1. **Sage Intacct**: Ensure the MCP server is installed and OAuth is configured
2. **SBCA**: Ensure internet connection to reach the SSE endpoint
3. **Agent Testing**: Mock MCP responses for unit tests
4. **Integration Testing**: Use actual MCP servers in Claude Desktop

### 6. Adding New MCP Servers

When adding a new MCP server:
1. Check if it's a local server (like Intacct) or hosted (like SBCA)
2. Use exact server names from Claude Desktop config
3. Match command structure (cmd for Windows Python, npx for Node packages)
4. Set appropriate environment variables

### Example: Adding QuickBooks

If QuickBooks uses a similar pattern:
```yaml
quickbooks:
  command: npx
  args: ["-y", "quickbooks-gateway", "--api", "https://quickbooks-mcp.example.com"]
```

Or for a local Python server:
```yaml
quickbooks:
  command: cmd
  args: ["/c", "cd /d C:\\path\\to\\quickbooks-mcp && python -m main"]
  env:
    PYTHONPATH: "C:\\path\\to\\quickbooks-mcp"
```

### 7. Troubleshooting

**"MCP server not found" errors:**
- Check exact server name spelling
- Verify server is configured in Claude Desktop
- Ensure command path is correct

**"Tool not available" errors:**
- Verify tool names match MCP server documentation
- Check if tool requires specific authentication
- Ensure MCP server is running correctly

**Connection issues:**
- For SSE servers: Check internet connection
- For local servers: Verify Python environment
- Check Claude Desktop logs for detailed errors
