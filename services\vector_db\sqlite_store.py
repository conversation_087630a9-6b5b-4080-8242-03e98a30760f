"""
SQLite with sqlite-vec extension for vector operations
"""

import sqlite3
import json
import os
from typing import List, Dict, Any, Optional, Tuple

import numpy as np
import structlog

from .base import VectorStore

logger = structlog.get_logger(__name__)


class SqliteVecStore(VectorStore):
    """SQLite with sqlite-vec extension for vector operations using single vec0 table."""

    def __init__(self, db_path: str = "data/ai_workspace_vectors.db", embedding_dimension: int = None):
        self.db_path = db_path
        self.embedding_dimension = embedding_dimension  # Will be auto-detected if None
        self._ensure_directory()
        self.conn: Optional[sqlite3.Connection] = None
        self.initialized = False
        
    def _ensure_directory(self):
        """Ensure the directory for the database exists."""
        dir_path = os.path.dirname(self.db_path)
        if dir_path and not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
    
    async def initialize(self) -> None:
        """Initialize SQLite with vec extension using single vec0 table with migration support."""
        try:
            import sqlite_vec

            # Create connection
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
            self.conn.row_factory = sqlite3.Row

            # Load sqlite-vec extension
            self.conn.enable_load_extension(True)
            sqlite_vec.load(self.conn)
            self.conn.enable_load_extension(False)

            # Check if we need to migrate from old schema
            await self._migrate_if_needed()

            # Determine embedding dimension
            if self.embedding_dimension is None:
                # Auto-detect from existing data or use default
                self.embedding_dimension = self._detect_embedding_dimension_sync()

            # Create simple vec0 virtual table with only essential columns that sqlite-vec supports
            # We'll store all metadata as JSON in a single column to avoid JOIN complexity
            self.conn.execute(f"""
                CREATE VIRTUAL TABLE IF NOT EXISTS tool_embeddings USING vec0(
                    id TEXT,
                    metadata TEXT,
                    embedding FLOAT[{self.embedding_dimension}]
                )
            """)

            self.conn.commit()

            # Restore migrated data if any
            if hasattr(self, '_migration_data') and self._migration_data:
                logger.info(f"Restoring {len(self._migration_data)} migrated records...")
                for record in self._migration_data:
                    try:
                        self.conn.execute("""
                            INSERT OR REPLACE INTO tool_embeddings (id, metadata, embedding)
                            VALUES (?, ?, vec_f32(?))
                        """, (
                            record["id"],
                            json.dumps(record["metadata"]),
                            record["embedding_json"]
                        ))
                    except Exception as e:
                        logger.warning(f"Could not restore record {record['id']}: {e}")

                self.conn.commit()
                delattr(self, '_migration_data')
                logger.info("Migration completed successfully")

            self.initialized = True
            logger.info("SQLite vector store initialized with native vec0 architecture", path=self.db_path)

        except ImportError:
            logger.error("sqlite-vec not installed. Run: pip install sqlite-vec")
            raise
        except Exception as e:
            logger.error("Failed to initialize SQLite vector store", error=str(e))
            raise
    
    async def _migrate_if_needed(self):
        """Migrate from old schema to new simplified schema if needed."""
        try:
            # Check if old tables exist
            cursor = self.conn.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name IN ('tool_metadata', 'tool_metadata_extended')
            """)
            old_tables = [row[0] for row in cursor.fetchall()]

            if old_tables:
                logger.info("Detected old schema, migrating to new simplified schema...")

                # Check if we have data to migrate
                data_to_migrate = []

                if 'tool_metadata' in old_tables:
                    # Try to get data from old hybrid approach
                    try:
                        cursor = self.conn.execute("""
                            SELECT m.id, m.tool_name, m.server_name, m.description, m.category,
                                   m.parameters, m.examples, m.capabilities, m.keywords,
                                   m.execution_count, m.success_rate, m.avg_execution_time, m.last_used,
                                   m.rowid_hash
                            FROM tool_metadata m
                        """)

                        for row in cursor.fetchall():
                            # Try to get the corresponding embedding
                            rowid = row["rowid_hash"]
                            try:
                                embed_cursor = self.conn.execute("""
                                    SELECT vec_to_json(embedding) as embedding_json
                                    FROM tool_embeddings
                                    WHERE rowid = ?
                                """, (rowid,))
                                embed_row = embed_cursor.fetchone()

                                if embed_row:
                                    # Build metadata object
                                    metadata = {
                                        "tool_name": row["tool_name"],
                                        "server": row["server_name"],
                                        "description": row["description"],
                                        "category": row["category"],
                                        "type": "tool",  # Default type
                                        "success": True,  # Default success
                                        "parameters": json.loads(row["parameters"] or "{}"),
                                        "examples": json.loads(row["examples"] or "[]"),
                                        "capabilities": json.loads(row["capabilities"] or "[]"),
                                        "keywords": json.loads(row["keywords"] or "[]"),
                                        "execution_stats": {
                                            "count": row["execution_count"],
                                            "success_rate": row["success_rate"],
                                            "avg_execution_time": row["avg_execution_time"],
                                            "last_used": row["last_used"]
                                        }
                                    }

                                    data_to_migrate.append({
                                        "id": row["id"],
                                        "metadata": metadata,
                                        "embedding_json": embed_row["embedding_json"]
                                    })
                            except Exception as e:
                                logger.warning(f"Could not migrate embedding for {row['id']}: {e}")

                    except Exception as e:
                        logger.warning(f"Could not read old metadata table: {e}")

                # Drop old tables
                for table in old_tables:
                    try:
                        self.conn.execute(f"DROP TABLE IF EXISTS {table}")
                        logger.info(f"Dropped old table: {table}")
                    except Exception as e:
                        logger.warning(f"Could not drop table {table}: {e}")

                # Drop old tool_embeddings if it exists with old schema
                try:
                    self.conn.execute("DROP TABLE IF EXISTS tool_embeddings")
                    logger.info("Dropped old tool_embeddings table")
                except Exception as e:
                    logger.warning(f"Could not drop old tool_embeddings: {e}")

                self.conn.commit()

                # Store migrated data for later insertion
                self._migration_data = data_to_migrate
                logger.info(f"Prepared {len(data_to_migrate)} records for migration")

        except Exception as e:
            logger.warning(f"Migration check failed: {e}")
            # Continue with initialization even if migration fails

    def _detect_embedding_dimension_sync(self) -> int:
        """Detect embedding dimension from existing data or use sensible default."""
        try:
            # Check if table exists and has data
            cursor = self.conn.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='tool_embeddings'
            """)

            if cursor.fetchone():
                # Table exists, try to get a sample embedding
                cursor = self.conn.execute("""
                    SELECT vec_to_json(embedding) as embedding_json
                    FROM tool_embeddings
                    LIMIT 1
                """)
                row = cursor.fetchone()

                if row:
                    embedding_data = json.loads(row["embedding_json"])
                    detected_dim = len(embedding_data)
                    logger.info(f"Detected embedding dimension from existing data: {detected_dim}")
                    return detected_dim

            # No existing data, check if OpenAI is available for default
            openai_key = os.getenv("OPENAI_API_KEY")
            if openai_key:
                logger.info("OpenAI API key found, defaulting to 1536 dimensions")
                return 1536  # OpenAI text-embedding-3-small
            else:
                logger.info("No OpenAI API key, defaulting to 768 dimensions")
                return 768   # sentence-transformers default

        except Exception as e:
            logger.warning(f"Could not detect embedding dimension: {e}, using 768")
            return 768

    def _ensure_initialized(self):
        """Ensure the store is initialized."""
        if not self.initialized or not self.conn:
            raise RuntimeError("Vector store not initialized. Call initialize() first.")
    
    async def upsert(
        self,
        id: str,
        embedding: np.ndarray,
        metadata: Dict[str, Any]
    ) -> None:
        """Insert or update a vector with metadata using single vec0 table."""
        self._ensure_initialized()

        try:
            # Ensure embedding is the right shape and type
            if len(embedding.shape) != 1:
                raise ValueError(f"Expected 1D embedding array, got shape {embedding.shape}")

            # Check if dimension matches the table dimension
            if hasattr(self, 'embedding_dimension') and self.embedding_dimension:
                if embedding.shape[0] != self.embedding_dimension:
                    raise ValueError(f"Expected embedding dimension {self.embedding_dimension}, got {embedding.shape[0]}")

            # Convert embedding to JSON for sqlite-vec vec_f32() function
            embedding_json = '[' + ','.join(str(float(x)) for x in embedding) + ']'

            # Store all metadata as JSON in a single column
            # This avoids sqlite-vec column type limitations and JOIN complexity
            metadata_json = json.dumps(metadata)

            # Insert/update in simple vec0 table
            self.conn.execute("""
                INSERT OR REPLACE INTO tool_embeddings (
                    id, metadata, embedding
                ) VALUES (?, ?, vec_f32(?))
            """, (
                id, metadata_json, embedding_json
            ))

            self.conn.commit()

        except Exception as e:
            logger.error("Failed to upsert vector", id=id, error=str(e))
            self.conn.rollback()
            raise
    
    async def search(
        self,
        query_embedding: np.ndarray,
        k: int = 20,
        threshold: float = 0.7,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Tuple[str, float, Dict[str, Any]]]:
        """Fast vector similarity search with post-filtering in Python."""
        self._ensure_initialized()

        try:
            # Convert embedding to JSON for sqlite-vec vec_f32() function
            query_json = '[' + ','.join(str(float(x)) for x in query_embedding) + ']'

            # Use simple sqlite-vec MATCH syntax without complex WHERE clauses
            # We'll do filtering in Python to avoid sqlite-vec constraint issues
            query = f"""
                SELECT id, metadata, distance
                FROM tool_embeddings
                WHERE embedding MATCH vec_f32(?)
                ORDER BY distance
                LIMIT {k * 3}
            """

            # Execute query with just the embedding
            cursor = self.conn.execute(query, [query_json])
            results = []

            for row in cursor:
                # Convert distance to similarity
                similarity = 1 - row["distance"]

                # Only include results above threshold
                if similarity < threshold:
                    continue

                # Parse metadata from JSON
                try:
                    metadata = json.loads(row["metadata"])
                except (json.JSONDecodeError, TypeError):
                    metadata = {}

                # Apply filters in Python
                if filters:
                    # Check server filter
                    if "server" in filters and metadata.get("server") != filters["server"]:
                        continue
                    # Check category filter
                    if "category" in filters and metadata.get("category") != filters["category"]:
                        continue
                    # Check type filter
                    if "type" in filters and metadata.get("type") != filters["type"]:
                        continue
                    # Check success filter
                    if "success" in filters and metadata.get("success") != filters["success"]:
                        continue

                results.append((row["id"], similarity, metadata))

                # Stop when we have enough results
                if len(results) >= k:
                    break

            return results

        except Exception as e:
            logger.error("Search failed", error=str(e))
            raise
    
    async def get(self, id: str) -> Optional[Tuple[np.ndarray, Dict[str, Any]]]:
        """Get a vector by ID from simple vec0 table."""
        self._ensure_initialized()

        try:
            # Get data from simple vec0 table
            cursor = self.conn.execute("""
                SELECT id, metadata, vec_to_json(embedding) as embedding_json
                FROM tool_embeddings
                WHERE id = ?
            """, (id,))

            row = cursor.fetchone()
            if not row:
                return None

            # Convert vector from JSON back to numpy array
            embedding_json = row["embedding_json"]
            embedding = np.array(json.loads(embedding_json), dtype=np.float32)

            # Parse metadata from JSON
            try:
                metadata = json.loads(row["metadata"])
            except (json.JSONDecodeError, TypeError):
                metadata = {}

            return (embedding, metadata)

        except Exception as e:
            logger.error("Failed to get vector", id=id, error=str(e))
            raise
    
    async def delete(self, id: str) -> bool:
        """Delete a vector by ID from single vec0 table."""
        self._ensure_initialized()

        try:
            # Delete from simple vec0 table
            cursor = self.conn.execute("DELETE FROM tool_embeddings WHERE id = ?", (id,))

            self.conn.commit()

            return cursor.rowcount > 0

        except Exception as e:
            logger.error("Failed to delete vector", id=id, error=str(e))
            self.conn.rollback()
            raise
    
    async def update_metadata(
        self,
        id: str,
        metadata_updates: Dict[str, Any]
    ) -> bool:
        """Update metadata for a vector in simple vec0 table."""
        self._ensure_initialized()

        try:
            # Get current metadata
            current_data = await self.get(id)
            if not current_data:
                return False

            _, current_metadata = current_data

            # Apply updates to metadata
            updated_metadata = current_metadata.copy()

            # Handle execution stats specially
            if "execution_stats.count" in metadata_updates:
                if "$inc" in metadata_updates["execution_stats.count"]:
                    exec_stats = updated_metadata.get("execution_stats", {})
                    exec_stats["count"] = exec_stats.get("count", 0) + metadata_updates["execution_stats.count"]["$inc"]
                    updated_metadata["execution_stats"] = exec_stats

            if "execution_stats.success_rate" in metadata_updates:
                exec_stats = updated_metadata.get("execution_stats", {})
                exec_stats["success_rate"] = metadata_updates["execution_stats.success_rate"]
                updated_metadata["execution_stats"] = exec_stats

            if "execution_stats.avg_execution_time" in metadata_updates:
                exec_stats = updated_metadata.get("execution_stats", {})
                exec_stats["avg_execution_time"] = metadata_updates["execution_stats.avg_execution_time"]
                updated_metadata["execution_stats"] = exec_stats

            if "execution_stats.last_used" in metadata_updates:
                exec_stats = updated_metadata.get("execution_stats", {})
                exec_stats["last_used"] = metadata_updates["execution_stats.last_used"]
                updated_metadata["execution_stats"] = exec_stats

            # Update the metadata JSON
            metadata_json = json.dumps(updated_metadata)

            cursor = self.conn.execute("""
                UPDATE tool_embeddings SET metadata = ? WHERE id = ?
            """, (metadata_json, id))

            self.conn.commit()

            return cursor.rowcount > 0

        except Exception as e:
            logger.error("Failed to update metadata", id=id, error=str(e))
            self.conn.rollback()
            raise
    
    async def clear(self) -> None:
        """Clear all vectors from single vec0 table."""
        self._ensure_initialized()

        try:
            self.conn.execute("DELETE FROM tool_embeddings")
            self.conn.commit()

        except Exception as e:
            logger.error("Failed to clear store", error=str(e))
            self.conn.rollback()
            raise

    async def count(self) -> int:
        """Get the total number of vectors in the store."""
        self._ensure_initialized()

        try:
            cursor = self.conn.execute("SELECT COUNT(*) as count FROM tool_embeddings")
            return cursor.fetchone()["count"]

        except Exception as e:
            logger.error("Failed to count vectors", error=str(e))
            raise
    
    async def health_check(self) -> bool:
        """Check if the vector store is healthy and accessible."""
        try:
            if not self.initialized:
                await self.initialize()
            
            # Try a simple query
            cursor = self.conn.execute("SELECT 1")
            cursor.fetchone()
            
            return True
            
        except Exception as e:
            logger.error("Health check failed", error=str(e))
            return False
    
    def __del__(self):
        """Clean up connection on deletion."""
        if self.conn:
            self.conn.close()