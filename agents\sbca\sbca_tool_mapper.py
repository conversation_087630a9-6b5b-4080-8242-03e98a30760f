"""
Example SBCA Tool Mapper

Demonstrates how to create a tool mapper for a different business system.
This would map SBCA agent tool names to SBCA MCP server tool names.
"""

from typing import Dict, Optional, Any
from agents.base import BaseToolMapper


class SBCAToolMapper(BaseToolMapper):
    """
    Maps agent tool names to SBCA MCP server tool names.
    
    Example implementation for Sage Business Cloud Accounting.
    """
    
    def _initialize_mappings(self):
        """Initialize the tool name mappings for SBCA MCP server"""
        # Define mappings from agent tool names to MCP tool names
        self._tool_map = {
            # Sales Module Tools
            "sbca-sales.invoice_create": "create_sales_invoice",
            "sbca-sales.quote_create": "create_sales_quote",
            "sbca-sales.customer_query": "get_contacts",
            "sbca-sales.sales_report": "get_sales_analytics",
            
            # Inventory Module Tools
            "sbca-inventory.stock_query": "get_stock_items",
            "sbca-inventory.stock_movement": "get_stock_movements",
            "sbca-inventory.product_create": "create_product",
            "sbca-inventory.reorder_check": "get_reorder_levels",
            
            # Common Tools
            "sbca-common.search": "search_all_modules",
            "sbca-common.dashboard": "get_dashboard_data",
        }
        
        # Tools that are not yet available
        self._unavailable_tools = {
            "sbca-sales.bulk_invoice": "Bulk invoice creation not yet supported",
            "sbca-inventory.stock_take": "Stock take functionality coming soon",
        }
        
        # Parameter mappings (if needed)
        self.param_mappings = {
            "get_contacts": {
                "type": "contact_type",
                "search": "query",
            },
            "get_stock_items": {
                "product": "product_id",
                "location": "location_id",
            }
        }
    
    def get_tool_description(self, agent_tool_name: str) -> Optional[Dict[str, Any]]:
        """Get detailed description of a tool's capabilities"""
        descriptions = {
            # Sales Tools
            "sbca-sales.invoice_create": "Create sales invoices in SBCA",
            "sbca-sales.quote_create": "Create sales quotes",
            "sbca-sales.customer_query": "Query customer information",
            "sbca-sales.sales_report": "Generate sales analytics reports",
            
            # Inventory Tools
            "sbca-inventory.stock_query": "Query current stock levels",
            "sbca-inventory.stock_movement": "Track stock movements",
            "sbca-inventory.product_create": "Create new products",
            "sbca-inventory.reorder_check": "Check items below reorder level",
            
            # Common Tools
            "sbca-common.search": "Search across all SBCA modules",
            "sbca-common.dashboard": "Get dashboard overview data",
        }
        
        description = descriptions.get(agent_tool_name, "Tool description not available")
        return {
            "name": agent_tool_name,
            "description": description,
            "available": self.is_tool_available(agent_tool_name)
        }


# Example instantiation
# sbca_tool_mapper = SBCAToolMapper("sbca")
