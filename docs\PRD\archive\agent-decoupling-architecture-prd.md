# AI Workspace Agent Decoupling Analysis & Architecture
## Current Codebase Review & fast-agent Migration Strategy

After reviewing the existing codebase, here's the comprehensive analysis and migration plan for decoupling all agents using `fast-agent`:

---

## Current Agent Architecture Analysis

### ✅ Existing Agents (lib/agents/)
1. **IntacctGLAgent.ts** - General Ledger operations
2. **IntacctARAgent.ts** - Accounts Receivable management  
3. **IntacctAPAgent.ts** - Accounts Payable operations
4. **AnalysisAgent.ts** - Financial analysis and calculations
5. **ReportAgent.ts** - Report generation
6. **ValidationAgent.ts** - Data validation operations
7. **OrchestratorAgent.ts** - Multi-agent workflow coordination

### ✅ Current Integration Pattern
- **BaseAgent** abstract class with common functionality
- **Agent Registry** for agent discovery and management
- **MCP Manager** for tool integration (lib/mcp/client.ts)
- **assistant-ui** integration via custom adapter (lib/assistant/adapter.ts)
- **Workflow orchestration** with predefined workflows

### ✅ Tool Integration
- MCP servers: `intacct-gl`, `intacct-ap`, `intacct-ar`
- Tool executor for MCP server communication
- Predefined tool sets per agent

---

## fast-agent Migration Recommendations

### 🎯 Agents to Migrate to fast-agent

**ALL current agents should be migrated** since this will be the standard architecture:

#### **Core Financial Agents** (Priority 1)
1. **IntacctGLAgent** → **fast-agent GL Service**
   - Tools: GL balance queries, journal entries, financial statements
   - Complex calculations and analysis capabilities
   - High computational requirements - perfect for Python

2. **IntacctARAgent** → **fast-agent AR Service**  
   - Tools: Customer queries, invoice creation, payment application
   - Collections management and aging reports
   - Customer relationship logic

3. **IntacctAPAgent** → **fast-agent AP Service**
   - Tools: Vendor management, invoice processing, payment batches
   - Approval workflows and payment optimization

#### **Analysis & Reporting Agents** (Priority 2)
4. **AnalysisAgent** → **fast-agent Analysis Service**
   - Financial ratio calculations, variance analysis
   - Complex mathematical operations - ideal for Python
   - ML capabilities for forecasting

5. **ReportAgent** → **fast-agent Reporting Service**
   - Report generation and formatting
   - Data aggregation and visualization
   - PDF/Excel export capabilities

6. **ValidationAgent** → **fast-agent Validation Service**
   - Data validation and integrity checks
   - Business rule enforcement
   - Compliance validation

#### **Orchestration Agent** (Priority 3)
7. **OrchestratorAgent** → **fast-agent Orchestrator Service**
   - Workflow coordination across services
   - Multi-agent communication
   - Process management and monitoring

---

## Architecture Decisions & Answers

### 1. ✅ MCP Server Configuration Location
**RECOMMENDATION: Move to Agent Projects**

**Why**: Since agents will be decoupled, MCP configurations should live with the agents that use them.

```yaml
# ai-workspace-agents/fastagent.config.yaml
mcp:
  servers:
    sage_intacct_gl:
      command: "uv"
      args: ["run", "intacct-gl-mcp-server.py"]
      env:
        INTACCT_API_KEY: "${INTACCT_API_KEY}"
        INTACCT_SENDER_ID: "${INTACCT_SENDER_ID}"
    
    sage_intacct_ar:
      command: "uv" 
      args: ["run", "intacct-ar-mcp-server.py"]
      env:
        INTACCT_API_KEY: "${INTACCT_API_KEY}"
        
    sage_intacct_ap:
      command: "uv"
      args: ["run", "intacct-ap-mcp-server.py"] 
      env:
        INTACCT_API_KEY: "${INTACCT_API_KEY}"
```

### 2. ✅ Project Structure Decision
**RECOMMENDATION: Separate Standalone Project**

**Why**: 
- Different technology stack (Python vs TypeScript)
- Independent deployment and scaling
- Separate development lifecycle
- Clean separation of concerns

### 3. ✅ Repository Organization
**RECOMMENDATION: Separate Repository or Workspace**

**Options**:

#### **Option A: Separate Repository (Recommended)**
```
/ai-workspace-web-app (existing)
├── components/
├── lib/
└── app/

/ai-workspace-agents (new repo)
├── agents/
├── services/
├── mcp-servers/
└── fastagent.config.yaml
```

#### **Option B: Monorepo with Workspaces**
```
/ai-workspace (root)
├── packages/
│   ├── web-app/ (existing Next.js)
│   └── agents/ (new Python)
├── package.json (workspace config)
└── README.md
```

**I recommend Option A** for cleaner separation and easier deployment.

---

## Recommended Project Structure

### New Agent Service Repository: `ai-workspace-agents`

```
ai-workspace-agents/
├── agents/
│   ├── gl_agent.py           # GL Agent implementation
│   ├── ar_agent.py           # AR Agent implementation  
│   ├── ap_agent.py           # AP Agent implementation
│   ├── analysis_agent.py     # Analysis Agent
│   ├── report_agent.py       # Report Agent
│   ├── validation_agent.py   # Validation Agent
│   └── orchestrator_agent.py # Orchestrator Agent
├── services/
│   ├── api_service.py        # FastAPI REST/WebSocket server
│   ├── agent_manager.py      # Agent lifecycle management
│   └── health_service.py     # Health checks and monitoring
├── mcp-servers/              # MCP server implementations
│   ├── intacct_gl_server.py
│   ├── intacct_ar_server.py  
│   └── intacct_ap_server.py
├── tests/
│   ├── test_agents.py
│   └── test_integration.py
├── docker/
│   ├── Dockerfile
│   └── docker-compose.yml
├── config/
│   ├── fastagent.config.yaml # fast-agent configuration
│   └── api.env.example       # Environment variables
├── requirements.txt          # Python dependencies
├── pyproject.toml           # Python project config
└── README.md

### Core Implementation Strategy

#### 1. FastAPI Agent Service Layer
```python
# agents_service.py
from fastapi import FastAPI, WebSocket, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import asyncio
import json
from typing import Dict, Any
import uvicorn

# Import your fast-agent agents
from agents.gl_agent import GLAgent
from agents.ar_agent import ARAgent
from agents.ap_agent import APAgent

app = FastAPI(title="AI Workspace Agent Service")

# Enable CORS for Next.js integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Next.js dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class AgentManager:
    def __init__(self):
        self.agents = {
            "gl-agent": GLAgent(),
            "ar-agent": ARAgent(),
            "ap-agent": APAgent(),
        }
    
    async def send_message(self, agent_id: str, message: str, context: Dict[str, Any]) -> str:
        if agent_id not in self.agents:
            raise HTTPException(status_code=404, detail=f"Agent {agent_id} not found")
        
        agent = self.agents[agent_id]
        # Send message to fast-agent
        response = await agent.send(message)
        return response

# Global agent manager
agent_manager = AgentManager()

# REST API Endpoints
@app.post("/agents/{agent_id}/chat")
async def chat_with_agent(agent_id: str, request: dict):
    """REST endpoint for single message exchange"""
    message = request.get("message", "")
    context = request.get("context", {})
    
    response = await agent_manager.send_message(agent_id, message, context)
    
    return {
        "response": response,
        "agent_id": agent_id,
        "timestamp": "2025-05-29T12:00:00Z"
    }

# WebSocket for real-time streaming
@app.websocket("/agents/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time agent communication"""
    await websocket.accept()
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_json()
            agent_id = data.get("agent_id")
            message = data.get("message")
            context = data.get("context", {})
            
            # Process with agent
            response = await agent_manager.send_message(agent_id, message, context)
            
            # Send response back
            await websocket.send_json({
                "type": "agent_response",
                "agent_id": agent_id,
                "response": response,
                "timestamp": "2025-05-29T12:00:00Z"
            })
            
    except Exception as e:
        await websocket.send_json({
            "type": "error",
            "error": str(e)
        })
    finally:
        await websocket.close()

# Health check
@app.get("/health")
async def health_check():
    return {"status": "healthy", "agents": list(agent_manager.agents.keys())}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

#### 2. fast-agent Agent Definitions
```python
# agents/gl_agent.py
import asyncio
from mcp_agent.core.fastagent import FastAgent

# Create the fast-agent application
fast = FastAgent("GL Agent Service")

@fast.agent(
    name="gl_agent",
    instruction="""You are a General Ledger specialist for Sage Intacct. 
    Help users with GL inquiries, journal entries, and financial reporting.""",
    servers=["sage_intacct"],  # MCP server connection
    model="claude-3-5-sonnet-20241022",
    use_history=True
)
class GLAgent:
    def __init__(self):
        self.fast = fast
    
    async def send(self, message: str) -> str:
        """Send message to the GL agent and return response"""
        async with self.fast.run() as agent:
            response = await agent.gl_agent.send(message)
            return response
```

#### 3. Frontend Integration (Next.js)
```typescript
// lib/agents/FastAgentClient.ts
export class FastAgentClient {
  private baseUrl: string;
  private wsUrl: string;
  
  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_AGENT_SERVICE_URL || 'http://localhost:8000';
    this.wsUrl = this.baseUrl.replace('http', 'ws') + '/agents/ws';
  }
  
  // REST API for single exchanges
  async sendMessage(agentId: string, message: string, context: any): Promise<string> {
    const response = await fetch(`${this.baseUrl}/agents/${agentId}/chat`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ message, context })
    });
    
    const data = await response.json();
    return data.response;
  }
  
  // WebSocket for real-time communication
  createWebSocketConnection(): WebSocket {
    const ws = new WebSocket(this.wsUrl);
    
    ws.onopen = () => console.log('Agent WebSocket connected');
    ws.onerror = (error) => console.error('Agent WebSocket error:', error);
    
    return ws;
  }
}
```

#### 4. assistant-ui Integration
```typescript
// components/chat/FastAgentAdapter.ts
import { ChatModelAdapter } from '@ai-sdk/core';
import { FastAgentClient } from '@/lib/agents/FastAgentClient';

export class FastAgentAdapter implements ChatModelAdapter {
  private client: FastAgentClient;
  
  constructor() {
    this.client = new FastAgentClient();
  }
  
  async *run({ messages, abortSignal, context }) {
    const lastMessage = messages[messages.length - 1];
    const agentId = this.determineAgentId(lastMessage.content);
    
    // For real-time streaming, use WebSocket
    if (this.requiresStreaming(lastMessage.content)) {
      yield* this.streamFromWebSocket(agentId, lastMessage.content, context);
    } else {
      // For simple requests, use REST
      const response = await this.client.sendMessage(agentId, lastMessage.content, context);
      yield { content: [{ type: 'text', text: response }] };
    }
  }
  
  private determineAgentId(content: string): string {
    // Logic to determine which agent to use
    if (content.includes('journal') || content.includes('GL')) return 'gl-agent';
    if (content.includes('receivables') || content.includes('AR')) return 'ar-agent';
    if (content.includes('payables') || content.includes('AP')) return 'ap-agent';
    return 'gl-agent'; // default
  }
  
  private async *streamFromWebSocket(agentId: string, message: string, context: any) {
    const ws = this.client.createWebSocketConnection();
    
    return new Promise((resolve, reject) => {
      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'agent_response') {
          resolve(data.response);
        }
      };
      
      ws.onopen = () => {
        ws.send(JSON.stringify({ agent_id: agentId, message, context }));
      };
    });
  }
}
```

---

## Migration Strategy

### Phase 1: Infrastructure Setup (Week 1)
1. **Create FastAPI Agent Service**
   - Set up basic FastAPI application
   - Add CORS middleware for Next.js integration
   - Implement health check endpoints

2. **Agent Wrapper Development**
   - Create wrapper classes for existing agents
   - Implement async message handling
   - Add error handling and logging

### Phase 2: Agent Migration (Week 2-3)
1. **Convert Current Agents to fast-agent**
   - Migrate GL Agent functionality
   - Migrate AR Agent functionality  
   - Migrate AP Agent functionality
   - Maintain MCP server connections

2. **API Endpoint Implementation**
   - Add REST endpoints for each agent
   - Implement WebSocket handlers
   - Add request/response validation

### Phase 3: Frontend Integration (Week 4)
1. **Update Agent Client**
   - Replace direct agent calls with HTTP requests
   - Implement WebSocket communication
   - Add error handling and retries

2. **assistant-ui Integration**
   - Create FastAgentAdapter
   - Update chat components
   - Test streaming functionality

### Phase 4: Testing & Optimization (Week 5)
1. **Integration Testing**
   - End-to-end testing
   - Performance optimization
   - Error handling validation

2. **Production Deployment**
   - Containerization
   - Environment configuration
   - Monitoring setup

---

## Benefits of This Architecture

### ✅ **True Decoupling**
- Agents run as separate Python service
- Frontend communicates via standard HTTP/WebSocket
- Independent scaling and deployment

### ✅ **fast-agent Advantages**
- Leverage fast-agent's MCP integration
- Advanced workflow capabilities (chains, parallel, orchestration)
- Declarative agent definitions
- Built-in multimodal support

### ✅ **Protocol Flexibility**
- REST for simple request/response
- WebSocket for real-time streaming
- Easy to add additional protocols later

### ✅ **Scalability**
- Agent service can be scaled independently
- Multiple agent instances possible
- Load balancing support

---

## Technical Considerations

### Performance
- **Latency**: Additional network hop (~10-50ms)
- **Throughput**: HTTP/WebSocket overhead
- **Mitigation**: Keep connections alive, use connection pooling

### Error Handling
- **Network failures**: Implement retry logic
- **Agent crashes**: Service restart mechanisms
- **Graceful degradation**: Fallback responses

### Security
- **Authentication**: JWT tokens for API access
- **CORS**: Proper origin configuration
- **Rate limiting**: Prevent abuse

---

## Final Recommendation

**✅ PROCEED** with FastAPI wrapper approach because:

1. **fast-agent is powerful** - Great MCP integration, workflow capabilities
2. **Clean separation** - True decoupling between frontend and agents
3. **Protocol flexibility** - Both REST and WebSocket support
4. **Scalable architecture** - Independent service scaling
5. **Future-proof** - Easy to extend with additional agents

The implementation requires building a FastAPI service layer but provides a robust, scalable foundation for agent decoupling.

---

## Next Steps

1. **Confirm approach** - Review and approve this architecture
2. **Set up development environment** - FastAPI, fast-agent dependencies
3. **Start with Phase 1** - Basic FastAPI service creation
4. **Begin agent migration** - Convert one agent as proof of concept