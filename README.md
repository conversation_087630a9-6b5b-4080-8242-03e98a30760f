# AI Workspace Agents

FastAPI service for AI Workspace agents with direct MCP (Model Context Protocol) client integration.

## Architecture Overview

This system uses direct MCP client connections to communicate with business system servers, providing a clean separation between orchestration logic and tool execution. See [Direct MCP Client Architecture](./docs/direct-mcp-client-architecture.md) for details.

## Project Structure

```
ai-workspace-agents/
├── agents/              # Agent implementations (orchestrator)
├── services/            # Core services
│   ├── mcp_client_wrapper.py    # MCP client abstraction
│   ├── mcp_registry.py          # Server registry and management
│   ├── response_processor.py    # Response formatting
│   └── llm_service.py           # LLM integration
├── mcp-servers/         # MCP server configurations
├── config/              # Configuration files
├── tests/               # Test files
├── docs/                # Documentation
└── docker/              # Docker configuration
```

## Prerequisites

- Python 3.11+
- Node.js (for MCP servers)
- uv package manager

## Setup

1. Install uv package manager:
   ```bash
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

2. Install dependencies:
   ```bash
   uv pip install -e .
   ```

3. Copy environment configuration:
   ```bash
   cp config/api.env.example config/api.env
   ```

4. Configure MCP servers:
   - Copy `mcp.config.example.yaml` to `mcp.config.yaml`
   - Update server configurations and API keys

5. Run the service:
   ```bash
   uv run uvicorn services.api_service:app --reload
   ```

## Quick Start

1. **Start the API server**:
   ```bash
   uv run uvicorn services.api_service:app --reload
   ```

2. **Test a simple request**:
   ```bash
   curl -X POST http://localhost:8000/api/v1/orchestrator/process \
     -H "Content-Type: application/json" \
     -d '{"message": "Check the health of all systems"}'
   ```

3. **Use WebSocket for streaming**:
   ```python
   import asyncio
   import websockets
   import json

   async def test_websocket():
       uri = "ws://localhost:8000/api/v1/orchestrator/ws"
       async with websockets.connect(uri) as websocket:
           await websocket.send(json.dumps({
               "message": "What's my current month sales?"
           }))
           response = await websocket.recv()
           print(json.loads(response))
   ```

## Environment Variables

Key environment variables (see `config/api.env.example` for full list):

```bash
# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# LLM Configuration (optional)
OPENAI_API_KEY=your_openai_key

# Business System APIs
INTACCT_API_KEY=your_intacct_key
# Add other system keys as needed
```

## MCP Server Configuration

Edit `mcp.config.yaml` to configure MCP servers:

```yaml
servers:
  sage-intacct:
    command: node
    args: ["path/to/sage-intacct-mcp-server"]
    env:
      INTACCT_API_KEY: ${INTACCT_API_KEY}
```

## Development

### Running Tests

```bash
# Run all tests
uv run pytest

# Run specific test suites
uv run pytest tests/test_mcp_migration_integration.py -v
uv run pytest tests/test_mcp_client_wrapper.py -v

# Run with coverage
uv run pytest --cov=services --cov=agents
```

### Code Quality

```bash
# Format code
uv run black .
uv run ruff check --fix .

# Type checking
uv run mypy .
```

### Testing with Real MCP Servers

1. Start your MCP server (e.g., Sage Intacct):
   ```bash
   cd /path/to/sage-intacct-mcp-server
   npm start
   ```

2. Run integration tests:
   ```bash
   uv run python tools/check_connectivity.py
   ```

## API Endpoints

- `POST /api/v1/orchestrator/process` - Process a message
- `GET /api/v1/orchestrator/tools` - List available tools
- `GET /api/v1/orchestrator/health` - Health check
- `WS /api/v1/orchestrator/ws` - WebSocket for streaming

## Documentation

- [Direct MCP Client Architecture](./docs/direct-mcp-client-architecture.md)
- [FastAgent to MCP Migration Guide](./docs/fastagent-to-mcp-migration-guide.md)
- [Adding New Business Systems](./docs/business-systems/adding-new-business-system-guide.md)

## Troubleshooting

### Common Issues

1. **"Client is not connected"**: Ensure MCP servers are running and configured correctly
2. **Tool not found**: Check tool discovery with `/api/v1/orchestrator/tools`
3. **Connection timeouts**: Verify server URLs and network connectivity

### Debug Mode

Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
uv run uvicorn services.api_service:app --reload
```

## Contributing

1. Create a feature branch
2. Make your changes
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

See LICENSE file for details.
