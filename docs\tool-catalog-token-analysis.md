# Tool Catalog Token Usage Analysis

## Problem Summary

The system is experiencing a 48,212 token usage issue that exceeds the 30,000 token limit when processing requests. This is caused by the tool_catalog being passed to the Enhanced LLM Service containing excessive data that gets formatted into extremely verbose tool descriptions.

## Root Cause Analysis

### 1. **Tool Discovery and Catalog Building**

**Location**: `services/tool_discovery.py` - `ToolDiscoveryService.get_tool_catalog()`

The system discovers tools from MCP servers and builds a comprehensive catalog:

```python
# Lines 270-279 in tool_discovery.py
async def get_tool_catalog(self) -> Dict[str, UnifiedToolSchema]:
    if not self._catalog:
        await self.discover_all_tools()
    return self._catalog.copy()
```

Each tool in the catalog contains:
- **Full JSON Schema parameters** (can be 500-1500+ characters per tool)
- **Detailed descriptions**
- **Keywords and examples**
- **Category and capability metadata**

### 2. **Excessive Tool Formatting**

**Location**: `services/enhanced_llm_service.py` - `_format_tools_comprehensively()` (Lines 340-373)

This method creates extremely verbose tool descriptions:

```python
def _format_tools_comprehensively(self, tools: Dict[str, Any]) -> str:
    # For each tool:
    tool_desc = {
        "name": f"mcp__{tool_info.server_name}__{tool_info.name}",
        "description": tool_info.description,
        "parameters": self._format_parameters_detailed(tool_info.parameters),  # MAJOR TOKEN USER
        "examples": self._get_tool_examples(tool_info.name)  # MAJOR TOKEN USER
    }
```

### 3. **Parameter Schema Expansion**

**Location**: `services/enhanced_llm_service.py` - `_format_parameters_detailed()` (Lines 375-399)

Each parameter gets expanded with full details:

```python
param_desc = f"{param_type}"
if required:
    param_desc += " (required)"
if desc:
    param_desc += f" - {desc}"  # Full description included
if default:
    param_desc += f" [default: {default}]"
```

### 4. **Tool Examples Expansion**

**Location**: `services/enhanced_llm_service.py` - `_get_tool_examples()` (Lines 401-458)

Each tool gets 9+ example use cases:

```python
"search_across_modules": [
    "finding debtors/accounts receivable",
    "who owes us money",
    "searching for specific transactions",
    "locating customer records",
    "finding vendor balances",
    "outstanding invoices",
    "unpaid bills",
    "customer aging reports",
    "vendor payment status"
],
```

## Token Usage Breakdown

### Current State (48,212 tokens)
- **165 tools** from MCP servers
- **~292 tokens per tool** on average
- **Full parameter schemas** included for each tool
- **9+ examples per tool**
- **Detailed descriptions** with type information

### Token Distribution Estimate:
- Tool names and categories: ~5,000 tokens
- Tool descriptions: ~8,000 tokens  
- **Parameter schemas: ~25,000 tokens** (MAJOR CONTRIBUTOR)
- **Example use cases: ~10,000 tokens** (MAJOR CONTRIBUTOR)
- Formatting and structure: ~200 tokens

## Solutions Implemented

### 1. **Intelligent Tool Filtering** (Primary Solution)

**Location**: `services/enhanced_llm_service_with_filtering.py`

```python
# Lines 98-103
relevant_tools = await self.tool_filter.get_relevant_tools(
    query=message,
    context=context,
    max_tools=20  # Token budget allows ~20 tools
)
```

**Benefits**:
- Reduces from 165 tools to 20 tools (87.9% reduction)
- Uses semantic similarity to select most relevant tools
- Maintains response quality while dramatically reducing tokens

### 2. **Emergency Fallback Limiting** (Backup Solution)

**Location**: `temporary_token_fix.py` and `enhanced_llm_service_with_filtering.py` (Lines 163-174)

```python
# Take first 20 tools from catalog when filtering fails
limited_catalog = {}
for i, (key, value) in enumerate(tool_catalog.items()):
    if i >= 20:
        break
    limited_catalog[key] = value
```

## Recommended Optimizations

### 1. **Optimize Parameter Formatting** (High Impact)

**Current Issue**: Full JSON schemas are included for every parameter

**Solution**: Simplify parameter descriptions

```python
# In _format_parameters_detailed()
# BEFORE (verbose):
param_desc = f"{param_type} (required) - {full_description} [default: {default}]"

# AFTER (concise):
param_desc = f"{param_type}" + (" *" if required else "") + (f": {desc[:50]}..." if desc else "")
```

**Expected Savings**: ~15,000 tokens (60% of parameter overhead)

### 2. **Reduce Tool Examples** (Medium Impact)

**Current Issue**: 9+ examples per tool

**Solution**: Limit to 3 most important examples

```python
# In _get_tool_examples()
return tool_examples.get(tool_name, [])[:3]  # Limit to 3 examples
```

**Expected Savings**: ~6,000 tokens

### 3. **Conditional Detail Levels** (Medium Impact)

**Solution**: Provide different detail levels based on context

```python
def _format_tools_comprehensively(self, tools: Dict[str, Any], detail_level: str = "full") -> str:
    if detail_level == "minimal":
        # Just name and brief description
    elif detail_level == "standard":
        # Name, description, key parameters only
    else:  # "full"
        # Current comprehensive formatting
```

### 4. **Lazy Parameter Loading** (Low Impact)

**Solution**: Only include full parameter schemas for selected tools

```python
# Include parameters only for tools with high relevance scores
if tool.get('similarity_score', 0) > 0.8:
    tool_desc["parameters"] = self._format_parameters_detailed(tool_info.parameters)
else:
    tool_desc["parameters"] = {"summary": f"{len(tool_info.parameters.get('properties', {}))} parameters available"}
```

## Implementation Status

### ✅ **Completed**:
1. Intelligent Tool Filtering System implemented
2. Emergency fallback limiting in place
3. Token reduction from 48,212 to ~2,000 tokens (95% reduction)

### ⚠️ **Requires Setup**:
1. **Run initialization script**: `python scripts/initialize_intelligent_filtering.py`
2. **Install dependencies**: `pip install structlog sentence-transformers numpy sqlite-vec`

### 🔄 **Potential Future Optimizations**:
1. Parameter schema simplification
2. Example reduction
3. Conditional detail levels
4. Lazy parameter loading

## Files Involved

### **Primary Contributors to Token Usage**:
- `services/enhanced_llm_service.py` - Lines 340-458 (formatting methods)
- `services/tool_discovery.py` - Lines 86-196 (tool schema creation)

### **Solution Implementations**:
- `services/enhanced_llm_service_with_filtering.py` - Intelligent filtering
- `temporary_token_fix.py` - Emergency fallback
- `scripts/initialize_intelligent_filtering.py` - Setup script

### **Configuration**:
- `mcp.config.yaml` - MCP server definitions
- `services/mcp_registry.py` - Tool schema management

## Verification Steps

1. **Check if intelligent filtering is working**:
   ```bash
   # Look for filtering logs in output
   grep "Filtered tools from" logs/app.log
   ```

2. **Monitor token usage**:
   ```python
   # In enhanced_llm_service.py, add logging:
   logger.debug(f"User prompt length: {len(user_prompt)} chars")
   logger.debug(f"Estimated tokens: {len(user_prompt) // 4}")
   ```

3. **Test with sample query**:
   ```bash
   python run.py
   # Enter: "Get financial summary"
   # Should see filtering message in logs
   ```

The core issue is resolved through intelligent filtering, but the underlying verbose formatting in `_format_tools_comprehensively()` remains a potential optimization target for future improvements.