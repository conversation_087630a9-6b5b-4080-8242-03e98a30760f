# Confidence Scoring Approach

## Overview
This document defines the confidence scoring system for the orchestrator to evaluate its understanding of user intents and make intelligent decisions about tool selection and clarification needs.

---

## Scoring Framework

### Core Formula
```python
confidence_score = (
    keyword_match_score * 0.4 +      # 40%: Keyword matching
    context_match_score * 0.3 +      # 30%: Context alignment
    parameter_completeness * 0.2 +   # 20%: Required info present
    specificity_score * 0.1          # 10%: Request specificity
) - ambiguity_penalty               # Deductions for unclear requests
```

### Score Range: 0.0 to 1.0
- **High Confidence**: 0.8 - 1.0
- **Medium Confidence**: 0.6 - 0.79
- **Low Confidence**: 0.0 - 0.59

---

## Component Scoring Details

### 1. Keyword Match Score (40%)

#### Scoring Rules
- **Exact Match** (1.0): Primary keywords found exactly
- **Strong Match** (0.8): Multiple secondary keywords present
- **Partial Match** (0.5): Some relevant keywords found
- **Weak Match** (0.3): Only peripheral keywords present
- **No Match** (0.0): No relevant keywords detected

#### Example Keyword Mappings
```yaml
Balance Inquiry:
  primary: ["balance", "how much"]
  secondary: ["account", "amount", "total"]
  weight: 1.0

Journal Entry:
  primary: ["journal entry", "JE"]
  secondary: ["debit", "credit", "posting"]
  weight: 1.0

Payment Processing:
  primary: ["payment", "pay", "remit"]
  secondary: ["vendor", "invoice", "bill"]
  weight: 0.9
```

### 2. Context Match Score (30%)

#### Scoring Factors
- **Entity Identified** (0.3): Customer/Vendor/Account specified
- **Time Period Clear** (0.2): Date/Period mentioned
- **Amount Specified** (0.2): Monetary values included
- **Purpose Stated** (0.3): Clear business reason given

#### Examples
```python
# High context score (0.9-1.0)
"Show me customer ABC Corp's balance as of December 31st"
# Entity: ✓ (ABC Corp)
# Time: ✓ (December 31st)
# Purpose: ✓ (balance inquiry)

# Medium context score (0.5-0.8)
"I need to process vendor payments"
# Entity: ✗ (no specific vendor)
# Time: ✗ (no date specified)
# Purpose: ✓ (payment processing)

# Low context score (0.0-0.4)
"Show me the report"
# Entity: ✗
# Time: ✗
# Purpose: ✗ (which report?)
```

### 3. Parameter Completeness (20%)

#### Calculation
```python
completeness_score = (
    required_params_provided / total_required_params
) * 0.7 + (
    optional_params_provided / total_optional_params
) * 0.3
```

#### Tool Parameter Requirements
```yaml
get_balance:
  required: [account_number]
  optional: [as_of_date, include_details]
  
create_invoice:
  required: [customer_id, line_items]
  optional: [invoice_date, due_date, terms]
  
run_report:
  required: [report_type, period]
  optional: [format, filters, grouping]
```

### 4. Specificity Score (10%)

#### Scoring Criteria
- **Very Specific** (1.0): Exact request with all details
- **Specific** (0.7): Clear request, some details to infer
- **General** (0.4): Broad request, multiple interpretations
- **Vague** (0.0): Unclear what user wants

#### Examples
```text
Very Specific: "Create journal entry debiting account 5100 for $1,000 and crediting account 2100 for $1,000 with description 'Monthly rent'"

Specific: "Show me last month's P&L statement"

General: "I need financial reports"

Vague: "Help with accounting"
```

### 5. Ambiguity Penalty (Deductions)

#### Penalty Factors
- **Conflicting Keywords** (-0.15): Mixed signals in request
- **Multiple Interpretations** (-0.10): Could mean several things
- **Missing Context** (-0.05): Important details omitted
- **Grammar Issues** (-0.05): Unclear due to phrasing

#### Examples
```text
Conflicting: "Show me customer invoices and vendor bills"
# Penalty: -0.15 (mixing AR and AP)

Multiple Interpretations: "Process the payment"
# Penalty: -0.10 (receive or make payment?)

Missing Context: "Run the report"
# Penalty: -0.05 (which report?)
```

---

## Confidence-Based Actions

### High Confidence (≥0.8)
```python
{
    "action": "execute",
    "reasoning": "Clear intent with all required information",
    "response_template": "I'll {specific_action} right away..."
}
```

Example:
> User: "Show me the balance for GL account 1000 as of today"
> 
> Confidence: 0.92
> - Keyword match: 1.0 (balance, account)
> - Context match: 0.9 (account specified, time clear)
> - Parameters: 1.0 (all required present)
> - Specificity: 0.8 (very clear request)
> 
> Response: "I'll retrieve the current balance for GL account 1000..."

### Medium Confidence (0.6-0.79)
```python
{
    "action": "confirm_and_execute",
    "reasoning": "Likely intent but should confirm details",
    "response_template": "I understand you want to {interpreted_action}. Let me {specific_steps}. Is this correct?"
}
```

Example:
> User: "Process vendor payments due this week"
> 
> Confidence: 0.72
> - Keyword match: 0.8 (process, vendor, payments)
> - Context match: 0.6 (time frame given, no specific vendors)
> - Parameters: 0.7 (have date range, missing selection criteria)
> - Specificity: 0.7 (clear but needs details)
> 
> Response: "I understand you want to process vendor payments due this week. Let me create a payment batch for all invoices due by [date]. Should I include early payment discounts?"

### Low Confidence (<0.6)
```python
{
    "action": "clarify",
    "reasoning": "Unclear intent or missing critical information",
    "response_template": "I'd be happy to help with {general_topic}. Could you tell me more about {specific_questions}?"
}
```

Example:
> User: "Fix the accounts"
> 
> Confidence: 0.35
> - Keyword match: 0.3 (accounts is too general)
> - Context match: 0.2 (no specifics)
> - Parameters: 0.3 (no clear action)
> - Specificity: 0.2 (very vague)
> - Ambiguity penalty: -0.1 (unclear action)
> 
> Response: "I'd be happy to help with the accounts. Could you tell me more about:
> - Are you looking to reconcile specific accounts?
> - Do you need to correct account balances?
> - Is there a specific issue you're trying to resolve?"

---

## Clarification Strategies

### Structured Clarification
When confidence is low, use structured options:

```text
Template:
"I can help with {detected_topic}. Are you looking to:
□ Option A: {most_likely_interpretation}
□ Option B: {alternative_interpretation}  
□ Option C: {another_possibility}
□ Something else? Please describe..."
```

### Progressive Clarification
For medium confidence, confirm while proceeding:

```text
Template:
"Based on your request, I'll {planned_action}:
- Step 1: {first_step}
- Step 2: {second_step}
- Step 3: {third_step}

Before I proceed, would you like me to adjust anything?"
```

### Contextual Hints
Provide examples to guide users:

```text
Template:
"To help me better understand, could you provide details like:
- Example 1: {concrete_example}
- Example 2: {another_example}
For instance, you might say: '{sample_request}'"
```

---

## Implementation Examples

### Example 1: Balance Inquiry
```python
def score_balance_inquiry(user_input):
    scores = {
        'keyword': 0,
        'context': 0,
        'parameters': 0,
        'specificity': 0
    }
    
    # Keyword scoring
    if 'balance' in user_input.lower():
        scores['keyword'] = 1.0
    elif 'how much' in user_input.lower():
        scores['keyword'] = 0.8
    
    # Context scoring
    if re.search(r'\d{4,}', user_input):  # Account number
        scores['context'] += 0.4
    if any(date_term in user_input for date_term in ['today', 'current', 'as of']):
        scores['context'] += 0.3
    
    # Parameter scoring
    if has_account_number(user_input):
        scores['parameters'] = 1.0
    
    # Calculate final score
    confidence = (
        scores['keyword'] * 0.4 +
        scores['context'] * 0.3 +
        scores['parameters'] * 0.2 +
        scores['specificity'] * 0.1
    )
    
    return confidence
```

### Example 2: Workflow Detection
```python
def score_workflow_request(user_input):
    workflow_patterns = {
        'month_end_close': {
            'keywords': ['month-end', 'close', 'closing'],
            'context_hints': ['period', 'GL', 'reconcile'],
            'confidence_boost': 0.2
        },
        'payment_run': {
            'keywords': ['payment batch', 'pay vendors', 'payment run'],
            'context_hints': ['due', 'discount', 'approve'],
            'confidence_boost': 0.15
        }
    }
    
    max_confidence = 0
    detected_workflow = None
    
    for workflow, pattern in workflow_patterns.items():
        score = calculate_pattern_match(user_input, pattern)
        if score > max_confidence:
            max_confidence = score
            detected_workflow = workflow
    
    return max_confidence, detected_workflow
```

---

## Continuous Improvement

### Confidence Calibration
- Log all confidence scores with actual outcomes
- Adjust weights based on success rates
- Track user corrections to improve scoring

### Pattern Learning
- Identify new keyword patterns from successful interactions
- Update ambiguity penalties based on clarification needs
- Refine context requirements per tool type

### Feedback Integration
```python
{
    "interaction_id": "uuid",
    "user_input": "original request",
    "confidence_score": 0.75,
    "action_taken": "confirm_and_execute",
    "user_feedback": "correct|incorrect|partial",
    "lessons_learned": {
        "keyword_adjustment": "+0.1 for 'process payments'",
        "context_requirement": "vendor specification helpful"
    }
}
```

---

## Best Practices

1. **Conservative Scoring**: When in doubt, score lower to ensure clarification
2. **User Education**: Help users learn to provide better context over time
3. **Graceful Degradation**: Always have a fallback for very low confidence
4. **Context Preservation**: Maintain conversation context to improve scoring
5. **Tool-Specific Tuning**: Adjust scoring weights based on tool requirements

This confidence scoring system ensures the orchestrator makes intelligent decisions about when to act, when to confirm, and when to ask for clarification, leading to more successful interactions and better user experience.
