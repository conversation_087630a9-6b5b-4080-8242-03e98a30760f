"""
Unit tests for ConfiguredAgent base class
"""

import unittest
from unittest.mock import Mo<PERSON>, patch, MagicMock, AsyncMock
import asyncio
from typing import Dict, Any

# Mock the dependencies
import sys
sys.modules['structlog'] = MagicMock()
sys.modules['mcp_agent.core.fastagent'] = MagicMock()

# Mock config module
mock_config = MagicMock()
mock_config.get_mcp_config.return_value = MagicMock()
mock_config.get_agent_mcp_servers = MagicMock(return_value=["test-server"])
mock_config.get_agent_tool_mapper = MagicMock(return_value=MagicMock())
sys.modules['config.mcp_config'] = mock_config

# Now import our class
from agents.base.configured_agent import ConfiguredAgent


class TestConfiguredAgent(ConfiguredAgent):
    """Test implementation of ConfiguredAgent"""
    
    def __init__(self):
        super().__init__(
            agent_id="test_agent",
            name="Test Agent",
            instruction="You are a test agent"
        )
    
    async def gather_data(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Test implementation of gather_data"""
        return {"test": "data", "params": params}


class TestConfiguredAgentClass(unittest.TestCase):
    """Test the ConfiguredAgent base class"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Reset mocks
        mock_config.reset_mock()
        
        # Create test agent
        with patch('agents.base.configured_agent.os.path.join', return_value='fake_config.yaml'):
            with patch('agents.base.configured_agent.os.path.dirname', return_value='fake_dir'):
                with patch('agents.base.configured_agent.os.path.abspath', return_value='fake_path'):
                    self.agent = TestConfiguredAgent()
    
    def test_initialization(self):
        """Test agent initialization with automatic configuration"""
        # Check basic properties
        self.assertEqual(self.agent.agent_id, "test_agent")
        self.assertEqual(self.agent.name, "Test Agent")
        self.assertEqual(self.agent.instruction, "You are a test agent")
        
        # Check MCP configuration was retrieved
        self.assertEqual(self.agent.mcp_servers, ["test-server"])
        self.assertIsNotNone(self.agent.tool_mapper)
        
        # Verify config was called correctly
        mock_config.get_mcp_config.assert_called()
        mock_config.get_agent_mcp_servers.assert_called_with("test_agent")
        mock_config.get_agent_tool_mapper.assert_called_with("test_agent")
    
    def test_no_mcp_servers_warning(self):
        """Test warning when no MCP servers configured"""
        # Mock no servers
        mock_config.get_agent_mcp_servers.return_value = []
        
        with patch('agents.base.configured_agent.logger') as mock_logger:
            with patch('agents.base.configured_agent.os.path.join', return_value='fake_config.yaml'):
                agent = TestConfiguredAgent()
            
            # Check warning was logged
            mock_logger.warning.assert_called_with(
                "No MCP servers configured for agent test_agent"
            )
    
    async def test_send_method(self):
        """Test the send method"""
        # Mock the agent function
        mock_response = "Test response"
        self.agent._agent_function = AsyncMock(return_value=mock_response)
        
        # Test send
        result = await self.agent.send("Test message", {"key": "value"})
        
        # Check response structure
        self.assertIn("content", result)
        self.assertIn("metadata", result)
        self.assertEqual(result["content"], mock_response)
        self.assertEqual(result["metadata"]["agent_id"], "test_agent")
        self.assertEqual(result["metadata"]["mcp_servers"], ["test-server"])
        self.assertIn("timestamp", result["metadata"])
        
        # Verify agent function was called
        self.agent._agent_function.assert_called_once_with("Test message")
    
    async def test_send_error_handling(self):
        """Test error handling in send method"""
        # Mock an error
        self.agent._agent_function = AsyncMock(side_effect=Exception("Test error"))
        
        # Test send
        result = await self.agent.send("Test message")
        
        # Check error response
        self.assertIn("error", result)
        self.assertEqual(result["error"], "Test error")
        self.assertIn("metadata", result)
        self.assertEqual(result["metadata"]["agent_id"], "test_agent")
    
    def test_enhance_message_default(self):
        """Test default message enhancement"""
        # Default implementation should return message as-is
        enhanced = self.agent._enhance_message("Test message")
        self.assertEqual(enhanced, "Test message")
    
    async def test_gather_data_implementation(self):
        """Test gather_data is implemented by subclass"""
        # Test our implementation
        result = await self.agent.gather_data({"type": "test"})
        
        self.assertEqual(result["test"], "data")
        self.assertEqual(result["params"]["type"], "test")
    
    def test_create_agent_decorator(self):
        """Test that _create_agent creates the decorated function"""
        # Check that agent function was created
        self.assertIsNotNone(self.agent._agent_function)
        
        # Check FastAgent was initialized
        self.assertIsNotNone(self.agent.fast)


class TestConfiguredAgentSubclass(unittest.TestCase):
    """Test creating a real subclass"""
    
    def test_subclass_with_custom_enhancement(self):
        """Test subclass with custom message enhancement"""
        
        class CustomAgent(ConfiguredAgent):
            def __init__(self):
                super().__init__(
                    agent_id="custom_agent",
                    name="Custom Agent",
                    instruction="Custom instruction"
                )
            
            def _enhance_message(self, message: str) -> str:
                """Custom enhancement"""
                return f"Enhanced: {message}"
            
            async def gather_data(self, params: Dict[str, Any]) -> Dict[str, Any]:
                return {"custom": True}
        
        with patch('agents.base.configured_agent.os.path.join', return_value='fake_config.yaml'):
            with patch('agents.base.configured_agent.os.path.dirname', return_value='fake_dir'):
                with patch('agents.base.configured_agent.os.path.abspath', return_value='fake_path'):
                    agent = CustomAgent()
        
        # Test custom enhancement
        enhanced = agent._enhance_message("Test")
        self.assertEqual(enhanced, "Enhanced: Test")


# Test the async methods
def run_async_test(coro):
    """Helper to run async tests"""
    loop = asyncio.new_event_loop()
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()


class TestAsyncMethods(unittest.TestCase):
    """Test async methods separately"""
    
    def setUp(self):
        """Set up async test agent"""
        with patch('agents.base.configured_agent.os.path.join', return_value='fake_config.yaml'):
            with patch('agents.base.configured_agent.os.path.dirname', return_value='fake_dir'):
                with patch('agents.base.configured_agent.os.path.abspath', return_value='fake_path'):
                    self.agent = TestConfiguredAgent()
    
    def test_async_send(self):
        """Test async send method"""
        async def test():
            self.agent._agent_function = AsyncMock(return_value="Async response")
            result = await self.agent.send("Async test")
            self.assertEqual(result["content"], "Async response")
        
        run_async_test(test())
    
    def test_async_gather_data(self):
        """Test async gather_data method"""
        async def test():
            result = await self.agent.gather_data({"async": True})
            self.assertEqual(result["test"], "data")
            self.assertTrue(result["params"]["async"])
        
        run_async_test(test())


if __name__ == "__main__":
    unittest.main()
