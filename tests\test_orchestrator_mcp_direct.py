"""
Test script for the refactored orchestrator using direct MCP client connections

This tests that the orchestrator can:
1. Connect to MCP servers without FastAgent
2. Execute tools directly
3. Return actual results (not just descriptions)
"""
import asyncio
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_orchestrator():
    """Test the orchestrator with various requests"""
    from agents.orchestrator import DynamicOrchestrator
    from services.mcp_registry import MCPServerRegistry
    
    print("\n=== Testing Direct MCP Orchestrator ===\n")
    
    # Create orchestrator
    registry = MCPServerRegistry()
    orchestrator = DynamicOrchestrator(registry)
    
    # Initialize
    print("1. Initializing orchestrator...")
    await orchestrator.initialize()
    print("✓ Orchestrator initialized\n")
    
    # Test cases
    test_cases = [
        {
            "name": "Health Check",
            "message": "Check the health status of the system",
            "expected_tool": "health_check"
        },
        {
            "name": "List Modules",
            "message": "What modules are enabled?",
            "expected_tool": "list_enabled_modules"
        },
        {
            "name": "Financial Summary",
            "message": "Show me the YTD sales revenue",
            "expected_tool": "get_financial_summary"
        },
        {
            "name": "Search",
            "message": "Search for recent transactions",
            "expected_tool": "search_across_modules"
        }
    ]
    
    # Run tests
    for i, test in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: {test['name']}")
        print(f"   Request: {test['message']}")
        
        try:
            # Process request
            result = await orchestrator.process(test['message'])
            
            # Check response
            print(f"   Response: {result['response'][:200]}...")
            
            # Check metadata
            metadata = result.get('metadata', {})
            tools_executed = metadata.get('tools_executed', [])
            
            if tools_executed:
                print(f"   Tools executed: {tools_executed}")
                # Verify expected tool was called
                expected = test['expected_tool']
                if any(expected in tool for tool in tools_executed):
                    print(f"   ✓ Expected tool '{expected}' was executed")
                else:
                    print(f"   ✗ Expected tool '{expected}' was NOT executed")
            else:
                print("   ✗ No tools were executed!")
            
            # Check for errors
            if 'error' in result:
                print(f"   Error: {result['error']}")
                
        except Exception as e:
            print(f"   ✗ Test failed with error: {str(e)}")
            logger.error(f"Test error", exc_info=True)
    
    print("\n=== Test Summary ===")
    print("The orchestrator should now:")
    print("1. Execute tools directly via MCP clients")
    print("2. Return actual results (not descriptions)")
    print("3. Work without FastAgent framework")
    

async def test_workflow_execution():
    """Test workflow execution capabilities"""
    from agents.orchestrator import DynamicOrchestrator
    from services.mcp_registry import MCPServerRegistry
    
    print("\n\n=== Testing Workflow Execution ===\n")
    
    # Create orchestrator
    registry = MCPServerRegistry()
    orchestrator = DynamicOrchestrator(registry)
    await orchestrator.initialize()
    
    # Test month-end close workflow
    print("Testing month-end close workflow...")
    
    try:
        result = await orchestrator.process("Perform month-end close for December 2024")
        print(f"Response: {result['response'][:300]}...")
        
        metadata = result.get('metadata', {})
        if metadata.get('workflow_executed'):
            print(f"✓ Workflow executed: {metadata['workflow_executed']}")
        else:
            print("✗ No workflow was executed")
            
    except Exception as e:
        print(f"✗ Workflow test failed: {str(e)}")


async def main():
    """Run all tests"""
    await test_orchestrator()
    await test_workflow_execution()
    

if __name__ == "__main__":
    asyncio.run(main())