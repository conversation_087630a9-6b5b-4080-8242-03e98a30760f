"""
Tool execution module for the orchestrator

Handles direct tool execution, parallel execution, and tool selection
for financial operations.
"""
import logging
from typing import Dict, Any, List, Optional, Callable

from services.parallel_executor import ParallelToolExecutor, ToolCall

logger = logging.getLogger(__name__)


class ToolExecutor:
    """Manages tool execution for the orchestrator"""
    
    def __init__(self, registry=None, discovery=None):
        self.registry = registry
        self.discovery = discovery
        self.parallel_executor = None
        self._available_tools = {}
    
    def set_available_tools(self, tools: Dict[str, Any]):
        """Update the available tools catalog"""
        self._available_tools = tools
    
    async def execute_tool_directly(self, tool_name: str, parameters: Dict[str, Any]) -> Any:
        """
        DEPRECATED: Execute a single tool directly via MCP client
        
        ⚠️  This method is deprecated in favor of MCP client delegation.
        ⚠️  Use orchestrator._delegate_to_mcp_client() for multi-step workflows instead.
        """
        try:
            # Parse tool name if it contains server prefix
            if ':' in tool_name:
                server_name, actual_tool_name = tool_name.split(':', 1)
            else:
                # Try to find the tool in our catalog
                tool_info = self._available_tools.get(tool_name)
                if not tool_info:
                    # Check with mcp__ prefix (MCP tool format)
                    if tool_name.startswith('mcp__'):
                        parts = tool_name.split('__')
                        if len(parts) >= 3:
                            server_name = parts[1]
                            actual_tool_name = '__'.join(parts[2:])
                        else:
                            raise ValueError(f"Invalid MCP tool name format: {tool_name}")
                    else:
                        raise ValueError(f"Tool not found: {tool_name}")
                else:
                    server_name = tool_info.server_name
                    actual_tool_name = tool_info.name
            
            # Execute the tool via registry
            if not self.registry:
                raise ValueError("Registry not initialized")
                
            result = await self.registry.call_tool(server_name, actual_tool_name, parameters)
            
            logger.info(f"Executed tool {tool_name} successfully")
            return result
            
        except Exception as e:
            logger.error(f"Failed to execute tool {tool_name}: {str(e)}")
            raise
    
    async def execute_parallel_tools(self, tool_calls: List[Dict[str, Any]], 
                                   allow_partial_failure: bool = True,
                                   response_processor=None) -> Dict[str, Any]:
        """
        Execute multiple tools in parallel when possible
        
        Args:
            tool_calls: List of tool call specifications
            allow_partial_failure: Whether to continue if some tools fail
            response_processor: Optional response processor for formatting
            
        Returns:
            Dict containing results, errors, and execution metadata
        """
        if not self.parallel_executor:
            # Initialize parallel executor with our tool execution function
            self.parallel_executor = ParallelToolExecutor(self.execute_tool_directly)
        
        result = await self.parallel_executor.execute_parallel(tool_calls, allow_partial_failure)
        
        # Format response if processor available
        if response_processor and (result.get('success') or (allow_partial_failure and result.get('results'))):
            formatted_response = await response_processor.format_parallel_response(
                parallel_result=result
            )
            result['formatted_response'] = formatted_response
        
        return result
    
    async def select_tools(self, intent: str, message: str, intent_detector=None) -> List[str]:
        """
        Select appropriate tools based on intent and available tools
        
        Args:
            intent: Detected intent
            message: User message
            intent_detector: Optional intent detector for capability mapping
            
        Returns:
            List of tool names to use
        """
        if not self.discovery:
            logger.warning("Discovery service not available, returning empty tool list")
            return []
        
        # Get capabilities and categories from intent detector if available
        if intent_detector:
            capabilities = intent_detector.get_capabilities_for_intent(intent)
            categories = intent_detector.get_categories_for_intent(intent)
        else:
            # Fallback mappings
            capabilities = ['read']
            categories = []
        
        # Search for appropriate tools
        results = await self.discovery.search_tools(
            query=message,
            category=categories[0] if categories else None,
            capabilities=capabilities
        )
        
        # Return top matching tools
        return [tool['full_name'] for tool in results[:3]]
    
    def detect_parallel_opportunities(self, message: str, intent_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Detect opportunities for parallel tool execution based on the request
        
        Args:
            message: User's message
            intent_info: Intent detection results
            
        Returns:
            List of tool calls that can be parallelized
        """
        parallel_patterns = {
            # Multiple module validation
            'multi_module_validation': {
                'keywords': ['all modules', 'validate everything', 'check all'],
                'tools': [
                    {'tool_name': 'mcp__sage-intacct__health_check', 'parameters': {}},
                    {'tool_name': 'mcp__sage-intacct__list_enabled_modules', 'parameters': {}},
                    {'tool_name': 'mcp__sage-intacct__get_financial_summary', 'parameters': {}}
                ]
            },
            
            # Month-end close workflow
            'month_end_parallel': {
                'keywords': ['month-end', 'close', 'period close'],
                'tools': [
                    # First batch - data gathering
                    {'tool_name': 'mcp__sage-intacct__get_financial_summary', 
                     'parameters': {'include_modules': ['GL', 'AR', 'AP']}, 'id': 'summary'},
                    
                    # Second batch - validation (depends on summary)
                    {'tool_name': 'mcp__sage-intacct__execute_month_end_close',
                     'parameters': {'dry_run': True, 'modules': ['GL', 'AR', 'AP']}, 
                     'id': 'validation', 'dependencies': ['summary']}
                ]
            },
            
            # Comprehensive analysis
            'comprehensive_analysis': {
                'keywords': ['comprehensive', 'full analysis', 'complete review'],
                'tools': [
                    # Parallel data gathering
                    {'tool_name': 'mcp__sage-intacct__search_across_modules',
                     'parameters': {'query': 'current period transactions', 'limit': 50}},
                    {'tool_name': 'mcp__sage-intacct__get_financial_summary',
                     'parameters': {}},
                    {'tool_name': 'mcp__sage-intacct__list_enabled_modules',
                     'parameters': {}}
                ]
            }
        }
        
        # Check message against patterns
        message_lower = message.lower()
        
        for pattern_name, pattern_config in parallel_patterns.items():
            if any(keyword in message_lower for keyword in pattern_config['keywords']):
                logger.info(f"Detected parallel execution opportunity: {pattern_name}")
                return pattern_config['tools']
        
        # Check if multiple tools were selected for the intent
        if 'tools_selected' in intent_info and len(intent_info.get('tools_selected', [])) > 1:
            # Create parallel calls for multiple independent tools
            tool_calls = []
            for tool_name in intent_info['tools_selected']:
                tool_calls.append({
                    'tool_name': tool_name,
                    'parameters': self.extract_parameters_from_message(message, tool_name)
                })
            return tool_calls
        
        return []
    
    def extract_parameters_from_message(self, message: str, tool_name: str) -> Dict[str, Any]:
        """Extract relevant parameters from the message for a specific tool"""
        import re
        params = {}
        
        # Extract date ranges
        date_pattern = r'\b(\d{4}-\d{2}-\d{2})\b'
        dates = re.findall(date_pattern, message)
        if dates:
            if len(dates) >= 2:
                params['start_date'] = dates[0]
                params['end_date'] = dates[1]
            elif len(dates) == 1:
                params['end_date'] = dates[0]
        
        # Extract modules
        modules = []
        for module in ['GL', 'AR', 'AP']:
            if module in message.upper():
                modules.append(module)
        if modules:
            params['modules'] = modules
        
        # Extract period (YYYY-MM)
        period_pattern = r'\b(\d{4}-\d{2})\b'
        periods = re.findall(period_pattern, message)
        if periods:
            params['period'] = periods[0]
        
        # Tool-specific parameter extraction
        if 'search' in tool_name.lower():
            # Extract search query
            if 'query' not in params:
                params['query'] = message
        
        if 'limit' in tool_name.lower() or 'search' in tool_name.lower():
            # Extract limit
            limit_match = re.search(r'\b(\d+)\s*(?:results?|items?|records?)\b', message)
            if limit_match:
                params['limit'] = int(limit_match.group(1))
        
        return params