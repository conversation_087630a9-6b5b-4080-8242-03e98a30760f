"""
Agent Manager Implementation

Handles agent lifecycle management, registration, and communication routing.
"""

import asyncio
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
import structlog
from pydantic import BaseModel, Field
import uuid
import time

logger = structlog.get_logger(__name__)


class AgentStatus(str, Enum):
    """Agent status enumeration."""
    INITIALIZING = "initializing"
    READY = "ready"
    BUSY = "busy"
    ERROR = "error"
    STOPPED = "stopped"


class AgentMessage(BaseModel):
    """Standard message format for agent communication."""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    agent_id: str
    message_type: str = "chat"  # chat, command, system
    content: Any
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    correlation_id: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class AgentResponse(BaseModel):
    """Standard response format from agents."""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    agent_id: str
    status: str = "success"  # success, error, partial
    content: Any
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    correlation_id: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    error: Optional[str] = None


@dataclass
class AgentInfo:
    """Agent information and metadata."""
    id: str
    name: str
    description: str
    status: AgentStatus = AgentStatus.INITIALIZING
    capabilities: List[str] = field(default_factory=list)
    started_at: Optional[datetime] = None
    last_activity: Optional[datetime] = None
    error_count: int = 0
    total_requests: int = 0
    avg_response_time: float = 0.0
    handler: Optional[Callable] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class AgentManager:
    """
    Manages agent lifecycle and communication.
    
    Implements agent registry pattern with health monitoring,
    error handling, and performance tracking.
    """
    
    def __init__(self):
        self._agents: Dict[str, AgentInfo] = {}
        self._message_handlers: Dict[str, Callable] = {}
        self._performance_metrics: Dict[str, List[float]] = {}
        self._lock = asyncio.Lock()
        self._timeout_seconds = 30.0
        self._max_retries = 3
        
        logger.info("AgentManager initialized")
    
    async def register_agent(
        self,
        agent_id: str,
        name: str,
        description: str,
        handler: Callable,
        capabilities: List[str] = None,
        metadata: Dict[str, Any] = None
    ) -> AgentInfo:
        """
        Register a new agent with the manager.
        
        Args:
            agent_id: Unique identifier for the agent
            name: Human-readable name
            description: Agent description
            handler: Async callable that handles messages
            capabilities: List of agent capabilities
            metadata: Additional agent metadata
            
        Returns:
            AgentInfo object for the registered agent
        """
        async with self._lock:
            if agent_id in self._agents:
                raise ValueError(f"Agent {agent_id} already registered")
            
            agent_info = AgentInfo(
                id=agent_id,
                name=name,
                description=description,
                capabilities=capabilities or [],
                handler=handler,
                metadata=metadata or {},
                status=AgentStatus.READY,
                started_at=datetime.now(timezone.utc)
            )
            
            self._agents[agent_id] = agent_info
            self._performance_metrics[agent_id] = []
            
            logger.info(
                "Agent registered",
                agent_id=agent_id,
                name=name,
                capabilities=capabilities
            )
            
            return agent_info
    
    async def unregister_agent(self, agent_id: str) -> bool:
        """
        Unregister an agent from the manager.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            True if successful, False otherwise
        """
        async with self._lock:
            if agent_id not in self._agents:
                return False
            
            agent = self._agents[agent_id]
            agent.status = AgentStatus.STOPPED
            
            del self._agents[agent_id]
            if agent_id in self._performance_metrics:
                del self._performance_metrics[agent_id]
            
            logger.info("Agent unregistered", agent_id=agent_id)
            return True
    
    async def get_agent(self, agent_id: str) -> Optional[AgentInfo]:
        """Get agent information by ID."""
        return self._agents.get(agent_id)
    
    async def list_agents(self) -> List[AgentInfo]:
        """Get list of all registered agents."""
        return list(self._agents.values())
    
    async def send_message(
        self,
        agent_id: str,
        content: Any,
        message_type: str = "chat",
        correlation_id: Optional[str] = None,
        metadata: Dict[str, Any] = None
    ) -> AgentResponse:
        """
        Send a message to an agent and wait for response.
        
        Args:
            agent_id: Target agent ID
            content: Message content
            message_type: Type of message
            correlation_id: Optional correlation ID for tracking
            metadata: Additional metadata
            
        Returns:
            AgentResponse from the agent
        """
        agent = self._agents.get(agent_id)
        if not agent:
            return AgentResponse(
                agent_id=agent_id,
                status="error",
                content=None,
                error=f"Agent {agent_id} not found"
            )
        
        if agent.status == AgentStatus.STOPPED:
            return AgentResponse(
                agent_id=agent_id,
                status="error",
                content=None,
                error=f"Agent {agent_id} is stopped"
            )
        
        # Create message
        message = AgentMessage(
            agent_id=agent_id,
            message_type=message_type,
            content=content,
            correlation_id=correlation_id or str(uuid.uuid4()),
            metadata=metadata or {}
        )
        
        # Update agent status
        agent.status = AgentStatus.BUSY
        agent.last_activity = datetime.now(timezone.utc)
        agent.total_requests += 1
        
        start_time = time.time()
        response = None
        
        try:
            # Send message with timeout and retries
            for attempt in range(self._max_retries):
                try:
                    response = await asyncio.wait_for(
                        agent.handler(message),
                        timeout=self._timeout_seconds
                    )
                    break
                except asyncio.TimeoutError:
                    if attempt == self._max_retries - 1:
                        raise
                    logger.warning(
                        "Agent request timeout, retrying",
                        agent_id=agent_id,
                        attempt=attempt + 1
                    )
                    await asyncio.sleep(0.5 * (attempt + 1))
            
            # Record performance
            elapsed = time.time() - start_time
            self._record_performance(agent_id, elapsed)
            
            # Ensure response is properly formatted
            if not isinstance(response, AgentResponse):
                response = AgentResponse(
                    agent_id=agent_id,
                    content=response,
                    correlation_id=message.correlation_id
                )
            
            agent.status = AgentStatus.READY
            return response
            
        except asyncio.TimeoutError:
            agent.error_count += 1
            agent.status = AgentStatus.ERROR
            logger.error(
                "Agent request timeout",
                agent_id=agent_id,
                timeout=self._timeout_seconds
            )
            return AgentResponse(
                agent_id=agent_id,
                status="error",
                content=None,
                error=f"Request timeout after {self._timeout_seconds}s",
                correlation_id=message.correlation_id
            )
            
        except Exception as e:
            agent.error_count += 1
            agent.status = AgentStatus.ERROR
            logger.error(
                "Agent request error",
                agent_id=agent_id,
                error=str(e),
                exc_info=True
            )
            return AgentResponse(
                agent_id=agent_id,
                status="error",
                content=None,
                error=str(e),
                correlation_id=message.correlation_id
            )
    
    def _record_performance(self, agent_id: str, elapsed_time: float):
        """Record agent performance metrics."""
        if agent_id not in self._performance_metrics:
            self._performance_metrics[agent_id] = []
        
        metrics = self._performance_metrics[agent_id]
        metrics.append(elapsed_time)
        
        # Keep only last 100 measurements
        if len(metrics) > 100:
            metrics.pop(0)
        
        # Update average response time
        agent = self._agents.get(agent_id)
        if agent:
            agent.avg_response_time = sum(metrics) / len(metrics)
    
    async def get_agent_status(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed status information for an agent.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            Status dictionary or None if agent not found
        """
        agent = self._agents.get(agent_id)
        if not agent:
            return None
        
        return {
            "id": agent.id,
            "name": agent.name,
            "status": agent.status.value,
            "started_at": agent.started_at.isoformat() if agent.started_at else None,
            "last_activity": agent.last_activity.isoformat() if agent.last_activity else None,
            "error_count": agent.error_count,
            "total_requests": agent.total_requests,
            "avg_response_time": round(agent.avg_response_time, 3),
            "capabilities": agent.capabilities,
            "metadata": agent.metadata
        }
    
    async def reset_agent(self, agent_id: str) -> bool:
        """
        Reset agent state and clear error counts.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            True if successful, False otherwise
        """
        agent = self._agents.get(agent_id)
        if not agent:
            return False
        
        agent.status = AgentStatus.READY
        agent.error_count = 0
        agent.last_activity = datetime.now(timezone.utc)
        
        # Clear performance metrics
        if agent_id in self._performance_metrics:
            self._performance_metrics[agent_id] = []
            agent.avg_response_time = 0.0
        
        logger.info("Agent reset", agent_id=agent_id)
        return True
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on all agents.
        
        Returns:
            Health status dictionary
        """
        healthy_count = 0
        error_count = 0
        total_count = len(self._agents)
        
        agent_statuses = []
        
        for agent in self._agents.values():
            is_healthy = agent.status in [AgentStatus.READY, AgentStatus.BUSY]
            if is_healthy:
                healthy_count += 1
            elif agent.status == AgentStatus.ERROR:
                error_count += 1
            
            agent_statuses.append({
                "id": agent.id,
                "name": agent.name,
                "status": agent.status.value,
                "healthy": is_healthy
            })
        
        return {
            "healthy": error_count == 0,
            "total_agents": total_count,
            "healthy_agents": healthy_count,
            "error_agents": error_count,
            "agents": agent_statuses
        }


# Global instance
_agent_manager: Optional[AgentManager] = None


def get_agent_manager() -> AgentManager:
    """Get the global AgentManager instance."""
    global _agent_manager
    if _agent_manager is None:
        _agent_manager = AgentManager()
    return _agent_manager
