# AR Agent Migration Notes

## Overview
The AR (Accounts Receivable) Agent has been successfully migrated to use the dynamic orchestrator architecture. All AR functionality is now accessible through the orchestrator using MCP tools.

## Migration Date
June 3, 2025

## Available AR Operations

### ✅ Customer Management
- **Query customer information** (`get_contacts`)
  - Retrieves comprehensive customer details including contact info, credit terms, credit limits, current balance, payment history, and account status
  - Useful for customer inquiries and credit management
  - Status: **Fully operational**

### ✅ AR Aging Reports
- **Generate AR aging report** (`get_sales_invoices`)
  - Produces detailed analysis of outstanding receivables organized by age buckets (current, 30, 60, 90+ days)
  - Includes customer details, invoice dates, amounts, and collection priority indicators
  - Can filter by date range for specific periods
  - Status: **Fully operational**

### ✅ Collections Workflow
- **Manage collections workflow** (`get_unallocated_artefacts`)
  - Identifies overdue accounts and unallocated payments
  - Suggests diplomatic collection actions
  - Tracks follow-up activities
  - Manages customer communications for effective receivables management
  - Status: **Fully operational**

## Unavailable AR Operations (Require MCP Server Update)

### ❌ Invoice Creation
- **Create sales invoices** 
  - Would generate new customer invoices with line items, tax calculations, payment terms, and due dates
  - Would validate customer credit before creation
  - Status: **Requires write access - currently unavailable**
  - Workaround: Use external system or wait for MCP server update

### ❌ Payment Application
- **Apply customer payments**
  - Would record received payments and apply them to specific outstanding invoices
  - Would handle partial payments, overpayments, and payment allocations
  - Status: **Requires write access - currently unavailable**
  - Workaround: Use external system or wait for MCP server update

### ❌ Credit Memo Processing
- **Create and apply credit memos**
  - Would create credit adjustments for customer accounts
  - Would apply credits to specific invoices
  - Status: **Requires write access - currently unavailable**
  - Workaround: Use external system or wait for MCP server update

## MCP Server Limitations

The Sage Intacct MCP server currently has the following limitations for AR operations:

1. **Module Status**: AR module is available but write operations are not enabled
2. **Read-Only Access**: All queries and reports work perfectly
3. **No Write Access**: Cannot create or modify invoices, payments, or credit memos
4. **Tool Mapping**: Some tool names differ between agent methods and MCP tools

## Usage Changes

### Before Migration
```python
# Direct AR agent usage
from agents.intacct import ar_agent_instance
result = await ar_agent_instance.send("Show customer balance for ABC Corp")
```

### After Migration
```python
# Through orchestrator
from agents.orchestrator import Orchestrator
orchestrator = Orchestrator(registry, discovery)
result = await orchestrator.process("Show customer balance for ABC Corp")
```

### Alternative: Using AR wrapper in orchestrator agent
```python
# The orchestrator agent still has an AR wrapper for compatibility
from agents.intacct import orchestrator_agent_instance
result = await orchestrator_agent_instance.send("AR: Show customer balance for ABC Corp")
```

## Benefits of Migration

1. **Unified Interface**: All AR operations through single orchestrator
2. **Dynamic Tool Discovery**: Automatically uses available MCP tools
3. **Better Error Handling**: Graceful degradation for unavailable tools
4. **Consistent Responses**: Standardized response format across all operations
5. **Future-Ready**: Easy to add new AR tools as they become available

## Recommendations

1. **For Read Operations**: Use orchestrator freely - all read operations work perfectly
2. **For Write Operations**: Wait for MCP server update or use external system
3. **For Complex Workflows**: Orchestrator can combine AR with other modules seamlessly
4. **For Collections**: Full collections workflow available through orchestrator

## Next Steps

1. Monitor MCP server updates for write operation availability
2. Update tool mappings when new AR tools are added
3. Consider implementing placeholder responses for write operations
4. Document any workarounds needed for write operations