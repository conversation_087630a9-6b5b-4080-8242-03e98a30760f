"""
MCP Client Wrapper using FastMCP

This module provides a wrapper around FastMCP client for direct MCP server communication.
It handles connection management, tool discovery, and tool execution without the
FastAgent framework overhead.
"""

import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone
import structlog
from pathlib import Path
import os

# FastMCP imports
from fastmcp import Client
from mcp.types import Tool, CallToolResult

logger = structlog.get_logger(__name__)


class MCPClientWrapper:
    """
    Wrapper around FastMCP client for MCP server communication.
    
    This class provides:
    - Connection management for different transport types (SSE, HTTP, stdio)
    - Tool discovery and caching
    - Direct tool execution
    - Error handling and retries
    """
    
    def __init__(self, server_config: Dict[str, Any]):
        """
        Initialize MCP client wrapper.
        
        Args:
            server_config: Server configuration containing:
                - name: Server name
                - command: Command to run (for stdio transport)
                - args: Command arguments
                - env: Environment variables
                - metadata: Additional metadata (transport, url, headers, etc.)
        """
        self.server_name = server_config.get('name', 'unknown')
        self.config = server_config
        self.client: Optional[Client] = None
        self._tools_cache: Dict[str, Tool] = {}
        self._connected = False
        self._last_error: Optional[str] = None
        
        logger.info(f"Initializing MCP client for {self.server_name}")
    
    def _create_client(self):
        """
        Create the MCP client based on configuration.
        This only creates the client, doesn't connect it.
        """
        try:
            # Determine transport type and connection parameters
            metadata = self.config.get('metadata', {})
            transport = metadata.get('transport', 'stdio')
            
            if transport == 'stdio':
                # For stdio transport, use command and args
                command = self.config.get('command', '')
                args = self.config.get('args', [])
                
                if not command:
                    raise ValueError(f"No command specified for stdio transport in {self.server_name}")
                
                # For stdio, we need to check if it's a Python script or executable
                if command.endswith('.py') or (args and args[0].endswith('.py')):
                    # Use PythonStdioTransport for Python scripts
                    from fastmcp.client.transports import PythonStdioTransport
                    script_path = args[0] if args and args[0].endswith('.py') else command
                    
                    # Set environment variables if provided
                    env = None
                    if self.config.get('env'):
                        env = os.environ.copy()
                        env.update(self.config['env'])
                    
                    transport_obj = PythonStdioTransport(
                        script_path=script_path,
                        env=env
                    )
                    self.client = Client(transport_obj)
                else:
                    # For other executables, just pass as command string
                    full_command = [command] + args
                    self.client = Client(' '.join(full_command))
                
            elif transport in ['sse', 'http']:
                # For HTTP/SSE transport, use URL
                url = metadata.get('url') or metadata.get('endpoint')
                if not url:
                    raise ValueError(f"No URL specified for {transport} transport in {self.server_name}")
                
                # Get headers if provided
                headers = metadata.get('headers', {})
                
                # For HTTP/SSE, we need to create the transport explicitly
                if transport == 'sse':
                    from fastmcp.client.transports import SSETransport
                    transport_obj = SSETransport(url=url, headers=headers)
                    self.client = Client(transport_obj)
                else:
                    # For HTTP, use StreamableHttpTransport (default)
                    from fastmcp.client.transports import StreamableHttpTransport
                    transport_obj = StreamableHttpTransport(url=url, headers=headers)
                    self.client = Client(transport_obj)
                
            else:
                raise ValueError(f"Unsupported transport type: {transport}")
                
        except Exception as e:
            self._last_error = str(e)
            logger.error(f"Failed to create client for {self.server_name}", error=str(e))
            raise
    
    async def connect(self) -> bool:
        """
        Establish connection to the MCP server.
        Note: This creates the client but doesn't enter the context.
        The actual connection happens when using call_tool or list_tools.
        
        Returns:
            True if client created successfully, False otherwise
        """
        try:
            if not self.client:
                self._create_client()
            
            self._connected = True
            logger.info(f"Client created for {self.server_name}")
            return True
            
        except Exception as e:
            self._last_error = str(e)
            logger.error(f"Failed to create client for {self.server_name}", error=str(e))
            return False
    
    async def disconnect(self):
        """Disconnect from the MCP server."""
        # Since we don't maintain a persistent connection, just mark as disconnected
        self._connected = False
        self.client = None
        self._tools_cache.clear()
        logger.info(f"Disconnected from {self.server_name}")
    
    async def _discover_tools(self):
        """Discover available tools from the connected server."""
        if not self._connected or not self.client:
            logger.warning(f"Cannot discover tools - not connected to {self.server_name}")
            return
        
        try:
            # Use client context to list tools
            async with self.client as active_client:
                # List tools using MCP protocol
                result = await active_client.list_tools()
                
                # Cache tools for quick lookup
                self._tools_cache.clear()
                
                # Handle both list and object with .tools attribute
                if isinstance(result, list):
                    # FastMCP returns a list directly
                    tools = result
                elif hasattr(result, 'tools'):
                    # Standard MCP format
                    tools = result.tools
                else:
                    logger.warning(f"Unexpected result type from list_tools: {type(result)}")
                    tools = []
                
                for tool in tools:
                    self._tools_cache[tool.name] = tool
                
                logger.info(f"Discovered {len(self._tools_cache)} tools from {self.server_name}")
            
        except Exception as e:
            logger.error(f"Failed to discover tools from {self.server_name}", error=str(e))
    
    async def list_tools(self) -> List[Tool]:
        """
        Get list of available tools.
        
        Returns:
            List of Tool objects
        """
        if not self._connected:
            await self.connect()
        
        # Discover tools if cache is empty
        if not self._tools_cache:
            await self._discover_tools()
        
        return list(self._tools_cache.values())
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """
        Execute a tool and return results.
        
        Args:
            tool_name: Name of the tool to execute
            arguments: Tool arguments
            
        Returns:
            Tool execution result
            
        Raises:
            Exception: If tool execution fails
        """
        if not self._connected:
            success = await self.connect()
            if not success:
                raise ConnectionError(f"Failed to connect to {self.server_name}: {self._last_error}")
        
        # For now, skip tool validation since discovery has context issues
        # We'll rely on the server to validate tool existence
        
        try:
            # Execute the tool within client context
            logger.info(f"Executing tool {tool_name} on {self.server_name}", arguments=arguments)
            
            async with self.client as active_client:
                result = await active_client.call_tool(tool_name, arguments)
                
                # Extract content from result
                if hasattr(result, 'content'):
                    # Handle different content types
                    if isinstance(result.content, list):
                        # Multiple content items
                        contents = []
                        for item in result.content:
                            if hasattr(item, 'text'):
                                contents.append(item.text)
                            elif hasattr(item, 'type') and item.type == 'text':
                                contents.append(getattr(item, 'text', str(item)))
                            else:
                                contents.append(str(item))
                        return '\n'.join(contents) if contents else str(result.content)
                    else:
                        # Single content item
                        return result.content
                else:
                    # Return raw result if no content attribute
                    return result
                
        except Exception as e:
            logger.error(f"Failed to execute tool {tool_name} on {self.server_name}", 
                        error=str(e), arguments=arguments)
            raise
    
    async def get_tool_schema(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """
        Get the schema for a specific tool.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Tool schema dictionary or None if not found
        """
        tool = self._tools_cache.get(tool_name)
        if not tool:
            return None
        
        # Convert tool to schema dict
        schema = {
            "name": tool.name,
            "description": tool.description or "",
            "parameters": {}
        }
        
        # Extract parameter schema
        if hasattr(tool, 'inputSchema'):
            if hasattr(tool.inputSchema, 'model_dump'):
                schema["parameters"] = tool.inputSchema.model_dump()
            elif isinstance(tool.inputSchema, dict):
                schema["parameters"] = tool.inputSchema
            else:
                # Try to convert to dict
                schema["parameters"] = {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
        
        return schema
    
    def is_connected(self) -> bool:
        """Check if client is connected."""
        return self._connected
    
    def get_last_error(self) -> Optional[str]:
        """Get the last error message."""
        return self._last_error
    
    async def health_check(self) -> bool:
        """
        Perform a health check on the connection.
        
        Returns:
            True if healthy, False otherwise
        """
        if not self._connected or not self.client:
            return False
        
        try:
            # Try to list tools as a health check within context
            async with self.client as active_client:
                await active_client.list_tools()
                return True
        except Exception as e:
            logger.error(f"Health check failed for {self.server_name}", error=str(e))
            return False