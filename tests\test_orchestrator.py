"""
Test suite for the Dynamic Orchestrator Agent
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock

from agents.orchestrator import DynamicOrchestrator, OrchestratorAgent


@pytest.fixture
async def mock_registry():
    """Mock MCP Registry for testing"""
    registry = AsyncMock()
    registry.list_servers = AsyncMock(return_value={
        "sage-intacct": {"name": "sage-intacct", "type": "tcp"}
    })
    registry.get_server_tools = AsyncMock(return_value=[
        Mock(name="get_gl_balance", description="Get GL account balance"),
        Mock(name="create_invoice", description="Create AR invoice")
    ])
    return registry


@pytest.fixture
async def mock_discovery():
    """Mock Tool Discovery Service for testing"""
    discovery = AsyncMock()
    discovery.get_tool_catalog = AsyncMock(return_value={
        "total_tools": 10,
        "by_category": {
            "general_ledger": [
                {"full_name": "sage-intacct:get_gl_balance", "name": "get_gl_balance", 
                 "description": "Get GL account balance"}
            ],
            "accounts_receivable": [
                {"full_name": "sage-intacct:create_invoice", "name": "create_invoice",
                 "description": "Create AR invoice"}
            ]
        }
    })
    discovery.search_tools = AsyncMock(return_value=[
        {"full_name": "sage-intacct:get_gl_balance", "description": "Get GL account balance"},
        {"full_name": "sage-intacct:get_trial_balance", "description": "Get trial balance"}
    ])
    return discovery


class TestDynamicOrchestrator:
    """Test the Dynamic Orchestrator implementation"""
    
    @pytest.mark.asyncio
    async def test_initialization(self, mock_registry, mock_discovery):
        """Test orchestrator initialization"""
        with patch('agents.orchestrator.MCPRegistry', return_value=mock_registry):
            with patch('agents.orchestrator.ToolDiscoveryService', return_value=mock_discovery):
                orchestrator = DynamicOrchestrator()
                
                assert orchestrator.registry == mock_registry
                assert orchestrator.discovery == mock_discovery
                assert not orchestrator._initialized
    
    @pytest.mark.asyncio
    async def test_intent_detection_high_confidence(self):
        """Test intent detection with high confidence"""
        orchestrator = DynamicOrchestrator()
        
        # Test GL balance intent
        intent_info = await orchestrator._detect_intent("What's the balance in account 1000?")
        assert intent_info['primary_intent'] == 'gl_balance'
        assert intent_info['confidence'] > 0.8
        
        # Test journal entry intent
        intent_info = await orchestrator._detect_intent("Create a journal entry for $1000")
        assert intent_info['primary_intent'] == 'journal_entry'
        assert intent_info['confidence'] > 0.8
        
        # Test month-end close intent
        intent_info = await orchestrator._detect_intent("Run month-end close for January 2024")
        assert intent_info['primary_intent'] == 'month_end_close'
        assert intent_info['confidence'] > 0.9
    
    @pytest.mark.asyncio
    async def test_intent_detection_low_confidence(self):
        """Test intent detection with low confidence"""
        orchestrator = DynamicOrchestrator()
        
        # Ambiguous query
        intent_info = await orchestrator._detect_intent("Show me the numbers")
        assert intent_info['confidence'] < 0.6
    
    @pytest.mark.asyncio
    async def test_tool_selection(self, mock_discovery):
        """Test tool selection based on intent"""
        with patch('agents.orchestrator.ToolDiscoveryService', return_value=mock_discovery):
            orchestrator = DynamicOrchestrator()
            orchestrator.discovery = mock_discovery
            
            # Test GL balance tools
            tools = await orchestrator._select_tools('gl_balance', 'account balance')
            assert len(tools) <= 3
            assert 'sage-intacct:get_gl_balance' in tools
    
    @pytest.mark.asyncio
    async def test_low_confidence_handling(self):
        """Test handling of low confidence scenarios"""
        orchestrator = DynamicOrchestrator()
        
        intent_info = {
            'primary_intent': 'general_query',
            'confidence': 0.4,
            'all_intents': [
                {'intent': 'gl_balance', 'confidence': 0.4},
                {'intent': 'customer_balance', 'confidence': 0.3}
            ]
        }
        
        result = await orchestrator._handle_low_confidence("Show balance", intent_info)
        
        assert "I want to help you" in result['response']
        assert "clarify" in result['response']
        assert result['metadata']['intent'] == 'clarification_needed'
    
    @pytest.mark.asyncio
    async def test_add_mcp_server(self, mock_registry):
        """Test adding a new MCP server"""
        with patch('agents.orchestrator.MCPRegistry', return_value=mock_registry):
            orchestrator = DynamicOrchestrator()
            
            server_config = {
                'name': 'new-server',
                'type': 'tcp',
                'port': 3333
            }
            
            result = await orchestrator.add_mcp_server(server_config)
            
            assert result['status'] == 'success'
            assert result['server'] == 'new-server'
            mock_registry.register_server.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_remove_mcp_server(self, mock_registry):
        """Test removing an MCP server"""
        with patch('agents.orchestrator.MCPRegistry', return_value=mock_registry):
            orchestrator = DynamicOrchestrator()
            
            result = await orchestrator.remove_mcp_server('test-server')
            
            assert result['status'] == 'success'
            mock_registry.unregister_server.assert_called_once_with('test-server')


class TestOrchestratorAgent:
    """Test the OrchestratorAgent wrapper"""
    
    @pytest.mark.asyncio
    async def test_send_message(self):
        """Test sending a message through the wrapper"""
        agent = OrchestratorAgent()
        
        # Mock the orchestrator's process method
        with patch.object(agent.orchestrator, 'process', new_callable=AsyncMock) as mock_process:
            mock_process.return_value = {
                "response": "Here's the GL balance...",
                "metadata": {"intent": "gl_balance", "confidence": 0.9}
            }
            
            result = await agent.send("What's the GL balance?")
            
            assert result['agent_id'] == 'orchestrator_agent'
            assert result['response'] == "Here's the GL balance..."
            assert result['metadata']['intent'] == 'gl_balance'
            mock_process.assert_called_once()


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
