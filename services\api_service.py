"""
AI Workspace Agent Service

FastAPI service that provides agent capabilities using fast-agent framework.
"""

import asyncio
import json
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional

import structlog
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from services.config import Settings, get_settings
from services.log_setup import setup_logging
from services.agent_manager import (
    get_agent_manager,
    AgentManager,
    AgentResponse,
    AgentStatus,
    AgentInfo
)
from config.dynamic_mcp_config import (
    get_dynamic_mcp_config,
    DynamicMCPServerConfig
)
from services.mcp_registry import MCPServerRegistry
from services.tool_discovery import ToolDiscoveryService

# Setup structured logging
logger = structlog.get_logger()


# Pydantic models for API
class HealthResponse(BaseModel):
    """Health check response model"""
    status: str = Field(description="Service health status")
    version: str = Field(description="API version")
    agents: List[Dict[str, Any]] = Field(description="List of available agents with status")
    timestamp: str = Field(description="Current timestamp")


class AgentChatRequest(BaseModel):
    """Agent chat request model"""
    message: str = Field(description="Message to send to agent")
    context: Optional[Dict[str, Any]] = Field(
        default={}, description="Additional context for the agent"
    )
    correlation_id: Optional[str] = Field(
        default=None, description="Optional correlation ID for tracking"
    )


class AgentChatResponse(BaseModel):
    """Agent chat response model"""
    response: Any = Field(description="Agent response")
    agent_id: str = Field(description="ID of the responding agent")
    timestamp: str = Field(description="Response timestamp")
    status: str = Field(description="Response status")
    correlation_id: Optional[str] = Field(default=None, description="Correlation ID")
    error: Optional[str] = Field(default=None, description="Error message if any")
    metadata: Optional[Dict[str, Any]] = Field(
        default={}, description="Additional response metadata"
    )


class AgentStatusResponse(BaseModel):
    """Agent status response model"""
    id: str
    name: str
    status: str
    started_at: Optional[str]
    last_activity: Optional[str]
    error_count: int
    total_requests: int
    avg_response_time: float
    capabilities: List[str]
    metadata: Dict[str, Any]


class AgentListResponse(BaseModel):
    """Agent list response model"""
    agents: List[AgentStatusResponse]
    total: int


# MCP Server Models
class MCPServerCreateRequest(BaseModel):
    """Request model for creating an MCP server"""
    name: str = Field(description="Unique server name")
    command: str = Field(description="Command to run the server")
    args: List[str] = Field(default=[], description="Command arguments")
    env: Dict[str, str] = Field(default={}, description="Environment variables")
    connection_type: str = Field(default="local", description="Connection type: local, hosted, sse")
    endpoint: Optional[str] = Field(default=None, description="Server endpoint (for hosted/sse)")
    health_check: Dict[str, Any] = Field(
        default={"enabled": True, "interval": 30, "timeout": 10},
        description="Health check configuration"
    )
    tags: List[str] = Field(default=[], description="Tags for categorization")
    metadata: Dict[str, Any] = Field(default={}, description="Additional metadata")
    persistent: bool = Field(default=True, description="Whether to persist configuration")


class MCPServerUpdateRequest(BaseModel):
    """Request model for updating an MCP server"""
    command: Optional[str] = Field(None, description="Command to run the server")
    args: Optional[List[str]] = Field(None, description="Command arguments")
    env: Optional[Dict[str, str]] = Field(None, description="Environment variables to update")
    endpoint: Optional[str] = Field(None, description="Server endpoint")
    connection_type: Optional[str] = Field(None, description="Connection type")
    health_check: Optional[Dict[str, Any]] = Field(None, description="Health check configuration")
    tags: Optional[List[str]] = Field(None, description="Tags for categorization")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class MCPServerAuthRequest(BaseModel):
    """Request model for updating MCP server authentication"""
    auth_env: Dict[str, str] = Field(description="Authentication environment variables")
    validate_auth: bool = Field(default=True, description="Whether to validate authentication")


class MCPServerInfo(BaseModel):
    """MCP server information"""
    name: str
    connection_type: str
    endpoint: Optional[str]
    status: Optional[str]
    health_check_enabled: bool
    tags: List[str]
    source: str
    added_at: Optional[str]


class MCPServerListResponse(BaseModel):
    """Response model for listing MCP servers"""
    servers: List[MCPServerInfo]
    total: int


class ToolInfo(BaseModel):
    """Tool information"""
    name: str
    description: str
    server_name: str
    category: str
    capabilities: List[str]
    keywords: List[str]
    full_name: str


class ToolListResponse(BaseModel):
    """Response model for listing tools"""
    tools: List[ToolInfo]
    total: int
    servers: List[str]
    categories: List[str]


# Application lifespan management
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Handle application startup and shutdown"""
    # Startup
    logger.info("Starting AI Workspace Agent Service")
    
    # Validate settings (including API key) at startup
    try:
        settings = get_settings()
        logger.info("Settings validated successfully", openai_configured=bool(settings.openai_api_key))
    except ValueError as e:
        logger.error("Configuration error", error=str(e))
        raise RuntimeError(f"Configuration error: {e}")
    
    app.state.agent_manager = get_agent_manager()
    
    # Initialize MCP registry and tool discovery - use singleton
    from services.mcp_registry import get_mcp_registry
    app.state.mcp_registry = get_mcp_registry()
    app.state.tool_discovery = ToolDiscoveryService(app.state.mcp_registry)
    
    # Set registry in dynamic config
    config = get_dynamic_mcp_config()
    config.set_registry(app.state.mcp_registry)
    
    # Initialize registry with configured servers
    await config.initialize_registry()
    
    # Register all agents
    from services.agent_registration import register_all_agents, unregister_all_agents
    await register_all_agents()
    
    yield
    
    # Shutdown
    logger.info("Shutting down AI Workspace Agent Service")
    await unregister_all_agents()


# Create FastAPI application
app = FastAPI(
    title="AI Workspace Agent Service",
    description="Agent service for AI Workspace using fast-agent",
    version="0.1.0",
    lifespan=lifespan,
)


# Configure CORS middleware
settings = get_settings()
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# API Routes
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    from datetime import datetime, timezone
    
    # Get health status from agent manager
    health_status = await app.state.agent_manager.health_check()
    
    return HealthResponse(
        status="healthy" if health_status["healthy"] else "degraded",
        version=settings.api_version,
        agents=health_status["agents"],
        timestamp=datetime.now(timezone.utc).isoformat() + "Z",
    )


@app.get("/agents", response_model=AgentListResponse)
async def list_agents():
    """List all available agents with their status"""
    agents = await app.state.agent_manager.list_agents()
    
    agent_responses = []
    for agent in agents:
        status_data = await app.state.agent_manager.get_agent_status(agent.id)
        if status_data:
            agent_responses.append(AgentStatusResponse(**status_data))
    
    return AgentListResponse(
        agents=agent_responses,
        total=len(agent_responses)
    )


@app.get("/agents/{agent_id}/status", response_model=AgentStatusResponse)
async def get_agent_status(agent_id: str):
    """Get detailed status of a specific agent"""
    status = await app.state.agent_manager.get_agent_status(agent_id)
    
    if not status:
        raise HTTPException(
            status_code=404,
            detail=f"Agent {agent_id} not found"
        )
    
    return AgentStatusResponse(**status)


@app.post("/agents/{agent_id}/reset")
async def reset_agent(agent_id: str):
    """Reset agent state and clear error counts"""
    success = await app.state.agent_manager.reset_agent(agent_id)
    
    if not success:
        raise HTTPException(
            status_code=404,
            detail=f"Agent {agent_id} not found"
        )
    
    return {"status": "success", "message": f"Agent {agent_id} reset successfully"}


@app.get("/agents/{agent_id}/history")
async def get_agent_history(agent_id: str, limit: int = 50):
    """Get conversation history for an agent (placeholder for now)"""
    # This will be implemented when we have proper message storage
    return {
        "agent_id": agent_id,
        "history": [],
        "total": 0,
        "limit": limit
    }


@app.post("/agents/{agent_id}/chat", response_model=AgentChatResponse)
async def chat_with_agent(
    agent_id: str, request: AgentChatRequest
) -> AgentChatResponse:
    """REST endpoint for single message exchange with an agent"""
    from datetime import datetime, timezone
    
    try:
        # Send message to agent
        agent_response = await app.state.agent_manager.send_message(
            agent_id=agent_id,
            content=request.message,
            message_type="chat",
            correlation_id=request.correlation_id,
            metadata=request.context
        )
        
        return AgentChatResponse(
            response=agent_response.content,
            agent_id=agent_response.agent_id,
            timestamp=agent_response.timestamp.isoformat() + "Z",
            status=agent_response.status,
            correlation_id=agent_response.correlation_id,
            error=agent_response.error,
            metadata=agent_response.metadata
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@app.websocket("/agents/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time agent communication"""
    await websocket.accept()
    logger.info("WebSocket connection established")
    
    try:
        while True:
            try:
                # Receive message from client
                raw_data = await websocket.receive()
                
                # Handle different message types
                if "text" in raw_data:
                    # Try to parse as JSON
                    try:
                        data = json.loads(raw_data["text"])
                    except json.JSONDecodeError:
                        await websocket.send_json({
                            "type": "error",
                            "error": "Invalid message format"
                        })
                        continue
                elif "json" in raw_data:
                    data = raw_data["json"]
                else:
                    await websocket.send_json({
                        "type": "error",
                        "error": "Invalid message format"
                    })
                    continue
                
                # Handle special message types
                message_type = data.get("type")
                if message_type == "ping":
                    await websocket.send_json({"type": "pong"})
                    continue
                elif message_type == "heartbeat":
                    from datetime import datetime, timezone
                    await websocket.send_json({
                        "type": "heartbeat",
                        "timestamp": datetime.now(timezone.utc).isoformat() + "Z"
                    })
                    continue
                
                # Extract fields (content or message field)
                agent_id = data.get("agent_id")
                message = data.get("content") or data.get("message")
                context = data.get("context", {})
                correlation_id = data.get("correlation_id")
                stream = data.get("stream", False)
                
                if not agent_id:
                    await websocket.send_json({
                        "type": "error",
                        "error": "Missing required field: agent_id"
                    })
                    continue
                    
                if not message:
                    await websocket.send_json({
                        "type": "error",
                        "error": "Missing required field: content or message"
                    })
                    continue
                
                try:
                    # Process with agent
                    agent_response = await app.state.agent_manager.send_message(
                        agent_id=agent_id,
                        content=message,
                        message_type="chat",
                        correlation_id=correlation_id,
                        metadata=context
                    )
                    
                    # Send response back
                    await websocket.send_json({
                        "type": "response",
                        "agent_id": agent_response.agent_id,
                        "content": agent_response.content,
                        "timestamp": agent_response.timestamp.isoformat() + "Z",
                        "status": agent_response.status,
                        "correlation_id": agent_response.correlation_id,
                        "error": agent_response.error,
                        "metadata": agent_response.metadata
                    })
                except Exception as e:
                    logger.error(f"WebSocket error: {e}", exc_info=True)
                    await websocket.send_json({
                        "type": "error",
                        "error": str(e),
                        "correlation_id": correlation_id
                    })
            except json.JSONDecodeError:
                await websocket.send_json({
                    "type": "error", 
                    "error": "Invalid JSON format"
                })
            except Exception as e:
                logger.error(f"Message handling error: {e}", exc_info=True)
                await websocket.send_json({
                    "type": "error",
                    "error": "Internal server error"
                })
                
    except WebSocketDisconnect:
        logger.info("WebSocket connection closed")
    except Exception as e:
        logger.error(f"WebSocket error: {e}", exc_info=True)
        await websocket.close()


# MCP Server Management Endpoints
@app.post("/mcp/servers", response_model=MCPServerInfo)
async def add_mcp_server(request: MCPServerCreateRequest):
    """Add a new MCP server at runtime"""
    try:
        config = get_dynamic_mcp_config()
        
        # Add the server
        server = await config.add_server(
            name=request.name,
            command=request.command,
            args=request.args,
            env=request.env,
            health_check=request.health_check,
            connection_type=request.connection_type,
            endpoint=request.endpoint,
            tags=request.tags,
            metadata=request.metadata,
            persistent=request.persistent
        )
        
        # Get server status from registry if available
        status = None
        if hasattr(app.state, 'mcp_registry'):
            status = app.state.mcp_registry.get_server_status(request.name)
        
        return MCPServerInfo(
            name=server.name,
            connection_type=server.connection_type,
            endpoint=server.endpoint,
            status=status,
            health_check_enabled=server.health_check.get("enabled", True),
            tags=server.tags,
            source=server.source,
            added_at=server.added_at.isoformat() if server.added_at else None
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error adding MCP server: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to add MCP server")


@app.delete("/mcp/servers/{server_name}")
async def remove_mcp_server(server_name: str, force: bool = False):
    """Remove an MCP server"""
    try:
        config = get_dynamic_mcp_config()
        removed = await config.remove_server(server_name, force=force)
        
        if not removed:
            raise HTTPException(
                status_code=404,
                detail=f"MCP server '{server_name}' not found"
            )
        
        return {"status": "success", "message": f"MCP server '{server_name}' removed"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error removing MCP server: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to remove MCP server")


@app.patch("/mcp/servers/{server_name}")
async def update_mcp_server(server_name: str, request: MCPServerUpdateRequest):
    """Update an existing MCP server configuration"""
    try:
        config = get_dynamic_mcp_config()
        
        # Convert request to dict and filter None values
        update_data = {k: v for k, v in request.dict().items() if v is not None}
        
        server = await config.update_server(server_name, **update_data)
        
        # Get server status from registry if available
        status = None
        if hasattr(app.state, 'mcp_registry'):
            status = app.state.mcp_registry.get_server_status(server_name)
        
        return MCPServerInfo(
            name=server.name,
            connection_type=server.connection_type,
            endpoint=server.endpoint,
            status=status,
            health_check_enabled=server.health_check.get("enabled", True),
            tags=server.tags,
            source=server.source,
            added_at=server.added_at.isoformat() if server.added_at else None
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating MCP server: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to update MCP server")


@app.post("/mcp/servers/{server_name}/auth")
async def update_mcp_server_auth(server_name: str, request: MCPServerAuthRequest):
    """Update authentication for an MCP server"""
    try:
        config = get_dynamic_mcp_config()
        await config.update_server_auth(
            server_name,
            auth_env=request.auth_env,
            validate=request.validate_auth
        )
        
        return {"status": "success", "message": f"Authentication updated for '{server_name}'"}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating MCP server auth: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to update authentication")


@app.get("/mcp/servers", response_model=MCPServerListResponse)
async def list_mcp_servers(tags: Optional[str] = None):
    """List all MCP servers"""
    try:
        config = get_dynamic_mcp_config()
        
        # Parse tags parameter
        tag_list = tags.split(",") if tags else None
        
        servers = config.list_servers(tags=tag_list)
        
        server_infos = []
        for server in servers:
            server_infos.append(MCPServerInfo(
                name=server["name"],
                connection_type=server["connection_type"],
                endpoint=server.get("endpoint"),
                status=server.get("status"),
                health_check_enabled=server["health_check_enabled"],
                tags=server["tags"],
                source=server["source"],
                added_at=server["added_at"].isoformat() if server.get("added_at") else None
            ))
        
        return MCPServerListResponse(
            servers=server_infos,
            total=len(server_infos)
        )
    except Exception as e:
        logger.error(f"Error listing MCP servers: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to list MCP servers")


@app.get("/mcp/servers/{server_name}/health")
async def check_mcp_server_health(server_name: str):
    """Check health of a specific MCP server"""
    try:
        if not hasattr(app.state, 'mcp_registry'):
            raise HTTPException(
                status_code=503,
                detail="MCP registry not available"
            )
        
        registry = app.state.mcp_registry
        server_info = registry.get_server_info(server_name)
        
        if not server_info:
            raise HTTPException(
                status_code=404,
                detail=f"MCP server '{server_name}' not found"
            )
        
        # Trigger health check
        await registry.check_server_health(server_name)
        
        return {
            "server": server_name,
            "status": server_info.status.value,
            "last_health_check": server_info.last_health_check.isoformat() if server_info.last_health_check else None,
            "error_count": server_info.error_count,
            "connected_at": server_info.connected_at.isoformat() if server_info.connected_at else None
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking MCP server health: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to check server health")


@app.get("/mcp/tools", response_model=ToolListResponse)
async def list_all_tools(
    query: Optional[str] = None,
    category: Optional[str] = None,
    capability: Optional[str] = None,
    server: Optional[str] = None,
    limit: int = 100
):
    """List all available tools from all MCP servers"""
    try:
        if not hasattr(app.state, 'tool_discovery'):
            # Initialize tool discovery service if not available
            registry = getattr(app.state, 'mcp_registry', MCPServerRegistry())
            app.state.tool_discovery = ToolDiscoveryService(registry)
        
        discovery = app.state.tool_discovery
        
        # Search for tools
        if query:
            tools = await discovery.search_tools(query, limit=limit)
        else:
            # Get all tools and filter
            catalog = await discovery.get_tool_catalog()
            tools = list(catalog.values())
            
            # Apply filters
            if category:
                tools = [t for t in tools if t.category == category]
            if capability:
                tools = [t for t in tools if capability in t.capabilities]
            if server:
                tools = [t for t in tools if t.server_name == server]
            
            # Limit results
            tools = tools[:limit]
        
        # Convert to response format
        tool_infos = []
        for tool in tools:
            tool_infos.append(ToolInfo(
                name=tool.name,
                description=tool.description,
                server_name=tool.server_name,
                category=tool.category,
                capabilities=tool.capabilities,
                keywords=tool.keywords,
                full_name=tool.full_name
            ))
        
        # Get unique servers and categories
        all_catalog = await discovery.get_tool_catalog()
        all_tools = list(all_catalog.values())
        servers = list(set(t.server_name for t in all_tools))
        # Extract unique categories from tools
        categories = list(set(str(t.category) for t in all_tools if hasattr(t, 'category')))
        
        return ToolListResponse(
            tools=tool_infos,
            total=len(tool_infos),
            servers=servers,
            categories=categories
        )
    except Exception as e:
        logger.error(f"Error listing tools: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to list tools")


@app.get("/mcp/tools/{tool_name}")
async def get_tool_details(tool_name: str):
    """Get detailed information about a specific tool"""
    try:
        if not hasattr(app.state, 'tool_discovery'):
            registry = getattr(app.state, 'mcp_registry', MCPServerRegistry())
            app.state.tool_discovery = ToolDiscoveryService(registry)
        
        discovery = app.state.tool_discovery
        
        # Search for the tool
        results = await discovery.search_tools(tool_name, limit=1)
        
        if not results:
            raise HTTPException(
                status_code=404,
                detail=f"Tool '{tool_name}' not found"
            )
        
        tool = results[0]
        
        # Get related tools
        related_tools = await discovery.get_related_tools(tool.full_name, limit=5) if hasattr(discovery, 'get_related_tools') else []
        
        return {
            "tool": {
                "name": tool.name,
                "full_name": tool.full_name,
                "description": tool.description,
                "server_name": tool.server_name,
                "category": tool.category,
                "capabilities": tool.capabilities,
                "keywords": tool.keywords,
                "parameters": tool.parameters
            },
            "related_tools": [
                {
                    "name": rt.name,
                    "full_name": rt.full_name,
                    "description": rt.description,
                    "server_name": rt.server_name
                }
                for rt in related_tools
            ]
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting tool details: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to get tool details")


# Feedback Models
class FeedbackRequest(BaseModel):
    """Request model for recording user feedback"""
    interaction_id: str = Field(description="Unique ID for the interaction")
    user_feedback: str = Field(description="Feedback type: correct, incorrect, partial")
    metadata: Dict[str, Any] = Field(description="Interaction metadata including intent and confidence")


@app.post("/feedback")
async def record_feedback(request: FeedbackRequest):
    """Record user feedback for confidence learning"""
    try:
        from agents.orchestrator import orchestrator
        
        # Validate feedback type
        if request.user_feedback not in ["correct", "incorrect", "partial"]:
            raise HTTPException(
                status_code=400,
                detail="Invalid feedback type. Must be: correct, incorrect, or partial"
            )
        
        # Record feedback
        await orchestrator.record_feedback(
            interaction_id=request.interaction_id,
            user_feedback=request.user_feedback,
            metadata=request.metadata
        )
        
        return {
            "status": "success",
            "message": f"Feedback recorded for interaction {request.interaction_id}"
        }
    except Exception as e:
        logger.error(f"Error recording feedback: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to record feedback")


@app.get("/confidence/stats")
async def get_confidence_stats():
    """Get confidence scoring statistics"""
    try:
        from agents.orchestrator import orchestrator
        
        # Get success rates from confidence scorer
        scorer = orchestrator.confidence_scorer
        success_rates = scorer.feedback_data.get('success_rates', {})
        total_interactions = len(scorer.feedback_data.get('interactions', []))
        
        # Calculate overall statistics
        stats = {
            "total_interactions": total_interactions,
            "success_rates_by_intent": success_rates,
            "overall_success_rate": 0.0,
            "confidence_thresholds": {
                "high": 0.8,
                "medium": 0.6,
                "low": 0.0
            }
        }
        
        # Calculate overall success rate
        if success_rates:
            total_correct = sum(sr.get('correct', 0) for sr in success_rates.values())
            total_attempts = sum(sr.get('total', 0) for sr in success_rates.values())
            if total_attempts > 0:
                stats['overall_success_rate'] = total_correct / total_attempts
        
        return stats
    except Exception as e:
        logger.error(f"Error getting confidence stats: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to get confidence statistics")


# Workflow endpoints
@app.get("/workflows", response_model=Dict[str, Any])
async def list_workflows():
    """List available workflow templates"""
    try:
        orchestrator = agent_manager.agents.get("orchestrator")
        if not orchestrator:
            raise HTTPException(status_code=404, detail="Orchestrator not found")
        
        workflows = orchestrator._workflow_templates.keys()
        return {
            "workflows": [
                {
                    "name": name,
                    "description": name.replace('_', ' ').title()
                }
                for name in workflows
            ]
        }
    except Exception as e:
        logger.error(f"Error listing workflows: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to list workflows")


@app.post("/workflows/{workflow_name}/execute", response_model=Dict[str, Any])
async def execute_workflow(
    workflow_name: str,
    context: Optional[Dict[str, Any]] = None
):
    """Execute a pre-built workflow"""
    try:
        orchestrator = agent_manager.agents.get("orchestrator")
        if not orchestrator:
            raise HTTPException(status_code=404, detail="Orchestrator not found")
        
        result = await orchestrator.execute_workflow(workflow_name, context or {})
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error executing workflow: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to execute workflow")


@app.post("/workflows/custom", response_model=Dict[str, Any])
async def create_custom_workflow(steps: List[Dict[str, Any]], name: str = "custom_workflow"):
    """Create and execute a custom workflow"""
    try:
        orchestrator = agent_manager.agents.get("orchestrator")
        if not orchestrator:
            raise HTTPException(status_code=404, detail="Orchestrator not found")
        
        # Create custom workflow
        workflow = await orchestrator.create_custom_workflow(steps, name)
        
        # Execute it
        if not orchestrator.workflow_executor:
            from services.tool_composition import WorkflowExecutor
            orchestrator.workflow_executor = WorkflowExecutor(
                tool_executor=orchestrator._execute_tool_via_agent
            )
        
        result = await orchestrator.workflow_executor.execute(workflow)
        return result
    except Exception as e:
        logger.error(f"Error creating custom workflow: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to create custom workflow")


@app.post("/workflows/chain", response_model=Dict[str, Any])
async def create_tool_chain(tools: List[str], name: str = "tool_chain"):
    """Create and execute a simple tool chain"""
    try:
        orchestrator = agent_manager.agents.get("orchestrator")
        if not orchestrator:
            raise HTTPException(status_code=404, detail="Orchestrator not found")
        
        # Create tool chain
        chain = await orchestrator.create_tool_chain(tools, name)
        workflow = chain.get_workflow()
        
        # Execute it
        if not orchestrator.workflow_executor:
            from services.tool_composition import WorkflowExecutor
            orchestrator.workflow_executor = WorkflowExecutor(
                tool_executor=orchestrator._execute_tool_via_agent
            )
        
        result = await orchestrator.workflow_executor.execute(workflow)
        return result
    except Exception as e:
        logger.error(f"Error creating tool chain: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to create tool chain")


# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Handle HTTP exceptions"""
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail},
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Handle general exceptions"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"},
    )


if __name__ == "__main__":
    import uvicorn
    import os
    
    # Change to the project root directory to find mcp.config.yaml
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    os.chdir(project_root)
    
    # Setup logging
    setup_logging(settings.log_level, settings.log_format)
    
    # Run the application
    uvicorn.run(
        "services.api_service:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.api_env == "development",
        log_config=None,  # Use our structured logging
    )
