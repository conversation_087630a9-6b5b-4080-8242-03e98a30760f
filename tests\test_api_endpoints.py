"""
Unit tests for the FastAPI service endpoints
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from datetime import datetime, timezone
from httpx import AsyncClient, ASGITransport
from fastapi.testclient import TestClient

from services.api_service import app
from services.agent_manager import <PERSON><PERSON><PERSON><PERSON>, AgentStatus, AgentResponse


class TestAPIEndpoints:
    """Test suite for API endpoint functionality"""
    
    @pytest.fixture
    def client(self):
        """Create test client with proper app state initialization"""
        # Initialize app state for tests
        from services.agent_manager import get_agent_manager
        app.state.agent_manager = get_agent_manager()
        
        with TestClient(app) as test_client:
            yield test_client
    
    @pytest.fixture
    async def async_client(self):
        """Create async test client"""
        # Initialize app state for tests
        from services.agent_manager import get_agent_manager
        app.state.agent_manager = get_agent_manager()
        
        transport = ASGITransport(app=app)
        async with AsyncClient(transport=transport, base_url="http://test") as client:
            yield client
    
    def test_health_endpoint(self, client):
        """Test health check endpoint"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "version" in data
        assert "agents" in data
        assert isinstance(data["agents"], list)
    
    def test_list_agents_endpoint(self, client):
        """Test list agents endpoint"""
        response = client.get("/agents")
        assert response.status_code == 200
        data = response.json()
        assert "agents" in data
        assert isinstance(data["agents"], list)
        # Should have at least GL, AR, AP agents
        assert len(data["agents"]) >= 3
    
    def test_get_agent_status(self, client):
        """Test get agent status endpoint"""
        # First get list of agents
        agents_response = client.get("/agents")
        agents = agents_response.json()["agents"]
        
        if agents:
            # Test status for first agent
            agent_id = agents[0]["id"]
            response = client.get(f"/agents/{agent_id}/status")
            assert response.status_code == 200
            data = response.json()
            assert data["id"] == agent_id
            assert "status" in data
            assert "avg_response_time" in data
    
    def test_get_nonexistent_agent_status(self, client):
        """Test getting status for non-existent agent"""
        response = client.get("/agents/nonexistent_agent/status")
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_chat_endpoint(self, async_client):
        """Test agent chat endpoint"""
        # Create mock response
        mock_response = AgentResponse(
            agent_id="gl_agent",
            content="Test response",
            correlation_id="test-123",
            status="success",
            timestamp=datetime.now(timezone.utc),
            metadata={"status": "success"}
        )
        
        # Mock the agent manager's send_message method
        with patch.object(app.state.agent_manager, 'send_message', return_value=mock_response):
            response = await async_client.post(
                "/agents/gl_agent/chat",
                json={
                    "message": "What's the GL balance?",
                    "correlation_id": "test-123"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["agent_id"] == "gl_agent"
            assert data["response"] == "Test response"
            assert data["correlation_id"] == "test-123"
    
    @pytest.mark.asyncio
    async def test_chat_endpoint_with_context(self, async_client):
        """Test agent chat endpoint with context"""
        mock_response = AgentResponse(
            agent_id="ar_agent",
            content="Customer info retrieved",
            correlation_id="test-456",
            status="success",
            timestamp=datetime.now(timezone.utc),
            metadata={"customer_count": 5}
        )
        
        with patch.object(app.state.agent_manager, 'send_message', return_value=mock_response):
            response = await async_client.post(
                "/agents/ar_agent/chat",
                json={
                    "message": "Show customer balances",
                    "correlation_id": "test-456",
                    "context": {
                        "user_id": "user123",
                        "session_id": "session456"
                    }
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["metadata"]["customer_count"] == 5
    
    @pytest.mark.asyncio
    async def test_chat_endpoint_error_handling(self, async_client):
        """Test chat endpoint error handling"""
        with patch.object(app.state.agent_manager, 'send_message', side_effect=Exception("Agent error")):
            response = await async_client.post(
                "/agents/gl_agent/chat",
                json={
                    "message": "Test message",
                    "correlation_id": "test-789"
                }
            )
            
            assert response.status_code == 500
            assert "Internal server error" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_reset_agent_endpoint(self, async_client):
        """Test reset agent endpoint"""
        with patch.object(app.state.agent_manager, 'reset_agent', return_value=True):
            response = await async_client.post("/agents/gl_agent/reset")
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "success"
            assert "gl_agent" in data["message"]
    
    def test_get_agent_history(self, client):
        """Test get agent history endpoint"""
        response = client.get("/agents/gl_agent/history")
        assert response.status_code == 200
        data = response.json()
        assert data["agent_id"] == "gl_agent"
        assert "history" in data
        assert isinstance(data["history"], list)
    
    def test_cors_headers(self, client):
        """Test CORS headers are properly set"""
        response = client.options(
            "/agents/gl_agent/chat",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "POST"
            }
        )
        assert response.status_code == 200
        assert response.headers["access-control-allow-origin"] == "http://localhost:3000"
        assert "POST" in response.headers["access-control-allow-methods"]
    
    @pytest.mark.asyncio
    async def test_invalid_request_body(self, async_client):
        """Test invalid request body handling"""
        response = await async_client.post(
            "/agents/gl_agent/chat",
            json={
                # Missing required 'message' field
                "correlation_id": "test-999"
            }
        )
        
        assert response.status_code == 422  # Unprocessable Entity
        error_detail = response.json()
        assert "detail" in error_detail
        assert any("message" in str(err).lower() for err in error_detail["detail"])
    
    def test_openapi_documentation(self, client):
        """Test OpenAPI documentation is available"""
        response = client.get("/openapi.json")
        assert response.status_code == 200
        data = response.json()
        assert data["info"]["title"] == "AI Workspace Agent Service"
        assert "paths" in data
        assert "/agents/{agent_id}/chat" in data["paths"]


class TestAPIPerformance:
    """Test API performance requirements"""
    
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    @pytest.mark.asyncio
    async def test_response_time_requirement(self, client):
        """Test that API responds within 500ms for simple requests"""
        import time
        
        # Test health endpoint
        start_time = time.time()
        response = client.get("/health")
        end_time = time.time()
        
        assert response.status_code == 200
        response_time = (end_time - start_time) * 1000  # Convert to ms
        assert response_time < 500, f"Response time {response_time}ms exceeds 500ms requirement"
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self):
        """Test API can handle multiple concurrent requests"""
        transport = ASGITransport(app=app)
        async with AsyncClient(transport=transport, base_url="http://test") as client:
            # Create 10 concurrent requests
            tasks = []
            for i in range(10):
                task = client.get("/agents")
                tasks.append(task)
            
            # Wait for all requests
            responses = await asyncio.gather(*tasks)
            
            # All should succeed
            for response in responses:
                assert response.status_code == 200


class TestAPIErrorHandling:
    """Test API error handling scenarios"""
    
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    def test_404_for_unknown_route(self, client):
        """Test 404 response for unknown routes"""
        response = client.get("/unknown/route")
        assert response.status_code == 404
    
    def test_method_not_allowed(self, client):
        """Test 405 response for wrong HTTP method"""
        response = client.delete("/agents")  # DELETE not allowed
        assert response.status_code == 405
    
    @pytest.mark.asyncio
    async def test_timeout_handling(self):
        """Test request timeout handling"""
        # This test requires special timeout handling
        # Skipping for now as it's complex to test properly
        pytest.skip("Timeout testing requires special setup")
