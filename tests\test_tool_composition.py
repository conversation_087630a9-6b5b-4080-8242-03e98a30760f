"""
Test suite for Tool Composition Engine
"""

import asyncio
import pytest
from datetime import datetime, timezone
from unittest.mock import Mock, AsyncMock, patch

from services.tool_composition import (
    StepStatus, WorkflowStep, StepResult, ToolWorkflow,
    ToolChain, DataTransformer, data_transformer,
    WorkflowTemplates, WorkflowExecutor
)


class TestWorkflowStep:
    """Test WorkflowStep functionality"""
    
    def test_step_creation(self):
        """Test creating a workflow step"""
        step = WorkflowStep(
            id="test_step",
            tool_name="test_tool",
            parameters={"param1": "value1"},
            depends_on=["step1", "step2"],
            retry_count=5,
            timeout=60.0,
            description="Test step"
        )
        
        assert step.id == "test_step"
        assert step.tool_name == "test_tool"
        assert step.parameters == {"param1": "value1"}
        assert step.depends_on == ["step1", "step2"]
        assert step.retry_count == 5
        assert step.timeout == 60.0
        assert step.description == "Test step"
    
    def test_step_auto_id(self):
        """Test auto-generated step ID"""
        step = WorkflowStep(
            id="",
            tool_name="test_tool"
        )
        assert step.id.startswith("step_")
        assert len(step.id) > 5


class TestStepResult:
    """Test StepResult functionality"""
    
    def test_result_creation(self):
        """Test creating a step result"""
        started = datetime.now(timezone.utc)
        completed = datetime.now(timezone.utc)
        
        result = StepResult(
            step_id="test_step",
            status=StepStatus.COMPLETED,
            result={"data": "test"},
            started_at=started,
            completed_at=completed
        )
        
        assert result.step_id == "test_step"
        assert result.status == StepStatus.COMPLETED
        assert result.result == {"data": "test"}
        assert result.duration is not None
        assert result.duration >= 0


class TestToolWorkflow:
    """Test ToolWorkflow functionality"""
    
    def test_workflow_creation(self):
        """Test creating a workflow"""
        workflow = ToolWorkflow("test_workflow", "Test workflow")
        
        assert workflow.name == "test_workflow"
        assert workflow.description == "Test workflow"
        assert len(workflow.steps) == 0
        assert len(workflow.state) == 0
        assert len(workflow.results) == 0
    
    def test_add_step(self):
        """Test adding steps to workflow"""
        workflow = ToolWorkflow("test")
        
        step1 = WorkflowStep(id="step1", tool_name="tool1")
        step2 = WorkflowStep(id="step2", tool_name="tool2", depends_on=["step1"])
        
        workflow.add_step(step1)
        workflow.add_step(step2)
        
        assert len(workflow.steps) == 2
        assert "step1" in workflow.steps
        assert "step2" in workflow.steps
        assert workflow._execution_order == ["step1", "step2"]
    
    def test_circular_dependency_detection(self):
        """Test circular dependency detection"""
        workflow = ToolWorkflow("test")
        
        step1 = WorkflowStep(id="step1", tool_name="tool1", depends_on=["step2"])
        step2 = WorkflowStep(id="step2", tool_name="tool2", depends_on=["step1"])
        
        workflow.add_step(step1)
        
        with pytest.raises(ValueError, match="Circular dependency"):
            workflow.add_step(step2)
    
    def test_remove_step(self):
        """Test removing steps from workflow"""
        workflow = ToolWorkflow("test")
        
        step1 = WorkflowStep(id="step1", tool_name="tool1")
        step2 = WorkflowStep(id="step2", tool_name="tool2", depends_on=["step1"])
        step3 = WorkflowStep(id="step3", tool_name="tool3", depends_on=["step2"])
        
        workflow.add_step(step1)
        workflow.add_step(step2)
        workflow.add_step(step3)
        
        # Remove step2
        workflow.remove_step("step2")
        
        assert len(workflow.steps) == 2
        assert "step2" not in workflow.steps
        # step3 should no longer depend on removed step2
        assert "step2" not in workflow.steps["step3"].depends_on
    
    def test_get_ready_steps(self):
        """Test getting ready steps"""
        workflow = ToolWorkflow("test")
        
        step1 = WorkflowStep(id="step1", tool_name="tool1")
        step2 = WorkflowStep(id="step2", tool_name="tool2", depends_on=["step1"])
        step3 = WorkflowStep(id="step3", tool_name="tool3")
        
        workflow.add_step(step1)
        workflow.add_step(step2)
        workflow.add_step(step3)
        
        # Initially, step1 and step3 are ready (no dependencies)
        ready = workflow.get_ready_steps()
        assert set(ready) == {"step1", "step3"}
        
        # Mark step1 as completed
        workflow.results["step1"] = StepResult(
            step_id="step1",
            status=StepStatus.COMPLETED
        )
        
        # Now step2 should also be ready
        ready = workflow.get_ready_steps()
        assert set(ready) == {"step2", "step3"}
    
    def test_workflow_state(self):
        """Test workflow state management"""
        workflow = ToolWorkflow("test")
        workflow.state["key1"] = "value1"
        
        step1 = WorkflowStep(id="step1", tool_name="tool1")
        workflow.add_step(step1)
        
        workflow.results["step1"] = StepResult(
            step_id="step1",
            status=StepStatus.COMPLETED,
            result={"data": "test"}
        )
        
        state = workflow.get_state()
        assert state["name"] == "test"
        assert state["state"]["key1"] == "value1"
        assert "step1" in state["results"]
        assert state["results"]["step1"]["status"] == "completed"
    
    def test_workflow_reset(self):
        """Test resetting workflow"""
        workflow = ToolWorkflow("test")
        workflow.state["key1"] = "value1"
        workflow.results["step1"] = StepResult(
            step_id="step1",
            status=StepStatus.COMPLETED
        )
        
        workflow.reset()
        
        assert len(workflow.state) == 0
        assert len(workflow.results) == 0


class TestToolChain:
    """Test ToolChain functionality"""
    
    def test_chain_creation(self):
        """Test creating a tool chain"""
        chain = ToolChain("test_chain")
        
        assert chain.name == "test_chain"
        assert chain.workflow.name == "test_chain"
    
    def test_add_tools_to_chain(self):
        """Test adding tools to chain"""
        chain = ToolChain("test")
        
        chain.add("tool1", {"param": "value1"})
        chain.add("tool2", {"param": "value2"})
        chain.add("tool3", transform=lambda x: x.upper())
        
        workflow = chain.get_workflow()
        assert len(workflow.steps) == 3
        
        # Check sequential dependencies
        steps = list(workflow.steps.values())
        assert steps[0].depends_on == []
        assert steps[1].depends_on == [steps[0].id]
        assert steps[2].depends_on == [steps[1].id]


class TestDataTransformer:
    """Test DataTransformer functionality"""
    
    def test_transformer_registration(self):
        """Test registering transformers"""
        transformer = DataTransformer()
        
        def test_transform(data):
            return data.upper()
        
        transformer.register("string", "upper_string", test_transform)
        
        assert transformer.can_transform("string", "upper_string")
        assert not transformer.can_transform("string", "lower_string")
        
        func = transformer.get_transformer("string", "upper_string")
        assert func("hello") == "HELLO"
    
    def test_transformer_decorator(self):
        """Test transformer decorator"""
        transformer = DataTransformer()
        
        @transformer.transform("list", "count")
        def count_items(items):
            return len(items)
        
        assert transformer.can_transform("list", "count")
        func = transformer.get_transformer("list", "count")
        assert func([1, 2, 3]) == 3
    
    def test_global_transformers(self):
        """Test pre-registered global transformers"""
        # Test customer ID extraction
        customers = [
            {"id": "123"},
            {"customerId": "456"},
            {"name": "Test"}
        ]
        
        ids = data_transformer.get_transformer("customer_list", "customer_ids")(customers)
        assert ids == ["123", "456", ""]
        
        # Test financial summary
        financial_data = {
            "revenue_items": [{"amount": 100}, {"amount": 200}],
            "expense_items": [{"amount": 50}, {"amount": 75}],
            "net_income": 175,
            "period": "2024-01"
        }
        
        summary = data_transformer.get_transformer("financial_data", "summary_report")(financial_data)
        assert summary["total_revenue"] == 300
        assert summary["total_expenses"] == 125
        assert summary["net_income"] == 175


class TestWorkflowTemplates:
    """Test WorkflowTemplates"""
    
    def test_month_end_close_template(self):
        """Test month-end close workflow template"""
        workflow = WorkflowTemplates.month_end_close()
        
        assert workflow.name == "month_end_close"
        assert len(workflow.steps) == 6
        
        # Check step order
        assert "validate_period" in workflow.steps
        assert "gl_reconciliation" in workflow.steps
        assert "execute_close" in workflow.steps
        
        # Check dependencies
        execute_step = workflow.steps["execute_close"]
        assert "consolidated_report" in execute_step.depends_on
        
        # Check condition on execute_close
        assert execute_step.condition is not None
    
    def test_customer_onboarding_template(self):
        """Test customer onboarding template"""
        workflow = WorkflowTemplates.customer_onboarding("Acme Corp")
        
        assert workflow.name == "customer_onboarding"
        assert "Acme Corp" in workflow.description
        assert len(workflow.steps) == 4
        
        # Check conditional step
        create_step = workflow.steps["create_customer"]
        assert create_step.condition is not None
    
    def test_financial_reporting_template(self):
        """Test financial reporting template"""
        workflow = WorkflowTemplates.financial_reporting("2024-01")
        
        assert workflow.name == "financial_reporting"
        assert "2024-01" in workflow.description
        
        # Check parallel data gathering
        gl_step = workflow.steps["gl_data"]
        ar_step = workflow.steps["ar_data"]
        ap_step = workflow.steps["ap_data"]
        
        # These should have no dependencies (can run in parallel)
        assert len(gl_step.depends_on) == 0
        assert len(ar_step.depends_on) == 0
        assert len(ap_step.depends_on) == 0
        
        # Executive summary should depend on all reports
        exec_step = workflow.steps["executive_summary"]
        assert "pl_statement" in exec_step.depends_on
        assert "balance_sheet" in exec_step.depends_on
        assert "cash_flow" in exec_step.depends_on


class TestWorkflowExecutor:
    """Test WorkflowExecutor functionality"""
    
    @pytest.mark.asyncio
    async def test_simple_workflow_execution(self):
        """Test executing a simple workflow"""
        executor = WorkflowExecutor()
        
        workflow = ToolWorkflow("test")
        step1 = WorkflowStep(id="step1", tool_name="tool1")
        step2 = WorkflowStep(id="step2", tool_name="tool2", depends_on=["step1"])
        
        workflow.add_step(step1)
        workflow.add_step(step2)
        
        result = await executor.execute(workflow)
        
        assert result["status"] == "completed"
        assert len(result["final_results"]) == 2
        assert "step1" in result["final_results"]
        assert "step2" in result["final_results"]
    
    @pytest.mark.asyncio
    async def test_workflow_with_condition(self):
        """Test workflow with conditional step"""
        executor = WorkflowExecutor()
        
        workflow = ToolWorkflow("test")
        step1 = WorkflowStep(id="step1", tool_name="tool1")
        step2 = WorkflowStep(
            id="step2",
            tool_name="tool2",
            depends_on=["step1"],
            condition=lambda state: False  # Always skip
        )
        
        workflow.add_step(step1)
        workflow.add_step(step2)
        
        result = await executor.execute(workflow)
        
        assert result["status"] == "completed"
        assert workflow.results["step2"].status == StepStatus.SKIPPED
    
    @pytest.mark.asyncio
    async def test_workflow_with_transformation(self):
        """Test workflow with step transformation"""
        executor = WorkflowExecutor()
        
        workflow = ToolWorkflow("test")
        step1 = WorkflowStep(
            id="step1",
            tool_name="tool1",
            transform=lambda x: {"transformed": True, "original": x}
        )
        
        workflow.add_step(step1)
        
        result = await executor.execute(workflow)
        
        assert result["status"] == "completed"
        step_result = result["final_results"]["step1"]
        assert "transformed" in step_result
        assert step_result["transformed"] is True
    
    @pytest.mark.asyncio
    async def test_parameter_resolution(self):
        """Test parameter resolution from workflow state"""
        executor = WorkflowExecutor()
        
        workflow = ToolWorkflow("test")
        
        # Step 1 produces a result
        step1 = WorkflowStep(id="step1", tool_name="tool1")
        
        # Step 2 uses result from step 1
        step2 = WorkflowStep(
            id="step2",
            tool_name="tool2",
            parameters={
                "input": "{{step1_result}}",
                "static": "value"
            },
            depends_on=["step1"]
        )
        
        workflow.add_step(step1)
        workflow.add_step(step2)
        
        # Mock the tool executor to capture parameters
        captured_params = {}
        
        async def mock_execute_tool(tool_name, params, timeout):
            captured_params[tool_name] = params
            return {"mocked": True}
        
        executor._execute_tool = mock_execute_tool
        
        result = await executor.execute(workflow)
        
        assert result["status"] == "completed"
        # Check that step2 received the resolved parameter
        assert "tool2" in captured_params
        assert captured_params["tool2"]["static"] == "value"
    
    @pytest.mark.asyncio
    async def test_workflow_with_error_handling(self):
        """Test workflow error handling"""
        executor = WorkflowExecutor()
        
        workflow = ToolWorkflow("test")
        
        # Create a step that will fail
        async def failing_tool(name, params, timeout):
            raise Exception("Tool failed")
        
        executor._execute_tool = failing_tool
        
        # Step with error handler
        def handle_error(error, state):
            return {"error_handled": True, "message": str(error)}
        
        step = WorkflowStep(
            id="step1",
            tool_name="tool1",
            retry_count=1,  # Don't retry too much
            error_handler=handle_error
        )
        
        workflow.add_step(step)
        
        result = await executor.execute(workflow)
        
        assert result["status"] == "completed"
        assert result["final_results"]["step1"]["error_handled"] is True
    
    @pytest.mark.asyncio
    async def test_checkpoint_and_resume(self):
        """Test workflow checkpoint and resume"""
        executor = WorkflowExecutor()
        
        workflow = ToolWorkflow("test")
        step1 = WorkflowStep(id="step1", tool_name="tool1")
        step2 = WorkflowStep(id="step2", tool_name="tool2", depends_on=["step1"])
        step3 = WorkflowStep(id="step3", tool_name="tool3", depends_on=["step2"])
        
        workflow.add_step(step1)
        workflow.add_step(step2)
        workflow.add_step(step3)
        
        # Execute with checkpoint
        checkpoint_id = "test_checkpoint"
        
        # First execution - simulate failure after step1
        call_count = 0
        
        async def mock_tool_with_failure(name, params, timeout):
            nonlocal call_count
            call_count += 1
            if call_count == 2:  # Fail on step2
                raise Exception("Simulated failure")
            return {"step": name}
        
        executor._execute_tool = mock_tool_with_failure
        
        # This should fail
        with pytest.raises(Exception):
            await executor.execute(workflow, checkpoint_id=checkpoint_id)
        
        # Check checkpoint was saved
        checkpoints = executor.list_checkpoints()
        assert len(checkpoints) == 1
        assert checkpoints[0]["id"] == checkpoint_id
        
        # Reset workflow and resume from checkpoint
        workflow.reset()
        
        # Fix the tool executor
        async def mock_tool_success(name, params, timeout):
            return {"step": name}
        
        executor._execute_tool = mock_tool_success
        
        # Resume from checkpoint
        result = await executor.execute(workflow, checkpoint_id=checkpoint_id)
        
        assert result["status"] == "completed"
        assert len(result["final_results"]) == 3
        
        # Checkpoint should be cleaned up
        assert len(executor.list_checkpoints()) == 0
    
    @pytest.mark.asyncio
    async def test_parallel_step_execution(self):
        """Test parallel execution of independent steps"""
        executor = WorkflowExecutor()
        
        workflow = ToolWorkflow("test")
        
        # Three independent steps that can run in parallel
        step1 = WorkflowStep(id="step1", tool_name="tool1")
        step2 = WorkflowStep(id="step2", tool_name="tool2")
        step3 = WorkflowStep(id="step3", tool_name="tool3")
        
        # Step 4 depends on all three
        step4 = WorkflowStep(
            id="step4",
            tool_name="tool4",
            depends_on=["step1", "step2", "step3"]
        )
        
        workflow.add_step(step1)
        workflow.add_step(step2)
        workflow.add_step(step3)
        workflow.add_step(step4)
        
        # Track execution order
        execution_order = []
        
        async def mock_tool_tracking(name, params, timeout):
            execution_order.append(name)
            await asyncio.sleep(0.01)  # Small delay
            return {"tool": name}
        
        executor._execute_tool = mock_tool_tracking
        
        result = await executor.execute(workflow)
        
        assert result["status"] == "completed"
        assert len(result["final_results"]) == 4
        
        # First three tools should have executed before tool4
        tool4_index = execution_order.index("tool4")
        assert "tool1" in execution_order[:tool4_index]
        assert "tool2" in execution_order[:tool4_index]
        assert "tool3" in execution_order[:tool4_index]


# Integration test
class TestToolCompositionIntegration:
    """Integration tests for tool composition"""
    
    @pytest.mark.asyncio
    async def test_financial_workflow_integration(self):
        """Test a complete financial workflow"""
        # Create a mock tool executor
        mock_executor = Mock()
        
        async def mock_execute(tool_name, params, timeout):
            # Simulate different tool responses
            if "financial_summary" in tool_name:
                return {
                    "total_revenue": 100000,
                    "total_expenses": 75000,
                    "net_income": 25000
                }
            elif "consolidated_report" in tool_name:
                return {
                    "report_type": params.get("report_type"),
                    "generated": True
                }
            return {"success": True}
        
        # Create workflow executor with mock
        executor = WorkflowExecutor()
        executor._execute_tool = mock_execute
        
        # Use financial reporting template
        workflow = WorkflowTemplates.financial_reporting("2024-01")
        
        # Execute workflow
        result = await executor.execute(workflow, context={"user": "test"})
        
        assert result["status"] == "completed"
        
        # Verify all steps completed
        workflow_state = result["workflow"]
        for step_id in workflow.steps:
            assert step_id in workflow_state["results"]
            assert workflow_state["results"][step_id]["status"] == "completed"
        
        # Verify executive summary was generated last
        exec_result = result["final_results"]["executive_summary"]
        assert exec_result["report_type"] == "executive_summary"