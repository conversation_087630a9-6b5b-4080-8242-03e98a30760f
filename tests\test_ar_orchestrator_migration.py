"""
Test AR functionality through orchestrator after migration
"""

import asyncio
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from agents.orchestrator import Orchestrator
from services.mcp_registry import MCPRegistry
from services.tool_discovery import ToolDiscoveryService


async def test_ar_operations():
    """Test various AR operations through the orchestrator"""
    
    # Initialize services
    registry = MCPRegistry()
    discovery = ToolDiscoveryService(registry)
    orchestrator = Orchestrator(registry, discovery)
    
    # Test scenarios
    test_scenarios = [
        {
            "name": "Customer Balance Inquiry",
            "query": "What is the current balance for customer ABC Company?",
            "expected_intent": "ar_customer_inquiry"
        },
        {
            "name": "AR Aging Report",
            "query": "Show me the AR aging report for all customers",
            "expected_intent": "ar_aging"
        },
        {
            "name": "Collections Workflow",
            "query": "Which customers are overdue by more than 60 days?",
            "expected_intent": "ar_collections"
        },
        {
            "name": "Customer Credit Management",
            "query": "Check the credit limit and payment terms for customer XYZ Corp",
            "expected_intent": "ar_credit_management"
        },
        {
            "name": "Invoice Creation (Unavailable)",
            "query": "Create a new invoice for customer ABC Company for $5,000",
            "expected_intent": "ar_invoice_creation"
        },
        {
            "name": "Payment Application (Unavailable)",
            "query": "Apply payment of $3,000 from customer DEF Inc to invoice INV-001",
            "expected_intent": "ar_payment_application"
        }
    ]
    
    print("=== AR Orchestrator Migration Test ===\n")
    
    for scenario in test_scenarios:
        print(f"\nTest: {scenario['name']}")
        print(f"Query: {scenario['query']}")
        
        try:
            # Detect intent
            intent_result = await orchestrator.detect_intent(scenario['query'])
            print(f"Detected Intent: {intent_result['intent']} (confidence: {intent_result['confidence']:.2f})")
            
            # Select tools
            tools = await orchestrator.select_tools_for_intent(intent_result['intent'], scenario['query'])
            print(f"Selected Tools: {[t['name'] for t in tools]}")
            
            # Check tool availability
            available_tools = []
            unavailable_tools = []
            
            for tool in tools:
                tool_name = tool['name']
                if tool_name.startswith('sage-intacct:'):
                    # Check if this is an AR tool
                    if any(ar_keyword in tool_name for ar_keyword in ['contact', 'sales_invoice', 'customer', 'unallocated']):
                        if 'write access' in tool.get('description', ''):
                            unavailable_tools.append(tool_name)
                        else:
                            available_tools.append(tool_name)
            
            print(f"Available: {available_tools}")
            print(f"Unavailable: {unavailable_tools}")
            
            # Execute if high confidence and tools available
            if intent_result['confidence'] >= 0.8 and available_tools:
                print("Status: ✓ Ready for execution through orchestrator")
            elif unavailable_tools and not available_tools:
                print("Status: ⚠ All required tools need write access (currently unavailable)")
            else:
                print(f"Status: ℹ Low confidence ({intent_result['confidence']:.2f}) - would ask for clarification")
                
        except Exception as e:
            print(f"Error: {str(e)}")
    
    print("\n=== AR Tool Availability Summary ===")
    
    # Check AR tool mappings
    ar_tools = {
        "intacct-ar.customer_query": "get_contacts",
        "intacct-ar.invoice_create": "N/A - requires write access",
        "intacct-ar.payment_apply": "N/A - requires write access", 
        "intacct-ar.aging_report": "get_sales_invoices",
        "intacct-ar.collections_workflow": "get_unallocated_artefacts"
    }
    
    print("\nAR Tool Mappings:")
    for agent_tool, mcp_tool in ar_tools.items():
        status = "✓" if "N/A" not in mcp_tool else "✗"
        print(f"{status} {agent_tool} -> {mcp_tool}")
    
    print("\n=== Test Summary ===")
    print("✓ AR read operations (customer query, aging, collections) available")
    print("✗ AR write operations (invoice creation, payment application) require MCP server update")
    print("✓ Orchestrator correctly routes AR queries")
    print("✓ Tool selection works for available AR operations")


if __name__ == "__main__":
    asyncio.run(test_ar_operations())