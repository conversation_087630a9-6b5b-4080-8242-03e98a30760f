"""
Confidence Scoring Service for Dynamic Orchestrator

This service implements multi-factor confidence scoring to evaluate
the orchestrator's understanding of user intents and make intelligent
decisions about tool selection and clarification needs.
"""
import re
import logging
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime, timezone
import json
from pathlib import Path

logger = logging.getLogger(__name__)


class ConfidenceScorer:
    """
    Multi-factor confidence scoring system for intent detection
    """
    
    def __init__(self, feedback_file: Optional[str] = None):
        """
        Initialize the confidence scorer
        
        Args:
            feedback_file: Path to store feedback data for learning
        """
        self.feedback_file = feedback_file or ".confidence_feedback.json"
        self.feedback_data = self._load_feedback()
        
        # Keyword mappings for different intents
        self.keyword_mappings = {
            'gl_balance': {
                'primary': ['balance', 'how much', 'gl balance'],
                'secondary': ['account', 'amount', 'total', 'general ledger'],
                'weight': 1.0
            },
            'journal_entry': {
                'primary': ['journal entry', 'je', 'journal', 'debit credit'],
                'secondary': ['debit', 'credit', 'posting', 'entry'],
                'weight': 1.0
            },
            'financial_statements': {
                'primary': ['financial statement', 'p&l', 'balance sheet', 'income statement', 'cash flow'],
                'secondary': ['statement', 'profit loss', 'trial balance'],
                'weight': 1.0
            },
            'customer_balance': {
                'primary': ['customer balance', 'ar balance', 'receivable balance'],
                'secondary': ['customer', 'ar', 'receivable', 'owe', 'owes'],
                'weight': 1.0
            },
            'create_invoice': {
                'primary': ['create invoice', 'new invoice', 'issue invoice'],
                'secondary': ['invoice', 'bill customer', 'charge'],
                'weight': 1.0
            },
            'vendor_balance': {
                'primary': ['vendor balance', 'ap balance', 'payable balance'],
                'secondary': ['vendor', 'ap', 'payable', 'owe vendor'],
                'weight': 1.0
            },
            'payment_batch': {
                'primary': ['payment batch', 'payment run', 'pay vendors', 'batch payment'],
                'secondary': ['payment', 'pay', 'remit', 'vendor payment'],
                'weight': 0.9
            },
            'variance_analysis': {
                'primary': ['variance analysis', 'budget variance', 'actual vs budget'],
                'secondary': ['variance', 'budget', 'actual', 'comparison', 'difference'],
                'weight': 1.0
            },
            'month_end_close': {
                'primary': ['month-end', 'month end', 'close books', 'period close'],
                'secondary': ['close', 'closing', 'reconcile', 'finalize'],
                'weight': 0.95
            }
        }
        
        # Tool parameter requirements
        self.tool_requirements = {
            'get_balance': {
                'required': ['account_number'],
                'optional': ['as_of_date', 'include_details']
            },
            'create_invoice': {
                'required': ['customer_id', 'line_items'],
                'optional': ['invoice_date', 'due_date', 'terms']
            },
            'run_report': {
                'required': ['report_type', 'period'],
                'optional': ['format', 'filters', 'grouping']
            },
            'search_across_modules': {
                'required': ['query'],
                'optional': ['modules', 'limit']
            },
            'get_financial_summary': {
                'required': [],
                'optional': ['start_date', 'end_date', 'include_modules']
            },
            'execute_month_end_close': {
                'required': ['period'],
                'optional': ['modules', 'dry_run']
            }
        }
        
        # Initialize historical success rates
        self._calculate_success_rates()
    
    def calculate_confidence(self, 
                           intent: str, 
                           message: str,
                           context: Dict[str, Any],
                           detected_intents: List[Dict[str, Any]]) -> Tuple[float, Dict[str, Any]]:
        """
        Calculate multi-factor confidence score
        
        Args:
            intent: Detected primary intent
            message: User's original message
            context: Context information
            detected_intents: All detected intents with initial scores
            
        Returns:
            Tuple of (confidence_score, scoring_details)
        """
        message_lower = message.lower()
        
        # Calculate component scores
        keyword_score = self._calculate_keyword_score(intent, message_lower)
        context_score = self._calculate_context_score(message, context)
        parameter_score = self._calculate_parameter_score(intent, message, context)
        specificity_score = self._calculate_specificity_score(message)
        
        # Apply historical success rate if available
        historical_boost = self._get_historical_boost(intent)
        
        # Calculate base confidence
        base_confidence = (
            keyword_score * 0.4 +
            context_score * 0.3 +
            parameter_score * 0.2 +
            specificity_score * 0.1
        )
        
        # Apply historical boost (up to 10% additional)
        confidence = base_confidence + (historical_boost * 0.1)
        
        # Apply ambiguity penalties
        ambiguity_penalty = self._calculate_ambiguity_penalty(message, detected_intents)
        confidence = max(0.0, confidence - ambiguity_penalty)
        
        # Cap at 1.0
        confidence = min(1.0, confidence)
        
        # Compile scoring details
        scoring_details = {
            'keyword_score': keyword_score,
            'context_score': context_score,
            'parameter_score': parameter_score,
            'specificity_score': specificity_score,
            'historical_boost': historical_boost,
            'ambiguity_penalty': ambiguity_penalty,
            'base_confidence': base_confidence,
            'final_confidence': confidence,
            'components': {
                'keyword_contribution': keyword_score * 0.4,
                'context_contribution': context_score * 0.3,
                'parameter_contribution': parameter_score * 0.2,
                'specificity_contribution': specificity_score * 0.1
            }
        }
        
        return confidence, scoring_details
    
    def _calculate_keyword_score(self, intent: str, message: str) -> float:
        """Calculate keyword matching score"""
        if intent not in self.keyword_mappings:
            return 0.5  # Default for unknown intents
        
        mapping = self.keyword_mappings[intent]
        score = 0.0
        
        # Check primary keywords (full score)
        primary_matches = sum(1 for kw in mapping['primary'] if kw in message)
        if primary_matches > 0:
            score = 1.0
        else:
            # Check secondary keywords (partial score)
            secondary_matches = sum(1 for kw in mapping['secondary'] if kw in message)
            if secondary_matches > 0:
                score = 0.8 * (secondary_matches / len(mapping['secondary']))
        
        # Apply weight
        return score * mapping.get('weight', 1.0)
    
    def _calculate_context_score(self, message: str, context: Dict[str, Any]) -> float:
        """Calculate context alignment score"""
        score = 0.0
        
        # Entity identification (30%)
        entity_patterns = [
            r'\b[A-Z][a-zA-Z]+(?: [A-Z][a-zA-Z]+)*\b',  # Proper names
            r'\b\d{4,}\b',  # Account numbers
            r'customer\s+(\w+)',  # Customer references
            r'vendor\s+(\w+)',  # Vendor references
        ]
        
        for pattern in entity_patterns:
            if re.search(pattern, message):
                score += 0.3
                break
        
        # Time period identification (20%)
        time_patterns = [
            r'\b(today|yesterday|current|this month|last month)\b',
            r'\b\d{4}-\d{2}-\d{2}\b',  # ISO date
            r'\b(Q[1-4]|quarter)\b',  # Quarters
            r'\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*\b',  # Months
        ]
        
        for pattern in time_patterns:
            if re.search(pattern, message, re.IGNORECASE):
                score += 0.2
                break
        
        # Amount specification (20%)
        amount_patterns = [
            r'\$[\d,]+\.?\d*',  # Dollar amounts
            r'\b\d+(?:,\d{3})*(?:\.\d+)?\b',  # Numbers with commas
        ]
        
        for pattern in amount_patterns:
            if re.search(pattern, message):
                score += 0.2
                break
        
        # Purpose clarity (30%)
        purpose_keywords = [
            'to', 'for', 'because', 'in order to', 'need to',
            'want to', 'looking for', 'trying to', 'help with'
        ]
        
        purpose_score = sum(0.1 for kw in purpose_keywords if kw in message.lower())
        score += min(0.3, purpose_score)
        
        # Boost for conversation context
        if context.get('previous_intent'):
            score += 0.1
        
        return min(1.0, score)
    
    def _calculate_parameter_score(self, intent: str, message: str, context: Dict[str, Any]) -> float:
        """Calculate parameter completeness score"""
        # Map intent to likely tool
        intent_to_tool = {
            'gl_balance': 'get_balance',
            'create_invoice': 'create_invoice',
            'financial_statements': 'run_report',
            'month_end_close': 'execute_month_end_close',
            'variance_analysis': 'get_financial_summary'
        }
        
        tool = intent_to_tool.get(intent)
        if not tool or tool not in self.tool_requirements:
            return 0.7  # Default for unknown tools
        
        requirements = self.tool_requirements[tool]
        
        # Extract potential parameters from message
        extracted_params = self._extract_parameters(message)
        
        # Calculate required parameters score (70%)
        required_score = 0.0
        if requirements['required']:
            required_found = sum(1 for param in requirements['required'] 
                               if param in extracted_params or param in context)
            required_score = required_found / len(requirements['required'])
        else:
            required_score = 1.0  # No required params means full score
        
        # Calculate optional parameters score (30%)
        optional_score = 0.0
        if requirements['optional']:
            optional_found = sum(1 for param in requirements['optional']
                               if param in extracted_params or param in context)
            optional_score = optional_found / len(requirements['optional'])
        else:
            optional_score = 1.0  # No optional params means full score
        
        return (required_score * 0.7) + (optional_score * 0.3)
    
    def _extract_parameters(self, message: str) -> Dict[str, Any]:
        """Extract potential parameters from message"""
        params = {}
        
        # Account numbers
        account_match = re.search(r'\b(\d{4,})\b', message)
        if account_match:
            params['account_number'] = account_match.group(1)
        
        # Dates
        date_match = re.search(r'\b(\d{4}-\d{2}-\d{2})\b', message)
        if date_match:
            params['date'] = date_match.group(1)
            params['as_of_date'] = date_match.group(1)
            params['start_date'] = date_match.group(1)
        
        # Report types
        if 'p&l' in message.lower() or 'profit' in message.lower():
            params['report_type'] = 'profit_loss'
        elif 'balance sheet' in message.lower():
            params['report_type'] = 'balance_sheet'
        
        # Periods
        if 'this month' in message.lower():
            params['period'] = 'current_month'
        elif 'last month' in message.lower():
            params['period'] = 'last_month'
        
        # Query terms
        if 'search' in message.lower() or 'find' in message.lower():
            # Extract search query
            query_match = re.search(r'(?:search|find|look for)\s+(.+?)(?:\s+in|\s+from|$)', message, re.IGNORECASE)
            if query_match:
                params['query'] = query_match.group(1)
        
        return params
    
    def _calculate_specificity_score(self, message: str) -> float:
        """Calculate request specificity score"""
        # Length factor
        word_count = len(message.split())
        
        if word_count < 3:
            length_score = 0.0  # Too vague
        elif word_count < 5:
            length_score = 0.4  # General
        elif word_count < 15:
            length_score = 0.7  # Specific
        else:
            length_score = 1.0  # Very specific
        
        # Detail indicators
        detail_keywords = [
            'specifically', 'exactly', 'particular', 'certain',
            'following', 'these', 'those', 'listed', 'mentioned'
        ]
        detail_boost = sum(0.1 for kw in detail_keywords if kw in message.lower())
        
        # Combine scores
        return min(1.0, length_score + detail_boost)
    
    def _calculate_ambiguity_penalty(self, message: str, detected_intents: List[Dict[str, Any]]) -> float:
        """Calculate ambiguity penalties"""
        penalty = 0.0
        
        # Conflicting keywords penalty
        conflicting_pairs = [
            ('customer', 'vendor'),
            ('receivable', 'payable'),
            ('debit', 'payment'),
            ('invoice', 'bill')  # Could be AR or AP
        ]
        
        message_lower = message.lower()
        for word1, word2 in conflicting_pairs:
            if word1 in message_lower and word2 in message_lower:
                penalty += 0.15
                break
        
        # Multiple high-confidence intents penalty
        high_confidence_intents = [i for i in detected_intents if i.get('confidence', 0) > 0.7]
        if len(high_confidence_intents) > 1:
            penalty += 0.10
        
        # Missing context penalty
        if not any(word in message_lower for word in ['please', 'want', 'need', 'show', 'get', 'create']):
            penalty += 0.05  # No clear action verb
        
        # Grammar issues (very basic check)
        if message.count('?') > 1 or message.count('...') > 0:
            penalty += 0.05
        
        return penalty
    
    def _get_historical_boost(self, intent: str) -> float:
        """Get historical success rate boost for an intent"""
        if intent in self.feedback_data.get('success_rates', {}):
            success_rate = self.feedback_data['success_rates'][intent]
            # Convert success rate to boost (0-1 scale)
            return success_rate.get('rate', 0.5)
        return 0.5  # Default neutral boost
    
    def _calculate_success_rates(self) -> None:
        """Calculate success rates from feedback data"""
        if 'interactions' not in self.feedback_data:
            return
        
        success_rates = {}
        
        for interaction in self.feedback_data['interactions']:
            intent = interaction.get('intent')
            if not intent:
                continue
                
            if intent not in success_rates:
                success_rates[intent] = {'correct': 0, 'total': 0}
            
            success_rates[intent]['total'] += 1
            if interaction.get('user_feedback') == 'correct':
                success_rates[intent]['correct'] += 1
        
        # Calculate rates
        for intent, stats in success_rates.items():
            if stats['total'] > 0:
                stats['rate'] = stats['correct'] / stats['total']
            else:
                stats['rate'] = 0.5
        
        self.feedback_data['success_rates'] = success_rates
    
    def record_feedback(self, 
                       interaction_id: str,
                       user_input: str,
                       intent: str,
                       confidence_score: float,
                       action_taken: str,
                       user_feedback: str,
                       additional_data: Optional[Dict[str, Any]] = None) -> None:
        """
        Record user feedback for continuous improvement
        
        Args:
            interaction_id: Unique ID for the interaction
            user_input: Original user request
            intent: Detected intent
            confidence_score: Calculated confidence
            action_taken: Action taken (execute, confirm, clarify)
            user_feedback: User feedback (correct, incorrect, partial)
            additional_data: Any additional data to store
        """
        if 'interactions' not in self.feedback_data:
            self.feedback_data['interactions'] = []
        
        feedback_entry = {
            'interaction_id': interaction_id,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'user_input': user_input,
            'intent': intent,
            'confidence_score': confidence_score,
            'action_taken': action_taken,
            'user_feedback': user_feedback
        }
        
        if additional_data:
            feedback_entry['additional_data'] = additional_data
        
        self.feedback_data['interactions'].append(feedback_entry)
        
        # Recalculate success rates
        self._calculate_success_rates()
        
        # Save feedback
        self._save_feedback()
        
        logger.info(f"Recorded feedback for interaction {interaction_id}: "
                   f"intent={intent}, confidence={confidence_score:.2f}, "
                   f"feedback={user_feedback}")
    
    def _load_feedback(self) -> Dict[str, Any]:
        """Load feedback data from file"""
        feedback_path = Path(self.feedback_file)
        if feedback_path.exists():
            try:
                with open(feedback_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Failed to load feedback data: {e}")
        
        return {'interactions': [], 'success_rates': {}}
    
    def _save_feedback(self) -> None:
        """Save feedback data to file"""
        try:
            feedback_path = Path(self.feedback_file)
            with open(feedback_path, 'w') as f:
                json.dump(self.feedback_data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save feedback data: {e}")
    
    def get_confidence_action(self, confidence: float) -> Dict[str, Any]:
        """
        Determine action based on confidence level
        
        Args:
            confidence: Confidence score (0-1)
            
        Returns:
            Dict with action and reasoning
        """
        if confidence >= 0.8:
            return {
                'action': 'execute',
                'reasoning': 'Clear intent with all required information',
                'response_template': "I'll {specific_action} right away..."
            }
        elif confidence >= 0.6:
            return {
                'action': 'confirm_and_execute',
                'reasoning': 'Likely intent but should confirm details',
                'response_template': "I understand you want to {interpreted_action}. "
                                   "Let me {specific_steps}. Is this correct?"
            }
        else:
            return {
                'action': 'clarify',
                'reasoning': 'Unclear intent or missing critical information',
                'response_template': "I'd be happy to help with {general_topic}. "
                                   "Could you tell me more about {specific_questions}?"
            }
    
    def generate_clarification_prompt(self, 
                                    message: str,
                                    detected_intents: List[Dict[str, Any]],
                                    confidence: float) -> str:
        """
        Generate appropriate clarification prompt based on confidence level
        
        Args:
            message: Original user message
            detected_intents: List of detected intents with scores
            confidence: Overall confidence score
            
        Returns:
            Clarification prompt string
        """
        if confidence >= 0.6:
            # Medium confidence - confirm interpretation
            primary_intent = detected_intents[0] if detected_intents else {'intent': 'general_query'}
            intent_descriptions = {
                'gl_balance': 'check general ledger account balances',
                'journal_entry': 'create or manage journal entries',
                'financial_statements': 'generate financial statements',
                'customer_balance': 'check customer account balances',
                'create_invoice': 'create a new sales invoice',
                'vendor_balance': 'check vendor account balances',
                'payment_batch': 'process vendor payments',
                'variance_analysis': 'analyze budget vs actual variances',
                'month_end_close': 'perform month-end close procedures'
            }
            
            action = intent_descriptions.get(primary_intent['intent'], 'process your request')
            
            prompt = f"I understand you want to {action}. "
            
            # Add specific confirmations based on extracted parameters
            params = self._extract_parameters(message)
            if params:
                prompt += "I found these details:\n"
                for key, value in params.items():
                    prompt += f"- {key.replace('_', ' ').title()}: {value}\n"
                prompt += "\n"
            
            prompt += "Is this correct? Let me know if you'd like me to proceed or if you need to adjust anything."
            
        else:
            # Low confidence - ask for clarification
            prompt = "I'd be happy to help with your request. "
            
            if detected_intents:
                prompt += "Based on what you've said, I think you might be looking for:\n\n"
                
                for i, intent in enumerate(detected_intents[:3]):
                    intent_descriptions = {
                        'gl_balance': 'Check general ledger account balances',
                        'journal_entry': 'Create or manage journal entries',
                        'financial_statements': 'Generate financial statements (P&L, Balance Sheet, etc.)',
                        'customer_balance': 'Check customer account balances or AR aging',
                        'create_invoice': 'Create a new sales invoice',
                        'vendor_balance': 'Check vendor account balances or AP aging',
                        'payment_batch': 'Process vendor payments or create payment batch',
                        'variance_analysis': 'Analyze budget vs actual variances',
                        'month_end_close': 'Perform month-end close procedures'
                    }
                    desc = intent_descriptions.get(intent['intent'], intent['intent'].replace('_', ' ').title())
                    prompt += f"{i+1}. {desc}\n"
                
                prompt += "\nWhich of these would you like to do? "
            else:
                prompt += "Could you provide more details about what you're trying to do? "
                
            prompt += "Feel free to be specific about:\n"
            prompt += "- Which accounts, customers, or vendors you're working with\n"
            prompt += "- What time period you're interested in\n"
            prompt += "- What specific action you want to take"
        
        return prompt


# Example usage and testing
if __name__ == "__main__":
    scorer = ConfidenceScorer()
    
    # Test cases
    test_cases = [
        {
            'message': "Show me the balance for GL account 1000 as of today",
            'intent': 'gl_balance',
            'context': {}
        },
        {
            'message': "Process vendor payments due this week",
            'intent': 'payment_batch',
            'context': {}
        },
        {
            'message': "Fix the accounts",
            'intent': 'general_query',
            'context': {}
        },
        {
            'message': "Create journal entry debiting account 5100 for $1,000 and crediting account 2100 for $1,000 with description 'Monthly rent'",
            'intent': 'journal_entry',
            'context': {}
        }
    ]
    
    for test in test_cases:
        confidence, details = scorer.calculate_confidence(
            test['intent'],
            test['message'],
            test['context'],
            [{'intent': test['intent'], 'confidence': 0.5}]
        )
        
        action = scorer.get_confidence_action(confidence)
        
        print(f"\nMessage: {test['message']}")
        print(f"Intent: {test['intent']}")
        print(f"Confidence: {confidence:.2f}")
        print(f"Action: {action['action']}")
        print(f"Scoring Details: {json.dumps(details, indent=2)}")
        
        if action['action'] in ['confirm_and_execute', 'clarify']:
            clarification = scorer.generate_clarification_prompt(
                test['message'],
                [{'intent': test['intent'], 'confidence': confidence}],
                confidence
            )
            print(f"Clarification: {clarification}")