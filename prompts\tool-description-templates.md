# Tool Description Templates

## Overview
This document provides standardized templates for describing tools to the orchestrator LLM. Consistent tool descriptions ensure accurate tool selection and parameter usage.

---

## Standard Tool Template

```yaml
tool_name: [MCP_Server].[Tool_Name]
category: [GL|AR|AP|Analysis|Report|Validation|Utility]
description: [Clear, concise description of what the tool does]
when_to_use:
  - [Specific scenario 1]
  - [Specific scenario 2]
  - [Specific scenario 3]
parameters:
  - name: [param_name]
    type: [string|number|date|boolean|object|array]
    required: [true|false]
    description: [What this parameter controls]
    example: [Example value]
returns: 
  type: [object|array|string|number]
  description: [Description of what the tool returns]
example_usage: |
  User: "[Example user request]"
  Tool Call: [tool_name]({ param1: value1, param2: value2 })
  Response: [Example response structure]
errors:
  - condition: [When this error occurs]
    message: [Error message returned]
    recovery: [How to handle this error]
notes: [Any special considerations, limitations, or best practices]
related_tools: [List of tools commonly used together]
```

---

## Tool Categories

### 1. GL (General Ledger) Tools

```yaml
tool_name: sage_intacct.get_gl_account_balance
category: GL
description: Retrieves current and historical balance for a GL account
when_to_use:
  - User asks for account balance
  - Before creating journal entries
  - During reconciliation processes
  - Part of financial statement preparation
parameters:
  - name: account_number
    type: string
    required: true
    description: GL account number from chart of accounts
    example: "1000"
  - name: as_of_date
    type: date
    required: false
    description: Balance as of specific date (ISO format)
    example: "2024-12-31"
  - name: include_details
    type: boolean
    required: false
    description: Include transaction details
    example: false
returns:
  type: object
  description: Account balance with metadata
example_usage: |
  User: "What's the balance in cash account 1000?"
  Tool Call: get_gl_account_balance({ 
    account_number: "1000",
    include_details: true 
  })
  Response: {
    account_number: "1000",
    account_name: "Cash - Operating",
    balance: 125000.00,
    balance_type: "debit",
    as_of_date: "2024-12-31",
    last_activity: "2024-12-30"
  }
errors:
  - condition: Account not found
    message: "GL account {account_number} not found"
    recovery: List available accounts or create new account
notes: Debit/credit normal balance depends on account type
related_tools: [create_journal_entry, get_trial_balance]
```

### 2. AR (Accounts Receivable) Tools

```yaml
tool_name: sage_intacct.create_sales_invoice
category: AR
description: Creates a new sales invoice for a customer
when_to_use:
  - User wants to bill a customer
  - Recording revenue transactions
  - Converting sales orders to invoices
parameters:
  - name: customer_id
    type: string
    required: true
    description: Customer identifier
    example: "CUST001"
  - name: invoice_date
    type: date
    required: true
    description: Invoice date (ISO format)
    example: "2024-12-31"
  - name: due_date
    type: date
    required: false
    description: Payment due date (defaults to terms)
    example: "2025-01-30"
  - name: line_items
    type: array
    required: true
    description: Array of invoice line items
    example: [{ item_id: "PROD001", quantity: 10, unit_price: 50.00 }]
returns:
  type: object
  description: Created invoice details with number
example_usage: |
  User: "Create an invoice for customer ABC Corp for 10 widgets at $50 each"
  Tool Call: create_sales_invoice({
    customer_id: "ABC001",
    invoice_date: "2024-12-31",
    line_items: [{
      item_id: "WIDGET",
      description: "Standard Widget",
      quantity: 10,
      unit_price: 50.00
    }]
  })
errors:
  - condition: Customer credit limit exceeded
    message: "Invoice exceeds customer credit limit"
    recovery: Request credit limit increase or partial invoice
  - condition: Period closed
    message: "Cannot post to closed period"
    recovery: Use current open period or request period reopen
notes: Invoice numbering is automatic based on sequence
related_tools: [get_customer_balance, apply_payment]
```

### 3. AP (Accounts Payable) Tools

```yaml
tool_name: sage_intacct.create_payment_batch
category: AP
description: Creates a batch of vendor payments optimizing for discounts
when_to_use:
  - Processing multiple vendor payments
  - Weekly/monthly payment runs
  - Taking advantage of early payment discounts
parameters:
  - name: payment_date
    type: date
    required: true
    description: Date payments will be made
    example: "2024-12-31"
  - name: payment_method
    type: string
    required: true
    description: Payment method (CHECK|ACH|WIRE)
    example: "CHECK"
  - name: selection_criteria
    type: object
    required: false
    description: Criteria for selecting bills
    example: { 
      due_by: "2025-01-15",
      take_discounts: true,
      vendor_groups: ["PREFERRED"]
    }
returns:
  type: object
  description: Payment batch details with selected bills
example_usage: |
  User: "Create payment batch for all bills due by month end"
  Tool Call: create_payment_batch({
    payment_date: "2024-12-31",
    payment_method: "ACH",
    selection_criteria: {
      due_by: "2024-12-31",
      take_discounts: true
    }
  })
errors:
  - condition: Insufficient cash
    message: "Insufficient funds for payment batch"
    recovery: Reduce batch size or defer payments
notes: Reviews approval limits before processing
related_tools: [get_cash_requirements, approve_payments]
```

### 4. Analysis Tools

```yaml
tool_name: sage_intacct.calculate_financial_ratios
category: Analysis
description: Calculates standard financial ratios from current data
when_to_use:
  - Financial health assessment
  - Loan covenant monitoring
  - Performance benchmarking
parameters:
  - name: ratio_types
    type: array
    required: true
    description: Types of ratios to calculate
    example: ["liquidity", "leverage", "profitability"]
  - name: period
    type: string
    required: true
    description: Period for calculation
    example: "2024-Q4"
  - name: compare_to_prior
    type: boolean
    required: false
    description: Include prior period comparison
    example: true
returns:
  type: object
  description: Calculated ratios with interpretations
example_usage: |
  User: "Calculate liquidity ratios for this quarter"
  Tool Call: calculate_financial_ratios({
    ratio_types: ["liquidity"],
    period: "2024-Q4",
    compare_to_prior: true
  })
errors:
  - condition: Incomplete financial data
    message: "Missing required accounts for ratio calculation"
    recovery: Run data validation first
notes: Uses industry-standard ratio formulas
related_tools: [variance_analysis, trend_analysis]
```

### 5. Reporting Tools

```yaml
tool_name: sage_intacct.generate_financial_statements
category: Report
description: Generates formatted financial statements
when_to_use:
  - Month-end reporting
  - Board presentations
  - External reporting requirements
parameters:
  - name: statement_types
    type: array
    required: true
    description: Statements to generate
    example: ["income_statement", "balance_sheet"]
  - name: period
    type: string
    required: true
    description: Reporting period
    example: "2024-12"
  - name: format
    type: string
    required: false
    description: Output format (PDF|EXCEL|JSON)
    example: "PDF"
  - name: comparative
    type: boolean
    required: false
    description: Include prior period comparison
    example: true
returns:
  type: object
  description: Generated statements with metadata
example_usage: |
  User: "Generate P&L and balance sheet for December"
  Tool Call: generate_financial_statements({
    statement_types: ["income_statement", "balance_sheet"],
    period: "2024-12",
    format: "PDF",
    comparative: true
  })
errors:
  - condition: Period not closed
    message: "Period must be closed for final statements"
    recovery: Generate draft statements instead
notes: Follows GAAP formatting standards
related_tools: [get_trial_balance, generate_dashboards]
```

### 6. Validation Tools

```yaml
tool_name: sage_intacct.validate_period_close
category: Validation
description: Validates readiness for period close
when_to_use:
  - Before closing a period
  - Month-end checklist
  - Pre-close validation
parameters:
  - name: period
    type: string
    required: true
    description: Period to validate
    example: "2024-12"
  - name: validation_level
    type: string
    required: false
    description: Validation depth (BASIC|FULL)
    example: "FULL"
returns:
  type: object
  description: Validation results with issues found
example_usage: |
  User: "Check if December is ready to close"
  Tool Call: validate_period_close({
    period: "2024-12",
    validation_level: "FULL"
  })
errors:
  - condition: Period already closed
    message: "Period {period} is already closed"
    recovery: Check next period or reopen if needed
notes: Runs multiple validation checks in sequence
related_tools: [check_data_integrity, execute_month_end_close]
```

---

## Tool Composition Patterns

### Sequential Tools
```yaml
pattern: Balance Inquiry with History
sequence:
  1. get_gl_account_balance
  2. get_account_transactions
  3. format_account_report
```

### Parallel Tools
```yaml
pattern: Multi-Module Balance Check
parallel:
  - get_gl_balances
  - get_ar_summary
  - get_ap_summary
aggregate: combine_financial_summary
```

### Conditional Tools
```yaml
pattern: Smart Payment Processing
condition: if has_early_payment_discounts
  then: create_payment_batch
  else: defer_payment_selection
```

---

## Best Practices for Tool Descriptions

1. **Clear Purpose**: Each tool should have one primary purpose
2. **Specific Scenarios**: List 3-5 specific use cases
3. **Complete Parameters**: Document all parameters with examples
4. **Error Handling**: Include common errors and recovery paths
5. **Relationships**: Note tools commonly used together
6. **Version Notes**: Track tool version changes if applicable

---

## Dynamic Tool Discovery Format

When tools are discovered at runtime, convert to this format:

```javascript
{
  "discovered_tool": {
    "server": "sage_intacct",
    "name": "tool_name",
    "schema": { /* original schema */ }
  },
  "formatted_description": {
    /* Use template format above */
  }
}
```

This ensures consistency regardless of tool source.
