# Business Systems Documentation

This directory contains documentation for working with multiple business systems (MCP servers) in the AI Workspace Agents project.

## 📚 Available Documentation

### 1. [Multi-MCP Architecture Overview](./multi-mcp-architecture.md)
Understanding the architecture that supports multiple business systems.
- Architecture benefits
- Directory structure
- Cross-system integration
- Configuration management

### 2. [Adding New Business System - Complete Guide](./adding-new-business-system-guide.md)
Comprehensive step-by-step guide for adding a new business system.
- **Target Audience**: All developers, especially juniors
- **Length**: Full tutorial with examples
- **Includes**: SBCA implementation example, testing, troubleshooting

### 3. [Quick Reference - New Business System](./quick-reference-new-business-system.md)
Quick checklist for experienced developers.
- **Target Audience**: Experienced developers
- **Length**: 5-minute reference
- **Includes**: Checklist, common pitfalls, key patterns

### 4. [MCP Server Configuration Notes](./mcp-server-config-notes.md)
Important notes about actual Claude Desktop MCP server configurations.
- **Target Audience**: All developers
- **Length**: Configuration reference
- **Includes**: Real examples, troubleshooting, key differences

## 🎯 Which Document Should I Read?

- **New to the project?** → Start with the [Complete Guide](./adding-new-business-system-guide.md)
- **Need architecture overview?** → Read [Multi-MCP Architecture](./multi-mcp-architecture.md)
- **Already familiar?** → Use the [Quick Reference](./quick-reference-new-business-system.md)

## 🏢 Supported Business Systems

Currently implemented:
- **Sage Intacct** (`agents/intacct/`)
  - GL Agent
  - AR Agent
  - AP Agent (planned)

Example structure provided for:
- **Sage Business Cloud Accounting** (`agents/sbca/`)
  - Sales Agent (example)
  - Inventory Agent (planned)

## 🔧 Key Concepts

1. **MCP Servers**: External servers that connect to business systems
2. **Agents**: AI-powered modules for specific business functions
3. **Tool Mappers**: Translation layers between agents and MCP servers
4. **Agent Manager**: Centralized registry and lifecycle management

## 📁 Project Structure

```
ai-workspace-agents/
├── agents/
│   ├── base/              # Base classes
│   ├── intacct/          # Sage Intacct agents
│   └── sbca/             # SBCA agents (example)
├── services/
│   ├── api_service.py    # REST/WebSocket API
│   └── agent_manager.py  # Agent lifecycle
└── fastagent.config.yaml # MCP server configuration
```

## 🚀 Getting Started

1. Choose your business system
2. Follow the [Complete Guide](./adding-new-business-system-guide.md)
3. Use existing implementations as reference
4. Test thoroughly
5. Document your implementation

## 📝 Contributing

When adding a new business system:
1. Follow the established patterns
2. Write comprehensive tests
3. Document your agents
4. Update this README with your system

---
*For questions or issues, refer to the troubleshooting sections in the guides or contact the team.*
