"""
Centralized MCP Server Configuration

This module provides a single source of truth for MCP server configurations.
Changes here automatically propagate to all agents and services.
"""

import os
import yaml
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
import structlog

logger = structlog.get_logger(__name__)


class MCPServerConfig(BaseModel):
    """Configuration for a single MCP server"""
    name: str
    command: Optional[str] = None  # Optional for SSE/HTTP connections
    args: List[str] = Field(default_factory=list)
    env: Dict[str, str] = Field(default_factory=dict)
    health_check: Dict[str, Any] = Field(default_factory=dict)
    connection_type: str = "local"  # local, hosted, sse
    endpoint: Optional[str] = None  # For hosted/sse servers
    transport: Optional[str] = None  # Transport type: stdio, sse, http
    url: Optional[str] = None  # URL for SSE/HTTP transport
    headers: Dict[str, str] = Field(default_factory=dict)  # Headers for SSE/HTTP


class BusinessSystemConfig(BaseModel):
    """Configuration for a business system and its agents"""
    system_name: str  # e.g., "intacct", "sbca", "quickbooks"
    mcp_server_name: str  # The actual MCP server name in config
    agents: List[str] = Field(default_factory=list)  # Agent IDs using this system
    tool_mapper_class: str  # Full class path to tool mapper
    is_composite: bool = True  # Whether server includes all modules


class MCPConfiguration:
    """
    Centralized MCP configuration manager.
    
    Provides a single source of truth for:
    - MCP server definitions
    - Business system mappings
    - Agent-to-server associations
    """
    
    _instance = None
    _config_loaded = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._config_loaded:
            self.mcp_servers: Dict[str, MCPServerConfig] = {}
            self.business_systems: Dict[str, BusinessSystemConfig] = {}
            self.agent_mappings: Dict[str, str] = {}  # agent_id -> mcp_server_name
            self._load_configuration()
            self._config_loaded = True
    
    def _load_configuration(self):
        """Load configuration from mcp.config.yaml"""
        config_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "mcp.config.yaml"
        )
        
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # Load MCP servers
            mcp_config = config.get('mcp', {})
            for server_name, server_config in mcp_config.get('servers', {}).items():
                self.mcp_servers[server_name] = MCPServerConfig(
                    name=server_name,
                    **server_config
                )
            
            # Define business system mappings
            # This centralizes the knowledge of which agents use which servers
            self.business_systems = {
                "intacct": BusinessSystemConfig(
                    system_name="intacct",
                    mcp_server_name="sage-intacct",  # Maps to actual MCP server
                    agents=["orchestrator_agent"],
                    tool_mapper_class="",  # No longer needed - orchestrator uses MCP directly
                    is_composite=True
                ),
                "sbca": BusinessSystemConfig(
                    system_name="sbca",
                    mcp_server_name="Sage Business Cloud Accounting",
                    agents=["sales_agent", "purchase_agent", "banking_agent"],
                    tool_mapper_class="agents.sbca.sbca_tool_mapper.SBCAToolMapper",
                    is_composite=True
                ),
                # Add more business systems here
            }
            
            # Build agent mappings
            for system in self.business_systems.values():
                for agent_id in system.agents:
                    self.agent_mappings[agent_id] = system.mcp_server_name
            
            logger.info(
                "MCP configuration loaded",
                servers=list(self.mcp_servers.keys()),
                systems=list(self.business_systems.keys()),
                agents=len(self.agent_mappings)
            )
            
        except Exception as e:
            logger.error("Failed to load MCP configuration", error=str(e))
            raise
    
    def get_mcp_server_for_agent(self, agent_id: str) -> Optional[str]:
        """Get the MCP server name for a given agent"""
        return self.agent_mappings.get(agent_id)
    
    def get_mcp_servers_for_agent(self, agent_id: str) -> List[str]:
        """Get list of MCP servers for an agent (for FastAgent servers parameter)"""
        server = self.get_mcp_server_for_agent(agent_id)
        return [server] if server else []
    
    def get_business_system_for_agent(self, agent_id: str) -> Optional[BusinessSystemConfig]:
        """Get the business system configuration for an agent"""
        for system in self.business_systems.values():
            if agent_id in system.agents:
                return system
        return None
    
    def get_tool_mapper_for_agent(self, agent_id: str) -> Optional[Any]:
        """
        Dynamically load and instantiate the tool mapper for an agent.
        
        Returns:
            Instantiated tool mapper or None if not found
        """
        system = self.get_business_system_for_agent(agent_id)
        if not system:
            return None
        
        # Check if tool mapper class is specified
        if not system.tool_mapper_class:
            logger.debug(f"No tool mapper specified for agent {agent_id}")
            return None
        
        try:
            # Dynamic import of tool mapper class
            module_path, class_name = system.tool_mapper_class.rsplit('.', 1)
            module = __import__(module_path, fromlist=[class_name])
            mapper_class = getattr(module, class_name)
            
            # Instantiate with the MCP server name
            return mapper_class(system.mcp_server_name)
            
        except Exception as e:
            logger.error(
                "Failed to load tool mapper",
                agent_id=agent_id,
                mapper_class=system.tool_mapper_class,
                error=str(e)
            )
            return None
    
    def get_server_config(self, server_name: str) -> Optional[MCPServerConfig]:
        """Get configuration for a specific MCP server"""
        return self.mcp_servers.get(server_name)
    
    def update_server_endpoint(self, server_name: str, new_endpoint: str):
        """
        Update server endpoint (useful for switching between local and remote).
        
        This is the key method for runtime configuration changes!
        """
        if server_name in self.mcp_servers:
            server = self.mcp_servers[server_name]
            
            # Update connection type based on endpoint
            if new_endpoint.startswith("http"):
                server.connection_type = "hosted"
                server.endpoint = new_endpoint
                # Update command for hosted services
                server.command = "npx"
                server.args = ["supergateway", new_endpoint]
            else:
                # Switching back to local
                server.connection_type = "local"
                server.endpoint = None
                # Restore local command (would need to store original)
            
            logger.info(
                "Updated MCP server endpoint",
                server=server_name,
                endpoint=new_endpoint,
                type=server.connection_type
            )
    
    def list_agents_by_system(self, system_name: str) -> List[str]:
        """List all agents for a business system"""
        system = self.business_systems.get(system_name)
        return system.agents if system else []
    
    def validate_configuration(self) -> Dict[str, Any]:
        """Validate the configuration and return any issues"""
        issues = []
        
        # Check that all mapped servers exist
        for system in self.business_systems.values():
            if system.mcp_server_name not in self.mcp_servers:
                issues.append(f"Business system '{system.system_name}' references "
                            f"non-existent MCP server '{system.mcp_server_name}'")
        
        # Check agent mappings
        for agent_id, server_name in self.agent_mappings.items():
            if server_name not in self.mcp_servers:
                issues.append(f"Agent '{agent_id}' mapped to "
                            f"non-existent server '{server_name}'")
        
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "stats": {
                "mcp_servers": len(self.mcp_servers),
                "business_systems": len(self.business_systems),
                "agent_mappings": len(self.agent_mappings)
            }
        }


# Singleton instance
_mcp_config = None


def get_mcp_config() -> MCPConfiguration:
    """Get the singleton MCP configuration instance"""
    global _mcp_config
    if _mcp_config is None:
        _mcp_config = MCPConfiguration()
    return _mcp_config


# Convenience functions for common operations
def get_agent_mcp_servers(agent_id: str) -> List[str]:
    """Get MCP servers list for FastAgent configuration"""
    return get_mcp_config().get_mcp_servers_for_agent(agent_id)


def get_agent_tool_mapper(agent_id: str):
    """Get instantiated tool mapper for an agent"""
    return get_mcp_config().get_tool_mapper_for_agent(agent_id)


def switch_to_remote_mcp(server_name: str, remote_endpoint: str):
    """Switch an MCP server from local to remote"""
    get_mcp_config().update_server_endpoint(server_name, remote_endpoint)


# For testing
if __name__ == "__main__":
    config = get_mcp_config()
    
    # Validate configuration
    validation = config.validate_configuration()
    print(f"Configuration valid: {validation['valid']}")
    if not validation['valid']:
        print("Issues found:")
        for issue in validation['issues']:
            print(f"  - {issue}")
    
    # Test agent lookups
    print("\nAgent MCP Server Mappings:")
    for agent_id in ["orchestrator_agent", "sales_agent", "purchase_agent"]:
        server = config.get_mcp_server_for_agent(agent_id)
        print(f"  {agent_id} -> {server}")
    
    # Test dynamic configuration change
    print("\nTesting dynamic configuration:")
    print(f"Before: sage-intacct type = {config.get_server_config('sage-intacct').connection_type}")
    
    # Simulate switching to remote
    switch_to_remote_mcp("sage-intacct", "https://intacct-mcp.example.com/sse")
    print(f"After: sage-intacct type = {config.get_server_config('sage-intacct').connection_type}")
