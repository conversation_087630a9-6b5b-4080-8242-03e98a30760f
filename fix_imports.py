#!/usr/bin/env python3
"""
Quick fix for missing structlog imports.

This script adds fallback imports for structlog in all Python files.
"""

import os
import re


def fix_structlog_import(filepath):
    """Fix structlog import in a single file."""
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if file imports structlog
    if 'import structlog' not in content:
        return False
    
    # Check if already has try/except
    if 'except ImportError:' in content and 'mock_logger' in content:
        return False
    
    # Replace simple import with try/except
    pattern = r'^import structlog$'
    replacement = '''try:
    import structlog
except ImportError:
    from services.mock_logger import structlog'''
    
    new_content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
    
    if new_content != content:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(new_content)
        return True
    
    return False


def main():
    """Fix imports in all Python files."""
    fixed_files = []
    
    for root, dirs, files in os.walk('.'):
        # Skip hidden directories
        dirs[:] = [d for d in dirs if not d.startswith('.')]
        
        for file in files:
            if file.endswith('.py'):
                filepath = os.path.join(root, file)
                if fix_structlog_import(filepath):
                    fixed_files.append(filepath)
    
    print(f"Fixed {len(fixed_files)} files:")
    for f in fixed_files:
        print(f"  - {f}")


if __name__ == "__main__":
    main()