# Adding MCP Servers to AI Workspace Agents

## Overview

This guide explains how to add new MCP (Model Context Protocol) servers to the AI Workspace Agents system. MCP servers provide the actual tools and integrations with external business systems like QuickBooks, Xero, Salesforce, etc.

## Two Methods to Add MCP Servers

### Method 1: Via REST API (Runtime - Recommended)

Add servers dynamically without restarting the service.

**Endpoint**: `POST /mcp/servers`

**Example Request**:
```bash
curl -X POST http://localhost:8000/mcp/servers \
  -H "Content-Type: application/json" \
  -d '{
    "name": "quickbooks",
    "command": "python",
    "args": ["-m", "quickbooks_mcp.main"],
    "env": {
      "QB_CLIENT_ID": "${QB_CLIENT_ID}",
      "QB_CLIENT_SECRET": "${QB_CLIENT_SECRET}",
      "PYTHONPATH": "C:\\mcp-servers\\quickbooks"
    },
    "connection_type": "local",
    "health_check": {
      "enabled": true,
      "interval": 30,
      "timeout": 10
    },
    "tags": ["accounting", "quickbooks", "financial"],
    "metadata": {
      "modules": ["invoicing", "payments", "reports"],
      "api_version": "v3"
    },
    "persistent": true
  }'
```

### Method 2: Via Configuration File

Add to `fastagent.config.yaml` for servers that should always be available at startup.

```yaml
mcp:
  servers:
    quickbooks:
      command: python
      args: ["-m", "quickbooks_mcp.main"]
      env:
        QB_CLIENT_ID: ${QB_CLIENT_ID}
        QB_CLIENT_SECRET: ${QB_CLIENT_SECRET}
        PYTHONPATH: "C:\\mcp-servers\\quickbooks"
      health_check:
        enabled: true
        interval: 30
        timeout: 10
      tags: ["accounting", "quickbooks"]
      metadata:
        modules: ["invoicing", "payments", "reports"]
```

## Required Configuration Details

### 1. **name** (required)
- **Type**: string
- **Description**: Unique identifier for the server
- **Examples**: `"quickbooks"`, `"xero"`, `"salesforce"`, `"hubspot"`
- **Rules**: Must be unique across all servers

### 2. **command** (required)
- **Type**: string
- **Description**: The executable to run the MCP server
- **Common Values**:
  - `"python"` - For Python-based MCP servers
  - `"node"` - For Node.js-based servers
  - `"npx"` - For NPM-based servers
  - `"cmd"` - For Windows batch scripts
  - `"bash"` - For shell scripts

### 3. **args** (optional)
- **Type**: array of strings
- **Description**: Command-line arguments for the executable
- **Examples**:
  - Python: `["-m", "module.main", "--mode", "production"]`
  - Node: `["server.js", "--port", "3000"]`
  - NPX: `["supergateway", "https://your-server.com/sse"]`
  - Script: `["/c", "cd /d C:\\path && python server.py"]`

### 4. **env** (optional)
- **Type**: object (key-value pairs)
- **Description**: Environment variables needed by the MCP server
- **Common Variables**:
  ```json
  {
    "API_KEY": "${YOUR_API_KEY}",
    "API_SECRET": "${YOUR_SECRET}",
    "CLIENT_ID": "${CLIENT_ID}",
    "PYTHONPATH": "/path/to/server",
    "NODE_ENV": "production"
  }
  ```
- **Note**: Use `${VAR_NAME}` syntax to reference environment variables

### 5. **connection_type** (optional)
- **Type**: string
- **Default**: `"local"`
- **Options**:
  - `"local"` - Server runs as a subprocess (most common)
  - `"hosted"` - Connect to remote HTTP endpoint
  - `"sse"` - Server-Sent Events connection

### 6. **endpoint** (conditional)
- **Type**: string
- **Required for**: `hosted` or `sse` connection types
- **Examples**:
  - `"https://api.example.com/mcp"`
  - `"wss://mcp.example.com/websocket"`
  - `"https://mcp-gateway.com/sse"`

### 7. **health_check** (optional)
- **Type**: object
- **Default**: `{"enabled": true, "interval": 30, "timeout": 10}`
- **Properties**:
  - `enabled` (boolean): Whether to monitor server health
  - `interval` (number): Seconds between health checks
  - `timeout` (number): Seconds before health check times out

### 8. **tags** (optional)
- **Type**: array of strings
- **Description**: Categories for organizing and filtering servers
- **Examples**: `["financial", "crm", "inventory", "hr", "external"]`

### 9. **metadata** (optional)
- **Type**: object
- **Description**: Additional information about the server
- **Examples**:
  ```json
  {
    "modules": ["sales", "inventory", "customers"],
    "version": "2.0",
    "region": "US",
    "capabilities": ["read", "write", "delete"],
    "rate_limit": "100/minute"
  }
  ```

### 10. **persistent** (optional)
- **Type**: boolean
- **Default**: `true`
- **Description**: Whether to save the configuration for restart persistence

## Real-World Examples

### Example 1: QuickBooks Integration
```json
{
  "name": "quickbooks",
  "command": "python",
  "args": ["-m", "quickbooks_mcp.server", "--production"],
  "env": {
    "QB_CLIENT_ID": "${QB_CLIENT_ID}",
    "QB_CLIENT_SECRET": "${QB_CLIENT_SECRET}",
    "QB_COMPANY_ID": "${QB_COMPANY_ID}",
    "QB_REFRESH_TOKEN": "${QB_REFRESH_TOKEN}",
    "PYTHONPATH": "C:\\mcp-servers\\quickbooks"
  },
  "tags": ["accounting", "quickbooks", "financial", "invoicing"],
  "metadata": {
    "modules": ["customers", "invoices", "payments", "reports"],
    "api_version": "v3",
    "oauth_type": "OAuth2"
  }
}
```

### Example 2: Salesforce CRM
```json
{
  "name": "salesforce",
  "command": "node",
  "args": ["./sf-mcp-server.js"],
  "env": {
    "SF_USERNAME": "${SF_USERNAME}",
    "SF_PASSWORD": "${SF_PASSWORD}",
    "SF_TOKEN": "${SF_TOKEN}",
    "SF_INSTANCE_URL": "https://your-instance.salesforce.com"
  },
  "health_check": {
    "enabled": true,
    "interval": 60,
    "timeout": 15
  },
  "tags": ["crm", "salesforce", "sales"],
  "metadata": {
    "modules": ["contacts", "opportunities", "leads", "accounts"],
    "api_version": "57.0"
  }
}
```

### Example 3: External MCP Gateway (SSE)
```json
{
  "name": "external-analytics",
  "connection_type": "sse",
  "endpoint": "https://analytics-mcp.example.com/sse",
  "env": {
    "AUTH_TOKEN": "${ANALYTICS_TOKEN}",
    "TENANT_ID": "${TENANT_ID}"
  },
  "tags": ["analytics", "external", "reporting"],
  "metadata": {
    "provider": "ThirdPartyAnalytics",
    "rate_limit": "1000/hour"
  }
}
```

### Example 4: Local Script-Based Server
```json
{
  "name": "custom-erp",
  "command": "cmd",
  "args": ["/c", "cd /d C:\\erp-integration && python start_mcp.py"],
  "env": {
    "ERP_HOST": "*************",
    "ERP_PORT": "8080",
    "ERP_API_KEY": "${ERP_API_KEY}"
  },
  "tags": ["erp", "inventory", "manufacturing"]
}
```

## Post-Installation Steps

### 1. Verify Server Status
```bash
# Check if server is running
curl http://localhost:8000/mcp/servers/quickbooks/health

# Response:
{
  "server": "quickbooks",
  "status": "healthy",
  "last_health_check": "2025-06-04T10:30:00Z",
  "error_count": 0
}
```

### 2. Discover Available Tools
```bash
# List all tools from the new server
curl "http://localhost:8000/mcp/tools?server=quickbooks"

# Response shows available tools:
{
  "tools": [
    {
      "name": "create_invoice",
      "description": "Create a new invoice in QuickBooks",
      "server_name": "quickbooks",
      "category": "financial",
      "capabilities": ["create", "write"]
    },
    ...
  ]
}
```

### 3. Test with Orchestrator
```bash
# Send a test request
curl -X POST http://localhost:8000/agents/orchestrator_agent/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Create an invoice for ABC Company for $500 in QuickBooks",
    "context": {}
  }'
```

## Managing MCP Servers

### Update Server Configuration
```bash
curl -X PATCH http://localhost:8000/mcp/servers/quickbooks \
  -H "Content-Type: application/json" \
  -d '{
    "env": {
      "QB_REFRESH_TOKEN": "${NEW_REFRESH_TOKEN}"
    },
    "tags": ["accounting", "quickbooks", "updated"]
  }'
```

### Remove a Server
```bash
# Graceful removal
curl -X DELETE http://localhost:8000/mcp/servers/quickbooks

# Force removal (even if unhealthy)
curl -X DELETE http://localhost:8000/mcp/servers/quickbooks?force=true
```

### List All Servers
```bash
# Get all servers
curl http://localhost:8000/mcp/servers

# Filter by tags
curl "http://localhost:8000/mcp/servers?tags=accounting,financial"
```

## Troubleshooting

### Common Issues

1. **Server Won't Start**
   - Check command and args are correct
   - Verify all required environment variables are set
   - Check MCP server logs for errors
   - Ensure the executable is in PATH

2. **Authentication Failures**
   - Verify API credentials in environment
   - Check token expiration
   - Ensure proper OAuth flow completion

3. **Tools Not Discovered**
   - Wait for health check interval to pass
   - Check server implements tool listing correctly
   - Verify server is actually running
   - Check for connection timeouts

4. **Performance Issues**
   - Adjust health check intervals
   - Check server resource usage
   - Consider connection pooling
   - Monitor rate limits

### Debug Commands

```bash
# Check server logs
docker logs ai-workspace-agents 2>&1 | grep quickbooks

# Test server directly
python -m quickbooks_mcp.server --test

# Check environment variables
echo $QB_CLIENT_ID
```

## Best Practices

1. **Security**
   - Never hardcode credentials
   - Use environment variables for all secrets
   - Rotate API keys regularly
   - Use least-privilege access

2. **Naming**
   - Use lowercase, hyphenated names
   - Be descriptive but concise
   - Include system name in server name

3. **Health Checks**
   - Enable for all production servers
   - Set appropriate intervals (30-60s)
   - Monitor health check failures

4. **Documentation**
   - Document all required environment variables
   - List available tools and their purposes
   - Include setup instructions
   - Note any limitations or quirks

5. **Testing**
   - Test server connection before adding
   - Verify all tools work as expected
   - Test error scenarios
   - Check performance under load

## Advanced Configuration

### Using Docker Compose
```yaml
# docker-compose.yml
services:
  quickbooks-mcp:
    image: quickbooks-mcp:latest
    environment:
      - QB_CLIENT_ID=${QB_CLIENT_ID}
      - QB_CLIENT_SECRET=${QB_CLIENT_SECRET}
    ports:
      - "8081:8081"
```

Then reference in MCP config:
```json
{
  "name": "quickbooks",
  "connection_type": "hosted",
  "endpoint": "http://quickbooks-mcp:8081",
  "tags": ["docker", "quickbooks"]
}
```

### Multi-Environment Setup
```json
{
  "name": "quickbooks-dev",
  "command": "python",
  "args": ["-m", "quickbooks_mcp.server", "--env", "development"],
  "env": {
    "QB_SANDBOX": "true",
    "QB_CLIENT_ID": "${QB_DEV_CLIENT_ID}"
  },
  "tags": ["development", "quickbooks"]
}
```

## Getting Help

- Check existing server configurations in `fastagent.config.yaml`
- Review MCP server documentation
- Enable debug logging: `LOG_LEVEL=debug`
- Contact the MCP server maintainer

---

*Last Updated: June 2025*
*Document Version: 1.0*