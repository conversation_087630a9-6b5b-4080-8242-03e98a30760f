# Quick Reference: Adding a New Business System

## 🚀 5-Minute Setup Checklist

### 1. Create Directory Structure
```bash
mkdir agents/your_system
touch agents/your_system/__init__.py
```

### 2. Create Tool Mapper (`your_system/your_system_tool_mapper.py`)
```python
from agents.base import BaseToolMapper

class YourSystemToolMapper(BaseToolMapper):
    def _initialize_mappings(self):
        self._tool_map = {
            "your-system.create_invoice": "mcp_create_invoice_tool",
            # Add more mappings
        }
        self._unavailable_tools = {
            "your-system.complex_feature": "Not yet supported"
        }
```

### 3. Create Agent - Two Options

#### Option A: Using Centralized Configuration (Recommended)
```python
from agents.base.configured_agent import ConfiguredAgent

class YourSystemAgent(ConfiguredAgent):
    def __init__(self):
        super().__init__(
            agent_id="your_system_agent",
            name="Your System Agent",
            instruction="You are an expert..."
        )
        # MCP server configured automatically!
    
    async def gather_data(self, params):
        # Implementation
        pass

your_system_agent = YourSystemAgent()
```

#### Option B: Traditional Approach
```python
from mcp_agent.core.fastagent import FastAgent
from agents.base import BaseAgent, AgentConfig

fast = FastAgent("Your Agent", config_path=config_file)

@fast.agent(
    name="your_system_agent",
    instruction="You are an expert...",
    servers=["your_system"],  # Hardcoded
    model="claude-3-sonnet-20240229"
)
class YourSystemAgent(BaseAgent):
    def __init__(self):
        config = AgentConfig(
            agent_id="your_system_agent",
            name="Your System Agent",
            description="Handles your business operations",
            mcp_server="your_system",
            capabilities=["List capabilities here"]
        )
        super().__init__(config)
```

### 4. Update Configuration

#### For Centralized Config (Recommended):
1. Add to `fastagent.config.yaml`:
```yaml
mcp_servers:
  your_system:
    command: python
    args: ["-m", "your_mcp_server.main"]
    cwd: "path/to/your-mcp-server"
    env:
      YOUR_SYSTEM_API_KEY: ${YOUR_SYSTEM_API_KEY}
```

2. Add to `config/mcp_config.py`:
```python
"your_system": BusinessSystemConfig(
    system_name="your_system",
    mcp_server_name="your_system",
    agents=["your_system_agent"],
    tool_mapper_class="agents.your_system.your_system_tool_mapper.YourSystemToolMapper"
)
```

#### For Traditional Config:
Just add the MCP server to `fastagent.config.yaml` as shown above.

### 5. Add Environment Variables (`.env`)
```
YOUR_SYSTEM_API_KEY=your_api_key_here
YOUR_SYSTEM_SECRET=your_secret_here
```

### 6. Test Your Agent
```python
python -m pytest tests/agents/test_your_agent.py
```

## 📁 File Structure Pattern

```
agents/
└── your_system/
    ├── __init__.py
    ├── your_system_tool_mapper.py
    ├── sales_agent.py
    ├── inventory_agent.py
    └── README.md
```

## 🔧 Key Classes to Extend

1. **BaseToolMapper** - For tool name translation
2. **BaseAgent** - For agent implementation
3. **AgentConfig** - For agent configuration

## 🧪 Testing Checklist

- [ ] Unit tests for intent analysis
- [ ] Tool mapper tests
- [ ] Integration tests
- [ ] API endpoint tests
- [ ] Error handling tests

## 📝 Documentation Required

- [ ] Agent capabilities
- [ ] Tool descriptions
- [ ] Configuration examples
- [ ] Common use cases
- [ ] Troubleshooting guide

## 🚨 Common Pitfalls

1. **Forgetting `__init__.py`** - Always create this file
2. **Wrong MCP server name** - Must match in config
3. **Missing environment variables** - Check `.env` file
4. **Import errors** - Use relative imports correctly
5. **Tool name mismatches** - Verify with MCP docs

## 🔗 Quick Links

- Full Guide: `docs/business-systems/adding-new-business-system-guide.md`
- Architecture: `docs/business-systems/multi-mcp-architecture.md`
- Base Classes: `agents/base/`
- Example Implementation: `agents/intacct/`
- Tests: `tests/agents/`

---
*For detailed instructions, see the full guide.*
