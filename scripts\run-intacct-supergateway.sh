#!/bin/bash

echo "Starting Sage Intacct MCP Server with Supergateway..."
echo ""
echo "This will expose the stdio MCP server as an SSE endpoint on http://localhost:8001"
echo ""

# Run supergateway to expose the Intacct stdio server as SSE
npx -y supergateway \
    --stdio "cd /mnt/c/Users/<USER>/Documents/GitHub/sage-intacct-mcp-server && python -m src.main" \
    --port 8001 \
    --baseUrl http://localhost:8001 \
    --ssePath /sse \
    --messagePath /message