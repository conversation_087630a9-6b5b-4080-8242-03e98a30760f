"""
Tool Discovery Service

Discovers and catalogs all available tools from MCP servers.
Provides unified tool catalog with search and filtering capabilities.
"""

import asyncio
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
import structlog
from pydantic import BaseModel, Field
import re
from fuzzywuzzy import fuzz
from collections import defaultdict

from .mcp_registry import get_mcp_registry, ToolSchema, MCPServerRegistry

logger = structlog.get_logger(__name__)


class ToolCategory(str, Enum):
    """Tool categories for organization."""
    FINANCIAL = "financial"
    ACCOUNTING = "accounting"
    REPORTING = "reporting"
    ANALYSIS = "analysis"
    VALIDATION = "validation"
    SEARCH = "search"
    SYSTEM = "system"
    TRANSACTION = "transaction"
    CUSTOMER = "customer"
    VENDOR = "vendor"
    GENERAL = "general"


class ToolCapability(str, Enum):
    """Tool capabilities for filtering."""
    READ = "read"
    WRITE = "write"
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    SEARCH = "search"
    ANALYZE = "analyze"
    REPORT = "report"
    VALIDATE = "validate"
    EXECUTE = "execute"


@dataclass
class ToolMetadata:
    """Extended metadata for a tool."""
    categories: Set[ToolCategory] = field(default_factory=set)
    capabilities: Set[ToolCapability] = field(default_factory=set)
    keywords: Set[str] = field(default_factory=set)
    related_tools: Set[str] = field(default_factory=set)
    usage_count: int = 0
    last_used: Optional[datetime] = None
    average_execution_time: Optional[float] = None
    success_rate: Optional[float] = None


class UnifiedToolSchema(BaseModel):
    """Unified schema for tools across all MCP servers."""
    # Basic info from ToolSchema
    name: str
    description: str
    parameters: Dict[str, Any]
    server_name: str
    
    # Extended unified fields
    full_name: str  # server_name:tool_name
    display_name: str  # Human-friendly name
    category: ToolCategory = ToolCategory.GENERAL
    capabilities: List[ToolCapability] = Field(default_factory=list)
    keywords: List[str] = Field(default_factory=list)
    examples: List[Dict[str, Any]] = Field(default_factory=list)
    
    # Metadata
    last_updated: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    metadata: ToolMetadata = Field(default_factory=ToolMetadata)
    
    @classmethod
    def from_tool_schema(cls, tool: ToolSchema) -> "UnifiedToolSchema":
        """Create unified schema from MCP tool schema."""
        # Determine category and capabilities from name and description
        category = cls._determine_category(tool.name, tool.description)
        capabilities = cls._determine_capabilities(tool.name, tool.description)
        keywords = cls._extract_keywords(tool.name, tool.description)
        
        return cls(
            name=tool.name,
            description=tool.description,
            parameters=tool.parameters,
            server_name=tool.server_name,
            full_name=f"{tool.server_name}:{tool.name}",
            display_name=cls._create_display_name(tool.name),
            category=category,
            capabilities=capabilities,
            keywords=keywords,
            metadata=ToolMetadata(
                categories={category},
                capabilities=set(capabilities),
                keywords=set(keywords)
            )
        )
    
    @staticmethod
    def _determine_category(name: str, description: str) -> ToolCategory:
        """Determine tool category from name and description."""
        text = f"{name} {description}".lower()
        
        # Category keywords
        category_keywords = {
            ToolCategory.FINANCIAL: ["financial", "finance", "money", "balance", "revenue", "expense"],
            ToolCategory.ACCOUNTING: ["journal", "entry", "account", "ledger", "trial balance"],
            ToolCategory.REPORTING: ["report", "summary", "dashboard", "consolidate", "generate"],
            ToolCategory.ANALYSIS: ["analyze", "analysis", "trend", "variance", "ratio", "metric"],
            ToolCategory.VALIDATION: ["validate", "check", "verify", "compliance", "audit"],
            ToolCategory.SEARCH: ["search", "find", "query", "lookup", "discover"],
            ToolCategory.SYSTEM: ["health", "status", "module", "server", "system"],
            ToolCategory.TRANSACTION: ["invoice", "bill", "payment", "transaction", "create", "apply"],
            ToolCategory.CUSTOMER: ["customer", "client", "receivable", "ar", "invoice"],
            ToolCategory.VENDOR: ["vendor", "supplier", "payable", "ap", "bill"]
        }
        
        # Score each category
        scores = {}
        for category, keywords in category_keywords.items():
            score = sum(1 for kw in keywords if kw in text)
            if score > 0:
                scores[category] = score
        
        # Return highest scoring category
        if scores:
            return max(scores.items(), key=lambda x: x[1])[0]
        
        return ToolCategory.GENERAL
    
    @staticmethod
    def _determine_capabilities(name: str, description: str) -> List[ToolCapability]:
        """Determine tool capabilities from name and description."""
        text = f"{name} {description}".lower()
        capabilities = []
        
        # Capability keywords
        capability_keywords = {
            ToolCapability.READ: ["get", "read", "fetch", "retrieve", "list", "view"],
            ToolCapability.WRITE: ["write", "save", "store", "persist"],
            ToolCapability.CREATE: ["create", "add", "new", "generate", "make"],
            ToolCapability.UPDATE: ["update", "modify", "edit", "change", "set"],
            ToolCapability.DELETE: ["delete", "remove", "clear", "purge"],
            ToolCapability.SEARCH: ["search", "find", "query", "lookup"],
            ToolCapability.ANALYZE: ["analyze", "analysis", "calculate", "compute"],
            ToolCapability.REPORT: ["report", "summary", "dashboard", "export"],
            ToolCapability.VALIDATE: ["validate", "check", "verify", "audit"],
            ToolCapability.EXECUTE: ["execute", "run", "perform", "process"]
        }
        
        for capability, keywords in capability_keywords.items():
            if any(kw in text for kw in keywords):
                capabilities.append(capability)
        
        # Default to READ if no capabilities found
        if not capabilities:
            capabilities.append(ToolCapability.READ)
        
        return capabilities
    
    @staticmethod
    def _extract_keywords(name: str, description: str) -> List[str]:
        """Extract keywords from name and description."""
        text = f"{name} {description}".lower()
        
        # Common words to exclude
        stop_words = {"a", "an", "the", "and", "or", "but", "in", "on", "at", "to", "for",
                      "of", "with", "by", "from", "as", "is", "was", "are", "were", "be",
                      "have", "has", "had", "do", "does", "did", "will", "would", "could",
                      "should", "may", "might", "must", "can", "this", "that", "these",
                      "those", "all"}
        
        # Extract words
        words = re.findall(r'\b[a-z]+\b', text)
        
        # Filter and dedupe
        keywords = []
        seen = set()
        for word in words:
            if word not in stop_words and word not in seen and len(word) > 2:
                keywords.append(word)
                seen.add(word)
        
        return keywords[:10]  # Limit to top 10 keywords
    
    @staticmethod
    def _create_display_name(name: str) -> str:
        """Create human-friendly display name."""
        # Convert snake_case to Title Case
        words = name.split('_')
        return ' '.join(word.capitalize() for word in words)


class ToolDiscoveryService:
    """
    Service for discovering and cataloging tools from MCP servers.
    
    Features:
    - Queries all registered MCP servers
    - Builds unified tool catalog
    - Provides search and filtering
    - Tool categorization and metadata
    """
    
    def __init__(self, registry: Optional[MCPServerRegistry] = None):
        self._registry = registry or get_mcp_registry()
        self._catalog: Dict[str, UnifiedToolSchema] = {}
        self._category_index: Dict[ToolCategory, Set[str]] = defaultdict(set)
        self._capability_index: Dict[ToolCapability, Set[str]] = defaultdict(set)
        self._keyword_index: Dict[str, Set[str]] = defaultdict(set)
        self._lock = asyncio.Lock()
        
        logger.info("ToolDiscoveryService initialized")
    
    async def discover_all_tools(self) -> List[UnifiedToolSchema]:
        """
        Discover all tools from all registered MCP servers.
        
        Returns:
            List of unified tool schemas
        """
        async with self._lock:
            # Get all tools from registry
            tools = await self._registry.get_all_tools()
            
            # Clear existing catalog
            self._catalog.clear()
            self._category_index.clear()
            self._capability_index.clear()
            self._keyword_index.clear()
            
            # Build unified catalog
            unified_tools = []
            for tool in tools:
                unified = UnifiedToolSchema.from_tool_schema(tool)
                self._catalog[unified.full_name] = unified
                unified_tools.append(unified)
                
                # Update indices
                self._update_indices(unified)
            
            logger.info(f"Discovered {len(unified_tools)} tools from {len(set(t.server_name for t in tools))} servers")
            return unified_tools
    
    def _update_indices(self, tool: UnifiedToolSchema):
        """Update search indices for a tool."""
        # Category index
        self._category_index[tool.category].add(tool.full_name)
        
        # Capability index
        for capability in tool.capabilities:
            self._capability_index[capability].add(tool.full_name)
        
        # Keyword index
        for keyword in tool.keywords:
            self._keyword_index[keyword].add(tool.full_name)
    
    async def get_tool_catalog(self) -> Dict[str, UnifiedToolSchema]:
        """
        Get the complete tool catalog.
        
        Returns:
            Dictionary of tool full names to unified schemas
        """
        if not self._catalog:
            await self.discover_all_tools()
        return self._catalog.copy()
    
    async def search_tools(
        self,
        query: str,
        category: Optional[ToolCategory] = None,
        capabilities: Optional[List[ToolCapability]] = None,
        server_name: Optional[str] = None,
        limit: int = 10
    ) -> List[UnifiedToolSchema]:
        """
        Search for tools based on various criteria.
        
        Args:
            query: Search query string
            category: Filter by category
            capabilities: Filter by required capabilities
            server_name: Filter by server
            limit: Maximum results to return
            
        Returns:
            List of matching tools sorted by relevance
        """
        if not self._catalog:
            await self.discover_all_tools()
        
        # Start with all tools
        candidates = list(self._catalog.values())
        
        # Filter by server
        if server_name:
            candidates = [t for t in candidates if t.server_name == server_name]
        
        # Filter by category
        if category:
            category_tools = self._category_index.get(category, set())
            candidates = [t for t in candidates if t.full_name in category_tools]
        
        # Filter by capabilities
        if capabilities:
            for capability in capabilities:
                capability_tools = self._capability_index.get(capability, set())
                candidates = [t for t in candidates if t.full_name in capability_tools]
        
        # Score and sort by relevance
        if query:
            scored_tools = []
            query_lower = query.lower()
            
            for tool in candidates:
                # Calculate relevance score
                score = self._calculate_relevance_score(tool, query_lower)
                scored_tools.append((score, tool))
            
            # Sort by score descending
            scored_tools.sort(key=lambda x: x[0], reverse=True)
            
            # Return top results
            return [tool for score, tool in scored_tools[:limit] if score > 0]
        else:
            # No query, return filtered results
            return candidates[:limit]
    
    def _calculate_relevance_score(self, tool: UnifiedToolSchema, query: str) -> float:
        """Calculate relevance score for a tool against a query."""
        score = 0.0
        
        # Exact name match
        if query == tool.name.lower():
            score += 100
        
        # Partial name match
        elif query in tool.name.lower():
            score += 50
        
        # Fuzzy name match
        name_ratio = fuzz.partial_ratio(query, tool.name.lower())
        score += name_ratio * 0.3
        
        # Description match
        if query in tool.description.lower():
            score += 30
        
        # Fuzzy description match
        desc_ratio = fuzz.partial_ratio(query, tool.description.lower())
        score += desc_ratio * 0.2
        
        # Keyword match
        for keyword in tool.keywords:
            if query in keyword or keyword in query:
                score += 10
        
        # Category match
        if query in tool.category.value:
            score += 20
        
        # Capability match
        for capability in tool.capabilities:
            if query in capability.value:
                score += 15
        
        return score
    
    async def get_tools_by_capability(
        self,
        capability: ToolCapability,
        category: Optional[ToolCategory] = None
    ) -> List[UnifiedToolSchema]:
        """
        Get all tools with a specific capability.
        
        Args:
            capability: Required capability
            category: Optional category filter
            
        Returns:
            List of tools with the capability
        """
        if not self._catalog:
            await self.discover_all_tools()
        
        tool_names = self._capability_index.get(capability, set())
        tools = [self._catalog[name] for name in tool_names if name in self._catalog]
        
        # Filter by category if specified
        if category:
            tools = [t for t in tools if t.category == category]
        
        return tools
    
    async def get_tools_by_category(self, category: ToolCategory) -> List[UnifiedToolSchema]:
        """
        Get all tools in a specific category.
        
        Args:
            category: Tool category
            
        Returns:
            List of tools in the category
        """
        if not self._catalog:
            await self.discover_all_tools()
        
        tool_names = self._category_index.get(category, set())
        return [self._catalog[name] for name in tool_names if name in self._catalog]
    
    async def get_tool_by_name(
        self,
        tool_name: str,
        server_name: Optional[str] = None
    ) -> Optional[UnifiedToolSchema]:
        """
        Get a specific tool by name.
        
        Args:
            tool_name: Tool name (can be short name or full name)
            server_name: Optional server name for disambiguation
            
        Returns:
            Tool if found, None otherwise
        """
        if not self._catalog:
            await self.discover_all_tools()
        
        # Try full name first
        if ':' in tool_name:
            return self._catalog.get(tool_name)
        
        # Search by short name
        for full_name, tool in self._catalog.items():
            if tool.name == tool_name:
                if server_name is None or tool.server_name == server_name:
                    return tool
        
        return None
    
    async def get_related_tools(self, tool_name: str, limit: int = 5) -> List[UnifiedToolSchema]:
        """
        Get tools related to a specific tool.
        
        Args:
            tool_name: Name of the tool
            limit: Maximum related tools to return
            
        Returns:
            List of related tools
        """
        tool = await self.get_tool_by_name(tool_name)
        if not tool:
            return []
        
        # Find tools with similar categories and keywords
        related = []
        
        for other_tool in self._catalog.values():
            if other_tool.full_name == tool.full_name:
                continue
            
            # Calculate similarity score
            score = 0
            
            # Same category
            if other_tool.category == tool.category:
                score += 30
            
            # Shared capabilities
            shared_capabilities = set(other_tool.capabilities) & set(tool.capabilities)
            score += len(shared_capabilities) * 10
            
            # Shared keywords
            shared_keywords = set(other_tool.keywords) & set(tool.keywords)
            score += len(shared_keywords) * 5
            
            if score > 0:
                related.append((score, other_tool))
        
        # Sort by score and return top results
        related.sort(key=lambda x: x[0], reverse=True)
        return [tool for score, tool in related[:limit]]
    
    async def refresh_catalog(self):
        """Refresh the tool catalog from all MCP servers."""
        await self._registry.refresh_tools()
        await self.discover_all_tools()
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics about the tool catalog."""
        if not self._catalog:
            return {
                "total_tools": 0,
                "servers": 0,
                "categories": {},
                "capabilities": {}
            }
        
        # Calculate statistics
        stats = {
            "total_tools": len(self._catalog),
            "servers": len(set(t.server_name for t in self._catalog.values())),
            "categories": {},
            "capabilities": {}
        }
        
        # Count by category
        for category in ToolCategory:
            count = len(self._category_index.get(category, set()))
            if count > 0:
                stats["categories"][category.value] = count
        
        # Count by capability
        for capability in ToolCapability:
            count = len(self._capability_index.get(capability, set()))
            if count > 0:
                stats["capabilities"][capability.value] = count
        
        return stats
    
    async def export_catalog(self) -> List[Dict[str, Any]]:
        """
        Export the tool catalog in a format suitable for LLM consumption.
        
        Returns:
            List of tool definitions
        """
        if not self._catalog:
            await self.discover_all_tools()
        
        tools = []
        for tool in self._catalog.values():
            tools.append({
                "name": tool.full_name,
                "display_name": tool.display_name,
                "description": tool.description,
                "category": tool.category.value,
                "capabilities": [c.value for c in tool.capabilities],
                "parameters": tool.parameters,
                "server": tool.server_name,
                "keywords": tool.keywords,
                "examples": tool.examples
            })
        
        return tools


# Global instance
_discovery_service: Optional[ToolDiscoveryService] = None


def get_tool_discovery_service() -> ToolDiscoveryService:
    """Get the global ToolDiscoveryService instance."""
    global _discovery_service
    if _discovery_service is None:
        _discovery_service = ToolDiscoveryService()
    return _discovery_service
