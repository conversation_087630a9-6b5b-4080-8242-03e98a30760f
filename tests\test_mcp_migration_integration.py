"""
Comprehensive integration tests for FastAgent to MCP Client migration.
Tests all phases of the migration to ensure everything works together.
"""

import asyncio
import json
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, List

# Import all the components we migrated
from services.mcp_client_wrapper import MC<PERSON><PERSON>Wrapper
from services.mcp_registry import MCPServerRegistry
from agents.orchestrator import DynamicOrchestrator
from services.response_processor import ResponseProcessor
from services.llm_service import LLMService
from services.parallel_executor import ParallelToolExecutor
from services.tool_discovery import ToolDiscoveryService


class TestMCPMigrationIntegration:
    """Complete integration tests for the MCP migration."""
    
    @pytest.fixture
    def mock_mcp_config(self):
        """Mock MCP configuration."""
        return {
            "sage-intacct": {
                "command": "node",
                "args": ["intacct-server.js"],
                "env": {"INTACCT_API_KEY": "test-key"},
                "tools": {
                    "search_across_modules": {
                        "description": "Search across Intacct modules",
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "query": {"type": "string"},
                                "modules": {"type": "array"},
                                "limit": {"type": "integer"}
                            }
                        }
                    },
                    "get_financial_summary": {
                        "description": "Get financial summary",
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "start_date": {"type": "string"},
                                "end_date": {"type": "string"}
                            }
                        }
                    }
                }
            }
        }
    
    @pytest.fixture
    def registry(self, mock_mcp_config):
        """Create MCP registry with mock config."""
        with patch('config.mcp_config.get_mcp_config', return_value=mock_mcp_config):
            return MCPServerRegistry()
    
    @pytest.fixture
    def orchestrator(self, registry):
        """Create orchestrator with registry."""
        return DynamicOrchestrator(registry=registry)
    
    @pytest.mark.asyncio
    async def test_phase2_mcp_client_wrapper(self):
        """Test Phase 2: MCP Client Wrapper functionality."""
        # Test different transport types
        config_stdio = {
            "command": "node",
            "args": ["server.js"],
            "env": {"API_KEY": "test"}
        }
        
        config_sse = {
            "url": "http://localhost:8080/sse",
            "transport": "sse"
        }
        
        # Create wrappers for different transports
        wrapper_stdio = MCPClientWrapper(config_stdio)
        wrapper_sse = MCPClientWrapper(config_sse)
        
        # Verify transport detection
        assert wrapper_stdio.transport_type == "stdio"
        assert wrapper_sse.transport_type == "sse"
        
        # Mock FastMCP client
        with patch('services.mcp_client_wrapper.fastmcp.FastMCP') as MockFastMCP:
            mock_client = AsyncMock()
            mock_client.list_tools = AsyncMock(return_value=[
                {
                    "name": "test_tool",
                    "description": "Test tool",
                    "inputSchema": {"type": "object"}
                }
            ])
            mock_client.call_tool = AsyncMock(return_value={"content": "test result"})
            MockFastMCP.return_value = mock_client
            
            # Test connection
            await wrapper_stdio.connect()
            
            # Test tool discovery
            tools = await wrapper_stdio.list_tools()
            assert len(tools) == 1
            assert tools[0]["name"] == "test_tool"
            
            # Test tool execution
            result = await wrapper_stdio.call_tool("test_tool", {"param": "value"})
            assert result == "test result"
            
            # Test health check
            is_healthy = await wrapper_stdio.health_check()
            assert is_healthy is True
    
    @pytest.mark.asyncio
    async def test_phase2_mcp_registry_integration(self, registry, mock_mcp_config):
        """Test Phase 2: MCP Registry with MCPClientWrapper."""
        with patch('services.mcp_client_wrapper.MCPClientWrapper') as MockWrapper:
            # Mock the wrapper
            mock_wrapper = AsyncMock()
            mock_wrapper.connect = AsyncMock()
            mock_wrapper.list_tools = AsyncMock(return_value=[])
            mock_wrapper.call_tool = AsyncMock(return_value={"content": "test result"})
            mock_wrapper.health_check = AsyncMock(return_value=True)
            MockWrapper.return_value = mock_wrapper
            
            # Register and start server
            registry.register_server("sage-intacct", mock_mcp_config["sage-intacct"])
            await registry._start_server("sage-intacct")
            
            # Test getting client
            client = registry.get_client("sage-intacct")
            assert client is not None
            
            # Test calling tool
            result = await registry.call_tool("sage-intacct", "search_across_modules", {"query": "test"})
            assert result == {"content": "test result"}
            
            # Test health check
            health = await registry.health_check("sage-intacct")
            assert health is True
    
    @pytest.mark.asyncio
    async def test_phase3_orchestrator_without_fastagent(self, orchestrator):
        """Test Phase 3: Orchestrator works without FastAgent."""
        # Verify no FastAgent references
        assert not hasattr(orchestrator, 'fast_agent')
        
        # Mock registry's call_tool method
        with patch.object(orchestrator.registry, 'call_tool') as mock_call_tool:
            mock_call_tool.return_value = {"sales": 100000, "currency": "USD"}
            
            # Test direct tool execution
            result = await orchestrator._execute_tool_directly(
                "sage-intacct:get_financial_summary",
                {"start_date": "2024-01-01", "end_date": "2024-01-31"}
            )
            
            assert result["sales"] == 100000
            mock_call_tool.assert_called_once_with(
                "sage-intacct",
                "get_financial_summary",
                {"start_date": "2024-01-01", "end_date": "2024-01-31"}
            )
    
    @pytest.mark.asyncio
    async def test_phase4_tool_execution_engine(self, orchestrator):
        """Test Phase 4: Tool execution with parallel and workflow support."""
        # Mock tool execution
        async def mock_tool_executor(tool_name: str, params: Dict[str, Any]):
            return {
                "tool": tool_name,
                "result": f"Executed {tool_name}",
                "params": params
            }
        
        orchestrator._execute_tool_directly = mock_tool_executor
        
        # Test parallel execution
        parallel_executor = ParallelToolExecutor(
            tool_executor=orchestrator._execute_tool_directly
        )
        
        tasks = [
            {
                "tool": "sage-intacct:search_across_modules",
                "params": {"query": "sales"},
                "id": "task1"
            },
            {
                "tool": "sage-intacct:get_financial_summary", 
                "params": {"start_date": "2024-01-01"},
                "id": "task2"
            }
        ]
        
        results = await parallel_executor.execute_parallel(tasks)
        assert len(results) == 2
        assert results["task1"]["result"] == "Executed sage-intacct:search_across_modules"
        assert results["task2"]["result"] == "Executed sage-intacct:get_financial_summary"
    
    @pytest.mark.asyncio
    async def test_phase5_response_processing(self):
        """Test Phase 5: Response processing and LLM integration."""
        # Test ResponseProcessor
        processor = ResponseProcessor()
        
        # Test data query formatting
        data_result = {
            "sales": 150000,
            "expenses": 75000,
            "profit": 75000,
            "currency": "USD"
        }
        
        response = processor.process_tool_result(
            data_result,
            "financial_summary",
            {"type": "data_query"}
        )
        
        assert "Financial Summary" in response
        assert "Sales: $150,000" in response
        assert "Profit: $75,000" in response
        
        # Test LLMService
        llm_service = LLMService()
        
        # Test pattern matching (no API key)
        analysis = await llm_service.analyze_request(
            "What's my current month sales?",
            ["sage-intacct:get_financial_summary", "sage-intacct:search_across_modules"]
        )
        
        assert analysis["tool"] == "sage-intacct:get_financial_summary"
        assert "current" in str(analysis["parameters"]).lower()
    
    @pytest.mark.asyncio
    async def test_end_to_end_flow(self, orchestrator):
        """Test complete end-to-end flow from user request to response."""
        # Mock all the components
        with patch.object(orchestrator.registry, 'list_all_tools') as mock_list_tools, \
             patch.object(orchestrator.registry, 'call_tool') as mock_call_tool, \
             patch.object(orchestrator, '_process_with_llm') as mock_process_llm:
            
            # Setup mocks
            mock_list_tools.return_value = [
                {
                    "name": "sage-intacct:get_financial_summary",
                    "description": "Get financial summary",
                    "parameters": []
                }
            ]
            
            mock_call_tool.return_value = {
                "sales": 250000,
                "expenses": 100000, 
                "profit": 150000,
                "period": "2024-01"
            }
            
            mock_process_llm.return_value = {
                "tool_calls": [{
                    "tool": "sage-intacct:get_financial_summary",
                    "parameters": {"start_date": "2024-01-01", "end_date": "2024-01-31"}
                }],
                "response": "Based on the financial data..."
            }
            
            # Execute request
            result = await orchestrator.process(
                message="What were my sales in January 2024?",
                context={}
            )
            
            # Verify flow
            assert result["success"] is True
            assert "financial data" in result["response"]
            mock_call_tool.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_tool_name_format_compatibility(self, orchestrator):
        """Test both tool name formats work correctly."""
        with patch.object(orchestrator.registry, 'call_tool') as mock_call_tool:
            mock_call_tool.return_value = {"result": "success"}
            
            # Test server:tool format
            result1 = await orchestrator._execute_tool_directly(
                "sage-intacct:health_check", 
                {}
            )
            assert result1["result"] == "success"
            
            # Test mcp__server__tool format
            result2 = await orchestrator._execute_tool_directly(
                "mcp__sage-intacct__health_check",
                {}
            )
            assert result2["result"] == "success"
            
            # Both should call the same underlying method
            assert mock_call_tool.call_count == 2
            mock_call_tool.assert_any_call("sage-intacct", "health_check", {})
    
    @pytest.mark.asyncio
    async def test_error_handling(self, orchestrator):
        """Test error handling throughout the stack."""
        # Test connection error
        with patch.object(orchestrator.registry, 'call_tool') as mock_call_tool:
            mock_call_tool.side_effect = Exception("Connection failed")
            
            result = await orchestrator._execute_tool_directly(
                "sage-intacct:test_tool",
                {}
            )
            
            assert "error" in result
            assert "Connection failed" in str(result["error"])
    
    @pytest.mark.asyncio 
    async def test_no_fastagent_imports(self):
        """Verify no FastAgent imports remain in key files."""
        files_to_check = [
            "agents/orchestrator.py",
            "services/mcp_registry.py",
            "services/mcp_client_wrapper.py"
        ]
        
        for file_path in files_to_check:
            full_path = f"/mnt/c/Users/<USER>/Documents/GitHub/ai-workspace-agents/{file_path}"
            try:
                with open(full_path, 'r') as f:
                    content = f.read()
                    assert 'fastagent' not in content.lower()
                    assert 'fast_agent' not in content.lower()
            except FileNotFoundError:
                # File might not exist in test environment
                pass


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])