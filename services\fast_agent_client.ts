/**
 * FastAgent Client - Frontend Integration Layer for TypeScript
 * 
 * This client provides the interface for the Next.js frontend to communicate
 * with the FastAPI agent service, replacing direct agent imports.
 */

export interface AgentMessage {
  message: string;
  context?: Record<string, any>;
  correlation_id?: string;
}

export interface AgentResponse {
  response: any;
  agent_id: string;
  timestamp: string;
  status: string;
  correlation_id?: string;
  error?: string;
  metadata?: Record<string, any>;
}

export interface AgentInfo {
  id: string;
  name: string;
  status: string;
  started_at?: string;
  last_activity?: string;
  error_count: number;
  total_requests: number;
  avg_response_time: number;
  capabilities: string[];
  metadata: Record<string, any>;
}

export interface HealthResponse {
  status: string;
  version: string;
  agents: any[];
  timestamp: string;
}

export class FastAgentClient {
  private baseUrl: string;
  private wsUrl: string;
  private timeout: number;
  private maxRetries: number;
  private ws: WebSocket | null = null;
  private wsMessageHandlers: Map<string, (response: AgentResponse) => void> = new Map();

  constructor(
    baseUrl: string = 'http://localhost:8000',
    wsUrl: string = 'ws://localhost:8000',
    timeout: number = 30000,
    maxRetries: number = 3
  ) {
    this.baseUrl = baseUrl.replace(/\/$/, '');
    this.wsUrl = wsUrl.replace(/\/$/, '');
    this.timeout = timeout;
    this.maxRetries = maxRetries;
  }

  // REST API Methods

  async healthCheck(): Promise<HealthResponse> {
    const response = await this.fetchWithRetry(`${this.baseUrl}/health`);
    return response.json();
  }

  async listAgents(): Promise<AgentInfo[]> {
    const response = await this.fetchWithRetry(`${this.baseUrl}/agents`);
    const data = await response.json();
    return data.agents;
  }

  async getAgentStatus(agentId: string): Promise<AgentInfo> {
    const response = await this.fetchWithRetry(
      `${this.baseUrl}/agents/${agentId}/status`
    );
    return response.json();
  }

  async resetAgent(agentId: string): Promise<{ status: string; message: string }> {
    const response = await this.fetchWithRetry(
      `${this.baseUrl}/agents/${agentId}/reset`,
      { method: 'POST' }
    );
    return response.json();
  }

  async getAgentHistory(
    agentId: string,
    limit: number = 50
  ): Promise<{ agent_id: string; history: any[]; total: number; limit: number }> {
    const response = await this.fetchWithRetry(
      `${this.baseUrl}/agents/${agentId}/history?limit=${limit}`
    );
    return response.json();
  }

  async sendMessage(
    agentId: string,
    message: string,
    context?: Record<string, any>,
    correlationId?: string
  ): Promise<AgentResponse> {
    const payload: AgentMessage = {
      message,
      context: context || {},
      correlation_id: correlationId,
    };

    const response = await this.fetchWithRetry(
      `${this.baseUrl}/agents/${agentId}/chat`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      }
    );

    return response.json();
  }

  // WebSocket Methods

  async connectWebSocket(): Promise<void> {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return; // Already connected
    }

    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(`${this.wsUrl}/agents/ws`);

      this.ws.onopen = () => {
        console.log('WebSocket connection established');
        resolve();
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        reject(error);
      };

      this.ws.onmessage = (event) => {
        try {
          const response = JSON.parse(event.data);
          
          if (response.type === 'error') {
            console.error('WebSocket error response:', response.error);
            return;
          }

          if (response.type === 'agent_response' && response.correlation_id) {
            const handler = this.wsMessageHandlers.get(response.correlation_id);
            if (handler) {
              handler(response);
              this.wsMessageHandlers.delete(response.correlation_id);
            }
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket connection closed');
        this.ws = null;
      };
    });
  }

  async disconnectWebSocket(): Promise<void> {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.close();
      this.ws = null;
    }
  }

  async sendMessageWS(
    agentId: string,
    message: string,
    context?: Record<string, any>,
    correlationId?: string
  ): Promise<AgentResponse> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      await this.connectWebSocket();
    }

    const actualCorrelationId = correlationId || this.generateCorrelationId();

    return new Promise((resolve, reject) => {
      // Set up response handler
      this.wsMessageHandlers.set(actualCorrelationId, (response) => {
        resolve({
          response: response.response,
          agent_id: response.agent_id,
          timestamp: response.timestamp,
          status: response.status,
          correlation_id: response.correlation_id,
          error: response.error,
          metadata: response.metadata || {},
        });
      });

      // Send message
      const payload = {
        agent_id: agentId,
        message,
        context: context || {},
        correlation_id: actualCorrelationId,
      };

      this.ws!.send(JSON.stringify(payload));

      // Timeout handler
      setTimeout(() => {
        if (this.wsMessageHandlers.has(actualCorrelationId)) {
          this.wsMessageHandlers.delete(actualCorrelationId);
          reject(new Error('WebSocket response timeout'));
        }
      }, this.timeout);
    });
  }

  // Helper Methods

  private async fetchWithRetry(
    url: string,
    options: RequestInit = {}
  ): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    const fetchOptions: RequestInit = {
      ...options,
      signal: controller.signal,
    };

    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      try {
        const response = await fetch(url, fetchOptions);
        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response;
      } catch (error) {
        clearTimeout(timeoutId);

        if (attempt < this.maxRetries - 1) {
          const waitTime = Math.pow(2, attempt) * 1000; // Exponential backoff
          console.warn(
            `Request failed (attempt ${attempt + 1}), retrying in ${waitTime}ms:`,
            error
          );
          await new Promise((resolve) => setTimeout(resolve, waitTime));
        } else {
          console.error('All retry attempts failed:', error);
          throw error;
        }
      }
    }

    throw new Error('Max retries exceeded');
  }

  private generateCorrelationId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Singleton instance for convenience
let clientInstance: FastAgentClient | null = null;

export function getAgentClient(): FastAgentClient {
  if (!clientInstance) {
    clientInstance = new FastAgentClient();
  }
  return clientInstance;
}

// Convenience functions

export async function chatWithAgent(
  agentId: string,
  message: string,
  context?: Record<string, any>
): Promise<AgentResponse> {
  const client = getAgentClient();
  return client.sendMessage(agentId, message, context);
}

export async function listAvailableAgents(): Promise<AgentInfo[]> {
  const client = getAgentClient();
  return client.listAgents();
}
