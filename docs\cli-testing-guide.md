# CLI Testing Guide for AI Workspace Agents

## Overview

The AI Workspace agents can be tested directly from the command line using the fast-agent framework. This guide explains the available options and setup requirements.

## Setup Requirements

### 1. LLM Provider Configuration

Create API keys for your chosen LLM provider(s):

**Option A: Using fastagent.secrets.yaml** (Recommended)
```yaml
# fastagent.secrets.yaml
openai:
  api_key: your-openai-key-here

anthropic:
  api_key: your-anthropic-key-here
```

**Option B: Using Environment Variables**
```bash
export OPENAI_API_KEY="your-openai-key-here"
export ANTHROPIC_API_KEY="your-anthropic-key-here"
```

### 2. MCP Server Setup

Ensure the Sage Intacct MCP server is running independently:
- The MCP server should already be running with its own credentials
- This agent service will connect to it automatically
- No MCP credentials needed in this project

## CLI Testing Methods

### Method 1: Direct Agent Execution (Fast-Agent Native)

Run agents directly using Python:

```bash
# Run GL agent interactively
python agents/intacct/gl_agent.py

# Specify a model
python agents/intacct/gl_agent.py --model gpt-4

# Send a single message
python agents/intacct/gl_agent.py --message "What's the current cash balance?"

# Quiet mode (only final response)
python agents/intacct/gl_agent.py --message "Get trial balance" --quiet
```

### Method 2: Custom CLI Test Script

Use the provided test script for easier testing:

```bash
# Interactive chat with GL agent
python test_agent_cli.py

# Interactive chat with AR agent
python test_agent_cli.py --agent ar

# Send single message
python test_agent_cli.py --message "List open invoices"

# Specify model
python test_agent_cli.py --model claude-3-opus
```

### Method 3: Fast-Agent Interactive Mode

If agents are properly decorated with `@fast.agent`, you can use:

```python
async with fast.run() as agent:
    await agent.interactive()  # Starts interactive chat
    # OR
    result = await agent("Your message here")  # Single message
```

## Available Models

Fast-agent supports both OpenAI and Anthropic models:

**OpenAI Models:**
- `gpt-4` - Most capable
- `gpt-3.5-turbo` - Faster, cheaper
- `o3-mini.low` - Optimized variant
- `o3-mini.high` - Higher quality variant

**Anthropic Models:**
- `claude-3-opus` - Most capable
- `claude-3-sonnet` / `sonnet` - Balanced
- `claude-3-haiku` / `haiku` - Fastest

## Interactive Commands

When in interactive mode:
- Type your message and press Enter
- Use `exit`, `quit`, or `bye` to end the session
- Use `@agent-name` to switch agents (in workflows)
- Use `/prompt` to apply MCP prompts

## Debugging Tips

1. **Check API Keys**: Ensure LLM provider keys are set
2. **Verify MCP Server**: Check if Intacct MCP server is accessible
3. **Enable Logging**: Set `LOG_LEVEL=DEBUG` for detailed output
4. **Test Tools**: Try simple queries first before complex operations

## Example Test Session

```bash
# 1. Set up environment
export OPENAI_API_KEY="sk-..."
# MCP server should already be running independently

# 2. Run GL agent test
python test_agent_cli.py --agent gl

# 3. Interactive session
You: What tools do you have available?
Agent: I have access to several GL tools including...

You: Show me the trial balance for this month
Agent: I'll retrieve the trial balance for you...

You: exit
Goodbye!
```

## Troubleshooting

**"No API key found" Error:**
- Check fastagent.secrets.yaml exists and has correct format
- Verify environment variables are set

**"MCP server not responding" Error:**
- Ensure the Sage Intacct MCP server is running independently
- Check if the MCP server path in fastagent.config.yaml is correct
- Verify the MCP server logs for any issues

**"Model not found" Error:**
- Verify model name is correct
- Check if API key has access to requested model

## Benefits of CLI Testing

1. **Rapid Development**: Test changes without restarting full API
2. **Isolated Testing**: Focus on single agent behavior
3. **Debugging**: Easier to trace issues without UI complexity
4. **Automation**: Can be scripted for regression testing
5. **Model Comparison**: Quickly test different LLMs
