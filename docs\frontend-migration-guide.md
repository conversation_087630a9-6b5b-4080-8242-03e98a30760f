# Frontend Migration Guide - Using FastAgent Client

This guide explains how to migrate the frontend from direct agent imports to using the FastAgent client for communication with the decoupled agent service.

## Overview

The migration involves replacing direct agent class imports and instantiations with API calls through the FastAgent client. This ensures proper separation between the frontend and agent logic.

## Migration Steps

### 1. Install Dependencies

No additional dependencies needed - the client uses native browser APIs (fetch and WebSocket).

### 2. Replace Agent Imports

**Before:**
```typescript
import { IntacctGLAgent } from '@/lib/agents/IntacctGLAgent';
import { IntacctARAgent } from '@/lib/agents/IntacctARAgent';

// Direct instantiation
const glAgent = new IntacctGLAgent();
const response = await glAgent.handleMessage(message, context);
```

**After:**
```typescript
import { FastAgentClient, chatWithAgent } from '@/services/fast_agent_client';

// API call to agent service
const response = await chatWithAgent('gl_agent', message, context);
```

### 3. Update Agent Usage Patterns

#### Simple Message Exchange

**Before:**
```typescript
const agent = new IntacctGLAgent();
const response = await agent.handleMessage(
  "What's the current GL balance?",
  { userId, conversationId, messageId, messages: [] }
);
```

**After:**
```typescript
const response = await chatWithAgent(
  'gl_agent',
  "What's the current GL balance?",
  { userId, conversationId, messageId }
);
```

#### Using the Client Instance

For more control, use the client instance directly:

```typescript
import { FastAgentClient } from '@/services/fast_agent_client';

const client = new FastAgentClient();

// List available agents
const agents = await client.listAgents();

// Check agent status
const status = await client.getAgentStatus('gl_agent');

// Send message with correlation ID
const response = await client.sendMessage(
  'ar_agent',
  'Show me overdue invoices',
  { customerId: '12345' },
  'corr-123'
);
```

#### WebSocket for Real-time Communication

```typescript
const client = new FastAgentClient();

// Connect WebSocket
await client.connectWebSocket();

// Send message via WebSocket
const response = await client.sendMessageWS(
  'gl_agent',
  'Generate trial balance',
  { period: '2024-Q1' }
);

// Disconnect when done
await client.disconnectWebSocket();
```

### 4. Update assistant-ui Integration

Replace direct agent usage in chat components:

**Before:**
```typescript
import { BaseAgent } from '@/lib/agents/BaseAgent';
import { IntacctGLAgent } from '@/lib/agents/IntacctGLAgent';

const agent = new IntacctGLAgent();
const response = await agent.handleMessage(message, context);
```

**After:**
```typescript
import { FastAgentAdapter } from '@/services/fast_agent_adapter';

// Create adapter for the agent
const adapter = new FastAgentAdapter({
  agentId: 'gl_agent',
  useWebSocket: true, // Enable real-time communication
  context: { userId, organizationId }
});

// Initialize adapter
await adapter.initialize();

// Send message
const response = await adapter.sendMessage(message, thread);

// Clean up when done
await adapter.cleanup();
```

### 5. React Component Example

```typescript
import React, { useState } from 'react';
import { useFastAgentAdapter } from '@/services/fast_agent_adapter';
import { Thread } from '@assistant-ui/react';

export function AgentChat({ agentId }: { agentId: string }) {
  const adapter = useFastAgentAdapter(agentId, {
    useWebSocket: true
  });
  
  const handleSendMessage = async (message: string) => {
    try {
      const response = await adapter.sendMessage(message, thread);
      // Handle response
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  // ... rest of component
}
```

### 6. Error Handling

The client includes built-in retry logic and error handling:

```typescript
try {
  const response = await chatWithAgent('gl_agent', message);
  // Handle successful response
} catch (error) {
  if (error.message.includes('404')) {
    console.error('Agent not found');
  } else if (error.message.includes('timeout')) {
    console.error('Request timed out');
  } else {
    console.error('Unexpected error:', error);
  }
}
```

### 7. Configuration

Update environment variables:

```bash
# .env.local
NEXT_PUBLIC_AGENT_SERVICE_URL=http://localhost:8000
NEXT_PUBLIC_AGENT_SERVICE_WS_URL=ws://localhost:8000
```

Use in the client:

```typescript
const client = new FastAgentClient(
  process.env.NEXT_PUBLIC_AGENT_SERVICE_URL,
  process.env.NEXT_PUBLIC_AGENT_SERVICE_WS_URL
);
```

## Agent ID Mapping

Map the old agent class names to their service IDs:

| Old Class Name | Agent Service ID |
|----------------|------------------|
| IntacctGLAgent | gl_agent |
| IntacctARAgent | ar_agent |
| IntacctAPAgent | ap_agent |
| AnalysisAgent | analysis_agent |
| ReportAgent | report_agent |
| ValidationAgent | validation_agent |
| OrchestratorAgent | orchestrator_agent |

## Benefits of Migration

1. **Decoupled Architecture**: Frontend no longer depends on agent implementation details
2. **Better Scalability**: Agent service can be scaled independently
3. **Language Agnostic**: Agents can be implemented in any language
4. **Improved Security**: Agent credentials stay on the server
5. **Real-time Support**: WebSocket enables streaming responses

## Troubleshooting

### Connection Issues

If the agent service is not reachable:
1. Ensure the agent service is running: `python services/api_service.py`
2. Check CORS settings allow your frontend origin
3. Verify the service URL in environment variables

### Agent Not Found

If you get a 404 error:
1. Check the agent ID matches the registered agents
2. Use `listAvailableAgents()` to see all agents
3. Ensure the agent is properly registered in the service

### WebSocket Connection Failed

1. Check if the WebSocket URL is correct (ws:// not http://)
2. Ensure the agent service has WebSocket support enabled
3. Check for proxy/firewall blocking WebSocket connections

## Complete Migration Checklist

- [ ] Replace all agent imports with FastAgent client imports
- [ ] Update all `agent.handleMessage()` calls to use the client
- [ ] Configure environment variables for service URLs
- [ ] Update error handling for network failures
- [ ] Test REST API communication
- [ ] Test WebSocket communication (if used)
- [ ] Update any agent-specific logic that was in the frontend
- [ ] Remove old agent files from the frontend codebase
- [ ] Update tests to mock API calls instead of agent classes
