# Fast-Agent Configuration File Example
# This file configures MCP servers and agent settings

# MCP Server Configurations
mcp_servers:
  # Sage Intacct MCP Server - Connects to independently running server
  sage-intacct:
    # The MCP server runs independently with its own credentials
    # This configuration just tells fast-agent how to connect to it
    command: cmd
    args: ["/c", "cd /d C:\\path\\to\\sage-intacct-mcp-server && python -m src.main"]
    env:
      # Only Python path needed - no credentials
      PYTHONPATH: "C:\\path\\to\\sage-intacct-mcp-server"
    
    health_check:
      enabled: true
      interval: 30
      timeout: 10

  # Sage Business Cloud Accounting - External Service Example
  "Sage Business Cloud Accounting":
    command: npx
    args: ["supergateway", "https://sbca-mcp.provenlabs.xyz/sse"]
    env: {}  # No credentials - handled by external service
    health_check:
      enabled: true
      interval: 30
      timeout: 10

# Agent Configuration
agents:
  # General Ledger Agent
  gl_agent:
    name: "General Ledger Agent"
    mcp_servers:
      - sage-intacct
    timeout: 30
    capabilities:
      - "GL account management"
      - "Journal entries"
      - "Trial balance reports"
      - "Financial statements"
