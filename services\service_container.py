"""
Service Container for dependency injection and proper initialization order.

Ensures services are initialized in the correct order to prevent state conflicts
and provide consistent access to shared resources.
"""

import structlog
from typing import Optional

from .mcp_registry import MCPServerRegistry
from .tool_discovery import ToolDiscoveryService
from .vector_db import VectorStoreFactory
from .embedding_service import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .intelligent_tool_filter import IntelligentToolFilter
from .enhanced_llm_service_with_filtering import EnhancedLLMServiceWithFiltering

logger = structlog.get_logger(__name__)


class ServiceContainer:
    """
    Service container that manages dependency injection and initialization order.
    
    Ensures services are initialized once and in the correct order to prevent
    state conflicts and provide consistent access to shared resources.
    """
    
    def __init__(self):
        # Service instances
        self._mcp_registry: Optional[MCPServerRegistry] = None
        self._tool_discovery: Optional[ToolDiscoveryService] = None
        self._vector_store = None
        self._embedder: Optional[ToolEmbedder] = None
        self._tool_filter: Optional[IntelligentToolFilter] = None
        self._enhanced_llm_service: Optional[EnhancedLLMServiceWithFiltering] = None
        
        # Initialization state
        self._initialized = False
        
        logger.info("ServiceContainer created")
    
    async def initialize(self) -> None:
        """Initialize all services in correct dependency order."""
        if self._initialized:
            logger.info("ServiceContainer already initialized")
            return
            
        logger.info("Initializing ServiceContainer with corrected architecture")
        
        try:
            # Phase 1: Initialize MCP registry first
            logger.info("Phase 1: Initializing MCP registry")
            self._mcp_registry = MCPServerRegistry()
            
            # Phase 2: Register MCP servers using dynamic config
            logger.info("Phase 2: Registering MCP servers via dynamic config")
            from config.dynamic_mcp_config import get_dynamic_mcp_config
            config = get_dynamic_mcp_config()
            config.set_registry(self._mcp_registry)
            await config.initialize_registry()
            
            # Phase 3: Initialize tool discovery with registry
            logger.info("Phase 3: Initializing tool discovery")
            self._tool_discovery = ToolDiscoveryService(self._mcp_registry)
            
            # Phase 4: Initialize corrected vector store (sqlite-vec)
            logger.info("Phase 4: Initializing corrected sqlite-vec store")
            self._vector_store = VectorStoreFactory.create({"backend": "sqlite"})
            await self._vector_store.initialize()
            
            # Phase 5: Initialize embedder
            logger.info("Phase 5: Initializing tool embedder")
            self._embedder = ToolEmbedder()
            
            # Phase 6: Initialize tool filter with corrected store
            logger.info("Phase 6: Initializing intelligent tool filter")
            self._tool_filter = IntelligentToolFilter(
                vector_store=self._vector_store,
                embedder=self._embedder
            )
            
            # Phase 7: Initialize enhanced LLM service with filtering
            logger.info("Phase 7: Initializing enhanced LLM service with filtering")
            self._enhanced_llm_service = EnhancedLLMServiceWithFiltering(
                tool_filter=self._tool_filter
            )
            
            self._initialized = True
            logger.info("ServiceContainer initialized successfully with corrected architecture")
            
        except Exception as e:
            logger.error("Failed to initialize ServiceContainer", error=str(e))
            self._initialized = False
            raise
    
    def _ensure_initialized(self):
        """Ensure the container is initialized."""
        if not self._initialized:
            raise RuntimeError("ServiceContainer not initialized - call initialize() first")
    
    @property
    def mcp_registry(self) -> MCPServerRegistry:
        """Get MCP server registry."""
        self._ensure_initialized()
        return self._mcp_registry
    
    @property
    def tool_discovery(self) -> ToolDiscoveryService:
        """Get tool discovery service."""
        self._ensure_initialized()
        return self._tool_discovery
    
    @property
    def vector_store(self):
        """Get vector store instance."""
        self._ensure_initialized()
        return self._vector_store
    
    @property
    def embedder(self) -> ToolEmbedder:
        """Get tool embedder."""
        self._ensure_initialized()
        return self._embedder
    
    @property
    def tool_filter(self) -> IntelligentToolFilter:
        """Get intelligent tool filter."""
        self._ensure_initialized()
        return self._tool_filter
    
    @property
    def enhanced_llm_service(self) -> EnhancedLLMServiceWithFiltering:
        """Get enhanced LLM service with filtering."""
        self._ensure_initialized()
        return self._enhanced_llm_service
    
    async def shutdown(self) -> None:
        """Shutdown all services."""
        logger.info("Shutting down ServiceContainer")
        
        try:
            # Shutdown in reverse order
            if self._enhanced_llm_service:
                # Enhanced LLM service doesn't need explicit shutdown
                pass
                
            if self._tool_filter:
                # Tool filter doesn't need explicit shutdown
                pass
                
            if self._embedder:
                # Embedder doesn't need explicit shutdown
                pass
                
            if self._vector_store:
                # Vector store connections will be closed automatically
                pass
                
            if self._tool_discovery:
                # Tool discovery doesn't need explicit shutdown
                pass
                
            if self._mcp_registry:
                # Registry will handle MCP server shutdowns
                pass
            
            self._initialized = False
            logger.info("ServiceContainer shutdown complete")
            
        except Exception as e:
            logger.error("Error during ServiceContainer shutdown", error=str(e))


# Global service container instance
_service_container: Optional[ServiceContainer] = None


def get_service_container() -> ServiceContainer:
    """Get the global service container instance."""
    global _service_container
    if _service_container is None:
        _service_container = ServiceContainer()
    return _service_container


async def initialize_services() -> ServiceContainer:
    """Initialize the global service container."""
    container = get_service_container()
    await container.initialize()
    return container


async def shutdown_services() -> None:
    """Shutdown the global service container."""
    global _service_container
    if _service_container and _service_container._initialized:
        await _service_container.shutdown()
    _service_container = None