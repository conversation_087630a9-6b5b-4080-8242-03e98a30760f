"""
Test script for MCP Registry functionality
"""

import asyncio
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.mcp_registry import MCPServerRegistry, MCPServerConfig


async def test_mcp_registry():
    """Test the MCP registry with Sage Intacct server."""
    print("Testing MCP Registry...")
    
    # Create registry instance
    registry = MCPServerRegistry(cache_dir=".cache/test_mcp")
    
    # Create Sage Intacct server config
    sage_config = MCPServerConfig(
        name="sage-intacct",
        command="uv",
        args=["run", str(Path("C:/Users/<USER>/Documents/GitHub/sage-intacct-mcp-server/intacct_server.py"))],
        env={
            "INTACCT_COMPANY_ID": "dummy",
            "INTACCT_USER_ID": "dummy", 
            "INTACCT_USER_PASSWORD": "dummy"
        },
        health_check={
            "enabled": True,
            "interval": 30,
            "timeout": 10
        },
        auto_discover=True
    )
    
    # Test 1: Register server
    print("\n1. Registering Sage Intacct server...")
    success = await registry.register_server(sage_config)
    print(f"   Registration: {'Success' if success else 'Failed'}")
    
    # Test 2: List servers
    print("\n2. Listing registered servers...")
    servers = await registry.list_servers()
    for server in servers:
        print(f"   - {server['name']}: {server['status']} ({server['tool_count']} tools)")
    
    # Test 3: Get all tools
    print("\n3. Getting all available tools...")
    tools = await registry.get_all_tools()
    for tool in tools:
        print(f"   - {tool.server_name}:{tool.name} - {tool.description}")
    
    # Test 4: Test tool mapping
    print("\n4. Testing tool mapping...")
    mapping = registry.get_tool_mapping()
    print("   Sample mappings:")
    for agent_tool, mcp_tool in list(mapping.items())[:5]:
        print(f"   - {agent_tool} -> {mcp_tool}")
    
    # Test 5: Get tool for agent method
    print("\n5. Testing agent method resolution...")
    test_methods = ["search", "get_financial_summary", "month_end_close", "create_invoice"]
    for method in test_methods:
        tool = await registry.get_tool_for_agent_method(method)
        if tool:
            print(f"   [OK] {method} -> {tool.server_name}:{tool.name}")
        else:
            print(f"   [NA] {method} -> Not available")
    
    # Test 6: Health report
    print("\n6. Getting health report...")
    health = await registry.health_report()
    print(f"   Overall health: {'Healthy' if health['healthy'] else 'Unhealthy'}")
    print(f"   Total servers: {health['total_servers']}")
    print(f"   Healthy servers: {health['healthy_servers']}")
    print(f"   Total tools: {health['total_tools']}")
    
    # Test 7: Refresh tools
    print("\n7. Refreshing tool discovery...")
    await registry.refresh_tools("sage-intacct")
    print("   Tool refresh completed")
    
    # Test 8: Unregister server
    print("\n8. Unregistering server...")
    success = await registry.unregister_server("sage-intacct")
    print(f"   Unregistration: {'Success' if success else 'Failed'}")
    
    print("\n[DONE] All tests completed!")


if __name__ == "__main__":
    asyncio.run(test_mcp_registry())
