"""
Orchestrator package for AI Workspace

This package contains the modular components of the orchestrator:
- intent_detector: Intent detection and analysis
- tool_executor: Tool execution and parallelization  
- response_handler: Response generation and formatting

Note: WorkflowManager deprecated - functionality moved to MCP client delegation
"""

from .intent_detector import IntentDetector
from .tool_executor import ToolExecutor
from .response_handler import ResponseHandler

__all__ = [
    'IntentDetector',
    'ToolExecutor',
    'ResponseHandler'
]