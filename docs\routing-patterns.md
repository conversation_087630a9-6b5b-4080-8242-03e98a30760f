# Agent Routing Patterns and Intent Detection

## Overview

This document captures the current routing logic and intent detection patterns used by each agent in the AI Workspace system. These patterns will be consolidated into the orchestrator's system prompt for dynamic routing.

---

## 1. GL Agent Routing Patterns

### Intent Categories
1. **Balance Inquiry** (`balance_inquiry`)
   - Keywords: `balance`, `how much`, `amount`
   - Pattern: Looks for account numbers (4+ digits)
   - Enhanced with: Current date context

2. **Journal Entry** (`journal_entry`)
   - Keywords: `journal`, `entry`, `debit`, `credit`
   - Context: Needs account validation and debit/credit balance

3. **Reconciliation** (`reconciliation`)
   - Keywords: `reconcile`, `reconciliation`
   - Context: Account-specific reconciliation workflows

4. **Analysis** (`analysis`)
   - Keywords: `analysis`, `budget`, `variance`, `trial balance`, `financial statement`
   - Context: Time-sensitive, requires current date

5. **Month-End Close** (`month_end`)
   - Keywords: `month-end`, `month end`, `close`, `closing`
   - Context: Period-specific, requires date context

### Special Methods
- `gather_financial_data(period)` - Used by orchestrator for comprehensive data collection

---

## 2. AR Agent Routing Patterns

### Intent Categories
1. **Customer Inquiry** (`customer_inquiry`)
   - Keywords: `customer` + (`balance`, `info`, `detail`, `account`)
   - Context: Include credit status and payment history

2. **Invoice Creation** (`invoice_creation`)
   - Keywords: `invoice` + (`create`, `new`, `generate`, `issue`)
   - Context: Current date, customer credit validation

3. **Credit Management** (`credit_management`)
   - Keywords: `credit limit`, `payment terms`, `credit hold`
   - Priority: Checked before payment application

4. **Payment Application** (`payment_application`)
   - Keywords: `payment`, `apply`, `cash receipt`, `check`
   - Context: Current date, invoice matching

5. **Collections** (`collections`)
   - Keywords: `collection`, `overdue`, `past due`, `aging`, `follow up`
   - Context: Diplomatic tone requirement, current date

6. **Credit Memo** (`credit_memo`)
   - Keywords: `credit` + (`memo`, `note`, `adjustment`)
   - Context: Customer account validation

### Special Methods
- `gather_customer_data(customer_id)` - Customer-specific data collection
- `run_collections_workflow(days_overdue)` - Automated collections process

---

## 3. AP Agent Routing Patterns

### Intent Categories
1. **Payment Batch** (`payment_batch`) - *Priority over general payment*
   - Keywords: `payment batch`, `payment run`, `pay vendors`, `create payments`
   - Context: Date, payment terms, discounts

2. **Invoice Processing** (`invoice_processing`)
   - Keywords: (`invoice`, `bill`) + (`process`, `enter`, `create`, `add`, `record`)
   - Context: Required fields collection, current date

3. **Vendor Inquiry** (`vendor_inquiry`)
   - Keywords: `vendor` + (`balance`, `info`, `detail`, `lookup`, `find`)
   - Context: Current date

4. **Vendor Aging** (`vendor_aging`)
   - Keywords: `aging`, `aged`, `overdue`, `payables report`
   - Context: Standard buckets (Current, 30, 60, 90+ days)

5. **PO Matching** (`po_matching`)
   - Keywords: `purchase order`, `po match`, `3-way match`, `three way match`
   - Context: Quantity/price verification

6. **Payment Approval** (`payment_approval`)
   - Keywords: `approval` + (`payment`, `invoice`, `bill`, `workflow`)
   - Context: Approval limits and hierarchies

7. **Cash Requirements** (`cash_requirements`)
   - Keywords: `cash requirement`, `cash need`, `payment forecast`
   - Context: Upcoming payments, available discounts

### Special Methods
- `gather_vendor_data(vendor_id)` - Vendor-specific or summary data
- `run_payment_workflow(criteria)` - Payment batch execution

---

## 4. Analysis Agent Routing Patterns

### Intent Categories
1. **Variance Analysis** (`variance_analysis`)
   - Keywords: `variance`, `budget`, `actual vs`
   - Parameters: 5% significance threshold
   - Focus: budget_vs_actual

2. **Ratio Calculation** (`ratio_calculation`)
   - Keywords: `ratio`, `liquidity`, `leverage`, `margin`
   - Types: liquidity, leverage, profitability ratios

3. **Trend Analysis** (`trend_analysis`)
   - Keywords: `trend`, `pattern`, `over time`, `historical`
   - Context: Historical data comparison

4. **Comprehensive Review** (`comprehensive_review`)
   - Keywords: `comprehensive`, `full analysis`, `complete review`, `financial analysis`, `financial review`
   - Scope: Full financial analysis

5. **Budget Performance** (`budget_performance`)
   - Keywords: `budget performance`, `budget vs actual`
   - Focus: Performance metrics

### Special Methods
- `perform_calculations(financial_data)` - Orchestrator integration for automated analysis

---

## 5. Report Agent Routing Patterns

### Intent Categories
1. **Month-End Report** (`month_end_report`) - *Priority check*
   - Keywords: `month` + `end`
   - Content: Comprehensive close package

2. **Executive Summary** (`executive_summary`)
   - Keywords: `executive`, `summary`, `overview`
   - Format: Concise with key insights

3. **Financial Statements** (`financial_statements`)
   - Keywords: `financial statement`, `p&l`, `balance sheet`, `cash flow`, `income statement`
   - Format: Standard financial formats

4. **Dashboard Report** (`dashboard_report`)
   - Keywords: `dashboard`, `metrics`, `kpi`
   - Content: Visual KPIs

5. **Custom Report** (`custom_report`)
   - Keywords: `custom`, `specific`
   - Requirements: User-defined

### Special Methods
- `generate_financial_report(data, format)` - Formatted report generation

---

## 6. Validation Agent Routing Patterns

### Intent Categories
1. **Period Check** (`period_check`)
   - Keywords: `period` + (`status`, `open`, `closed`)
   - Validation: Open/closed/locked status

2. **Data Integrity** (`data_integrity`)
   - Keywords: `integrity`, `data quality`, `consistency`
   - Checks: GL balance, subsidiary reconciliation

3. **Month-End Validation** (`month_end_validation`)
   - Keywords: `month` + `end`
   - Scope: All month-end validations

4. **Balance Validation** (`balance_validation`)
   - Keywords: `balance` + (`validation`, `check`, `verify`, `validate`)
   - Focus: Account balancing

5. **Compliance Check** (`compliance_check`)
   - Keywords: `compliance`, `audit`, `requirements`
   - Standards: Accounting standards verification

6. **Outstanding Transactions** (`outstanding_transactions`)
   - Keywords: `outstanding`, `pending`, `unposted`
   - Review: Unposted items

### Special Methods
- `validate_period_status(period)` - Period-specific validation

---

## 7. Orchestrator Routing Logic

### Current Implementation
The orchestrator uses fast-agent's built-in orchestration capabilities with:
- **Plan Type**: Iterative (up to 10 iterations)
- **Available Agents**: GL, AR, AP, Analysis, Report, Validation
- **Human Input**: Enabled for clarifications

### Common Workflows
1. **Month-End Close**
   - Sequence: Validation → GL → AR → AP → Analysis → Report → Validation
   - Coordination: Data flows between agents

2. **Financial Analysis**
   - Sequence: GL+AR+AP data gathering → Analysis → Report → Validation
   - Focus: Insights and recommendations

3. **Cash Flow Analysis**
   - Sequence: GL (balances) → AR (receivables) → AP (payables) → Analysis → Report
   - Output: Projections and recommendations

---

## Intent Categorization Schema

### Primary Categories
1. **Data Query** - Information retrieval (balances, customer info, vendor details)
2. **Transaction Processing** - Creating/modifying transactions (journal entries, invoices, payments)
3. **Analysis** - Calculations and insights (variance, ratios, trends)
4. **Reporting** - Formatted output generation (statements, summaries, dashboards)
5. **Validation** - Data integrity and compliance checks
6. **Workflow** - Multi-step processes (month-end, collections, payments)

### Intent Confidence Scoring
- **High Confidence (0.9)**: Explicit keywords match
- **Medium Confidence (0.7)**: Partial keyword match or context clues
- **Low Confidence (0.5)**: General inquiry, needs clarification

### Cross-Agent Patterns
1. **Date Context**: Most intents benefit from current date injection
2. **Period Specification**: Financial operations often need period context
3. **Entity Reference**: Customer/Vendor/Account IDs flow between agents
4. **Validation Requirements**: Most write operations need pre-validation
5. **Approval Workflows**: AP/AR operations often have approval steps

---

## Migration Strategy

### For Orchestrator System Prompt
1. Combine all intent patterns into comprehensive detection logic
2. Priority ordering: Specific intents before general ones
3. Context enhancement: Auto-inject relevant context based on intent
4. Tool selection: Map intents to appropriate MCP tools
5. Workflow detection: Identify multi-agent patterns

### Key Considerations
1. **Intent Overlap**: Some patterns exist in multiple agents (e.g., "analysis")
2. **Context Preservation**: Information must flow between tool calls
3. **Error Handling**: Graceful fallbacks for ambiguous intents
4. **User Guidance**: Suggest clarifications for low-confidence intents
