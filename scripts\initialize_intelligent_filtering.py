#!/usr/bin/env python3
"""Initialize the Intelligent Tool Filtering System.

This script sets up the vector database and indexes all available tools
from configured MCP servers for the first time.
"""
import asyncio
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.vector_db.factory import VectorStoreFactory
from services.embedding_service import ToolEmbedder
from services.tool_indexing import ToolIndexingPipeline
from services.tool_discovery import ToolDiscoveryService
from services.mcp_registry import MCPServerRegistry
from config.mcp_config import MCPConfiguration
import structlog

logger = structlog.get_logger()


async def initialize_system():
    """Initialize the intelligent tool filtering system."""
    print("=" * 60)
    print("INTELLIGENT TOOL FILTERING SYSTEM - INITIALIZATION")
    print("=" * 60)
    
    try:
        # 1. Initialize configuration
        print("\n1. Loading configuration...")
        config = MCPConfiguration()
        registry = MCPServerRegistry()
        servers = await registry.list_servers()
        print(f"   Found {len(servers)} MCP servers configured")
        for server in servers:
            print(f"   - {server}")
        
        # 2. Initialize vector database
        print("\n2. Initializing vector database...")
        db_config = {
            "backend": os.getenv("VECTOR_DB_BACKEND", "sqlite"),
            "db_path": os.getenv("VECTOR_DB_PATH", "data/ai_workspace_vectors.db")
        }
        
        # Create directory if needed
        db_dir = os.path.dirname(db_config["db_path"])
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
            print(f"   Created directory: {db_dir}")
        
        vector_store = VectorStoreFactory.create(db_config)
        await vector_store.initialize()
        
        health = await vector_store.health_check()
        if isinstance(health, dict):
            print(f"   Vector store status: {health.get('status', 'OK')}")
        else:
            print(f"   Vector store status: {'OK' if health else 'ERROR'}")
        print(f"   Backend: {db_config['backend']}")
        print(f"   Database: {db_config['db_path']}")
        
        # 3. Initialize embedding service
        print("\n3. Initializing embedding service...")
        embedder = ToolEmbedder()
        print(f"   Model: sentence-transformers/all-mpnet-base-v2")
        print(f"   Embedding dimension: 768")
        print(f"   Cache enabled: True")
        
        # 4. Initialize tool discovery
        print("\n4. Discovering tools from MCP servers...")
        discovery = ToolDiscoveryService(registry)
        
        # 5. Create indexing pipeline
        print("\n5. Creating tool indexing pipeline...")
        pipeline = ToolIndexingPipeline(vector_store, discovery, embedder)
        
        # 6. Index all tools
        print("\n6. Indexing tools (this may take a moment)...")
        stats = await pipeline.index_all_tools()
        
        print(f"\n✅ Indexing complete!")
        print(f"   Total tools discovered: {stats['total_tools']}")
        print(f"   Successfully indexed: {stats['indexed']}")
        print(f"   Failed: {stats['failed']}")
        
        if stats['servers']:
            print(f"\n   Tools per server:")
            for server, count in stats['servers'].items():
                print(f"   - {server}: {count} tools")
        
        # 7. Test the system
        print("\n7. Testing the system...")
        from services.intelligent_tool_filter import IntelligentToolFilter
        
        filter_service = IntelligentToolFilter(vector_store, embedder)
        
        # Get all tools for testing
        all_tools = await discovery.discover_all_tools()
        total_tools = sum(len(tools) for tools in all_tools.values())
        
        if total_tools > 0:
            # Test filtering
            test_query = "Get financial summary"
            filtered = await filter_service.get_relevant_tools(
                test_query,
                {"user_preferences": {}}
            )
            
            print(f"   Test query: '{test_query}'")
            print(f"   Filtered from {total_tools} to {len(filtered)} tools")
            print(f"   Token reduction: {(1 - len(filtered)/total_tools)*100:.1f}%")
            
            if filtered:
                print(f"\n   Top 3 filtered tools:")
                for i, tool in enumerate(filtered[:3]):
                    print(f"   {i+1}. {tool['name']}")
        
        # Close vector store
        await vector_store.close()
        
        print("\n" + "=" * 60)
        print("✅ INITIALIZATION COMPLETE")
        print("=" * 60)
        print("\nThe Intelligent Tool Filtering System is now ready to use!")
        print("\nKey features enabled:")
        print("- Semantic tool search with vector embeddings")
        print("- Automatic filtering to reduce token usage by >60%")
        print("- Hard limit of 20 tools per LLM request")
        print("- Context-aware tool selection")
        
        return True
        
    except Exception as e:
        logger.error("Initialization failed", error=str(e))
        print(f"\n❌ Error during initialization: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main entry point."""
    success = asyncio.run(initialize_system())
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()