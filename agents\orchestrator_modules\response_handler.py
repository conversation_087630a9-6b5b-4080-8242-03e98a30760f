"""
Response handling module for the orchestrator

Handles response generation, formatting, and off-topic detection
for user interactions.
"""
import logging
from typing import Dict, Any, Optional

from services.response_processor import ResponseProcessor

logger = logging.getLogger(__name__)


class ResponseHandler:
    """Handles response generation and formatting for the orchestrator"""
    
    def __init__(self, confidence_scorer=None):
        self.response_processor = ResponseProcessor()
        self.confidence_scorer = confidence_scorer
    
    def get_off_topic_acknowledgment(self, message: str) -> Optional[str]:
        """
        Check if message is about off-topic subjects and return acknowledgment
        
        Args:
            message: Lowercase user message
            
        Returns:
            Acknowledgment message or None if not off-topic
        """
        # Weather related
        if any(word in message for word in ['weather', 'temperature', 'forecast', 'rain', 'snow', 'sunny', 'cloudy']):
            return "I'm sorry, I can't help with weather information. I'm specialized in accounting and financial operations. I can help you with financial reports, account balances, or transaction searches."
        
        # Sports related
        if any(word in message for word in ['game', 'score', 'sports', 'football', 'basketball', 'soccer', 'baseball']):
            return "I don't have access to sports information. I focus on accounting and financial data. Is there anything related to your financial accounts I can help you with?"
        
        # News related
        if any(word in message for word in ['news', 'headlines', 'current events', 'politics']):
            return "I can't provide news or current events. However, I can help you with financial news from your accounting systems, like recent transactions or financial summaries."
        
        # Entertainment
        if any(word in message for word in ['movie', 'music', 'song', 'show', 'netflix', 'youtube']):
            return "I don't have information about entertainment. I'm designed to help with accounting tasks like generating reports, checking balances, or searching financial records."
        
        # Food/Restaurants
        if any(word in message for word in ['restaurant', 'food', 'recipe', 'cooking', 'lunch', 'dinner']):
            return "I can't help with food or restaurant recommendations. But I can help you track restaurant expenses or food-related transactions in your accounting system!"
        
        # General knowledge questions
        if any(phrase in message for phrase in ['what is', 'who is', 'where is', 'when was', 'how do i', 'tell me about']):
            # Check if it's not about accounting/financial topics
            accounting_terms = ['account', 'balance', 'revenue', 'expense', 'invoice', 'payment', 'transaction', 'report']
            if not any(term in message for term in accounting_terms):
                return "I understand you're looking for general information, but I'm specialized in accounting and financial operations. I can answer questions about your financial data, generate reports, or help with account management."
        
        return None
    
    async def generate_llm_response(self, message: str, context: Dict[str, Any]) -> str:
        """
        Generate a response using LLM when no tools are needed
        
        Args:
            message: User message
            context: Additional context
            
        Returns:
            Generated response
        """
        try:
            # Use the response processor's LLM capabilities
            system_prompt = """You are an AI assistant for accounting and financial operations. 
You have access to tools for Sage Intacct including financial summaries, searches, reports, and more.

When users ask questions:
1. If it's a greeting, respond warmly and explain your capabilities
2. If it's about accounting/finance but vague, ask for clarification with specific examples
3. If it's off-topic, politely redirect to accounting/finance topics
4. Always be helpful and professional

Available tools include:
- Financial summaries and reports
- Searching across GL, AR, AP modules  
- Account balances and transactions
- Month-end close procedures
- Consolidated reporting"""
            
            # Call LLM through response processor
            response = await self.response_processor._call_llm(
                system_prompt=system_prompt,
                user_prompt=message
            )
            return response.strip()
                
        except Exception as e:
            logger.error(f"Failed to generate LLM response: {str(e)}")
            # Fallback response
            return "I can help with accounting and financial operations. Please let me know what specific information you're looking for."
    
    def handle_low_confidence(self, message: str, intent_info: Dict[str, Any]) -> Dict[str, Any]:
        """Handle low confidence scenarios with clarification"""
        if not self.confidence_scorer:
            clarification = "I'm not sure I understood your request correctly. Could you please provide more details?"
        else:
            # Generate clarification prompt using confidence scorer
            clarification = self.confidence_scorer.generate_clarification_prompt(
                message,
                intent_info['all_intents'],
                intent_info['confidence']
            )
        
        return {
            "response": clarification,
            "metadata": {
                "intent": "clarification_needed",
                "confidence": intent_info['confidence'],
                "confidence_action": "clarify",
                "detected_intents": intent_info['all_intents'],
                "scoring_details": intent_info.get('scoring_details', {})
            }
        }
    
    def handle_medium_confidence(self, message: str, intent_info: Dict[str, Any]) -> Dict[str, Any]:
        """Handle medium confidence scenarios with confirmation"""
        if not self.confidence_scorer:
            confirmation = f"I think you want to {intent_info['primary_intent'].replace('_', ' ')}. Is that correct?"
        else:
            # Generate confirmation prompt
            confirmation = self.confidence_scorer.generate_clarification_prompt(
                message,
                intent_info['all_intents'],
                intent_info['confidence']
            )
        
        # Add execution readiness indicator
        confirmation += "\n\n✓ I'm ready to proceed once you confirm."
        
        return {
            "response": confirmation,
            "metadata": {
                "intent": intent_info['primary_intent'],
                "confidence": intent_info['confidence'],
                "confidence_action": "confirm_and_execute",
                "detected_intents": intent_info['all_intents'],
                "scoring_details": intent_info.get('scoring_details', {}),
                "ready_to_execute": True
            }
        }
    
    def format_workflow_response(self, workflow_name: str, workflow_result: Dict[str, Any]) -> str:
        """Format workflow execution response"""
        if workflow_result['status'] == 'completed':
            response = f"I've completed the {workflow_name.replace('_', ' ')} workflow successfully.\n\n"
            
            # Add summary of completed steps
            workflow_state = workflow_result['workflow']
            completed_steps = [
                step for step, result in workflow_state['results'].items()
                if result['status'] == 'completed'
            ]
            
            response += f"Completed {len(completed_steps)} steps:\n"
            for step in completed_steps:
                response += f"✓ {step.replace('_', ' ').title()}\n"
            
            # Add key results
            if workflow_result.get('final_results'):
                response += "\nKey Results:\n"
                for step_id, result in workflow_result['final_results'].items():
                    if isinstance(result, dict) and 'report_type' in result:
                        response += f"- Generated {result['report_type'].replace('_', ' ')}\n"
        else:
            response = f"The {workflow_name.replace('_', ' ')} workflow encountered an issue: {workflow_result.get('error', 'Unknown error')}"
        
        return response
    
    def format_parallel_response(self, parallel_result: Dict[str, Any]) -> str:
        """Format parallel execution response"""
        if parallel_result['success']:
            response_parts = ["I've completed the requested operations. Here are the results:\n"]
            
            # Add aggregated results
            if parallel_result.get('aggregated'):
                for category, data in parallel_result['aggregated'].items():
                    response_parts.append(f"\n{category.replace('_', ' ').title()}:")
                    if isinstance(data, dict):
                        for key, value in data.items():
                            response_parts.append(f"  - {key}: {value}")
                    else:
                        response_parts.append(f"  {data}")
            
            response = "\n".join(response_parts)
        else:
            # Handle partial failures
            response = "I encountered some issues while processing your request:\n"
            for error_id, error_msg in parallel_result['errors'].items():
                response += f"\n- {error_id}: {error_msg}"
            
            if parallel_result['results']:
                response += "\n\nHowever, I was able to complete some operations successfully."
        
        return response