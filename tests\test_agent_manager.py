"""
Tests for AgentManager
"""

import pytest
import asyncio
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock

from services.agent_manager import (
    AgentManager,
    AgentStatus,
    AgentMessage,
    AgentResponse,
    AgentInfo,
    get_agent_manager
)


@pytest.fixture
def agent_manager():
    """Create a fresh AgentManager instance."""
    return AgentManager()


@pytest.fixture
def mock_handler():
    """Create a mock async handler."""
    handler = AsyncMock()
    handler.return_value = AgentResponse(
        agent_id="test_agent",
        status="success",
        content="Test response"
    )
    return handler


class TestAgentManager:
    """Test cases for AgentManager."""
    
    @pytest.mark.asyncio
    async def test_register_agent(self, agent_manager, mock_handler):
        """Test agent registration."""
        agent_info = await agent_manager.register_agent(
            agent_id="test_agent",
            name="Test Agent",
            description="A test agent",
            handler=mock_handler,
            capabilities=["test", "demo"],
            metadata={"version": "1.0"}
        )
        
        assert agent_info.id == "test_agent"
        assert agent_info.name == "Test Agent"
        assert agent_info.status == AgentStatus.READY
        assert agent_info.capabilities == ["test", "demo"]
        assert agent_info.metadata["version"] == "1.0"
        assert agent_info.started_at is not None
    
    @pytest.mark.asyncio
    async def test_register_duplicate_agent(self, agent_manager, mock_handler):
        """Test registering duplicate agent raises error."""
        await agent_manager.register_agent(
            agent_id="test_agent",
            name="Test Agent",
            description="A test agent",
            handler=mock_handler
        )
        
        with pytest.raises(ValueError, match="already registered"):
            await agent_manager.register_agent(
                agent_id="test_agent",
                name="Test Agent 2",
                description="Another test agent",
                handler=mock_handler
            )
    
    @pytest.mark.asyncio
    async def test_unregister_agent(self, agent_manager, mock_handler):
        """Test agent unregistration."""
        await agent_manager.register_agent(
            agent_id="test_agent",
            name="Test Agent",
            description="A test agent",
            handler=mock_handler
        )
        
        result = await agent_manager.unregister_agent("test_agent")
        assert result is True
        
        # Verify agent is removed
        agent = await agent_manager.get_agent("test_agent")
        assert agent is None
    
    @pytest.mark.asyncio
    async def test_unregister_nonexistent_agent(self, agent_manager):
        """Test unregistering non-existent agent returns False."""
        result = await agent_manager.unregister_agent("nonexistent")
        assert result is False
    
    @pytest.mark.asyncio
    async def test_list_agents(self, agent_manager, mock_handler):
        """Test listing all agents."""
        # Register multiple agents
        for i in range(3):
            await agent_manager.register_agent(
                agent_id=f"agent_{i}",
                name=f"Agent {i}",
                description=f"Test agent {i}",
                handler=mock_handler
            )
        
        agents = await agent_manager.list_agents()
        assert len(agents) == 3
        assert all(isinstance(agent, AgentInfo) for agent in agents)
        assert {agent.id for agent in agents} == {"agent_0", "agent_1", "agent_2"}
    
    @pytest.mark.asyncio
    async def test_send_message_success(self, agent_manager, mock_handler):
        """Test successful message sending."""
        await agent_manager.register_agent(
            agent_id="test_agent",
            name="Test Agent",
            description="A test agent",
            handler=mock_handler
        )
        
        response = await agent_manager.send_message(
            agent_id="test_agent",
            content="Hello, agent!",
            message_type="chat",
            metadata={"user": "test"}
        )
        
        assert response.status == "success"
        assert response.content == "Test response"
        assert response.agent_id == "test_agent"
        assert response.error is None
        
        # Verify handler was called
        mock_handler.assert_called_once()
        call_args = mock_handler.call_args[0][0]
        assert isinstance(call_args, AgentMessage)
        assert call_args.content == "Hello, agent!"
    
    @pytest.mark.asyncio
    async def test_send_message_to_nonexistent_agent(self, agent_manager):
        """Test sending message to non-existent agent."""
        response = await agent_manager.send_message(
            agent_id="nonexistent",
            content="Hello"
        )
        
        assert response.status == "error"
        assert response.error == "Agent nonexistent not found"
        assert response.content is None
    
    @pytest.mark.asyncio
    async def test_send_message_timeout(self, agent_manager):
        """Test message timeout handling."""
        # Create handler that times out
        async def slow_handler(message):
            await asyncio.sleep(40)  # Longer than timeout
            return AgentResponse(agent_id="test_agent", content="Too late")
        
        await agent_manager.register_agent(
            agent_id="test_agent",
            name="Test Agent",
            description="A test agent",
            handler=slow_handler
        )
        
        # Set short timeout for test
        agent_manager._timeout_seconds = 0.1
        agent_manager._max_retries = 1
        
        response = await agent_manager.send_message(
            agent_id="test_agent",
            content="Hello"
        )
        
        assert response.status == "error"
        assert "timeout" in response.error.lower()
        
        # Check error count increased
        agent = await agent_manager.get_agent("test_agent")
        assert agent.error_count == 1
        assert agent.status == AgentStatus.ERROR
    
    @pytest.mark.asyncio
    async def test_send_message_error_handling(self, agent_manager):
        """Test error handling during message processing."""
        # Create handler that raises exception
        async def error_handler(message):
            raise ValueError("Test error")
        
        await agent_manager.register_agent(
            agent_id="test_agent",
            name="Test Agent",
            description="A test agent",
            handler=error_handler
        )
        
        response = await agent_manager.send_message(
            agent_id="test_agent",
            content="Hello"
        )
        
        assert response.status == "error"
        assert "Test error" in response.error
        
        # Check error count increased
        agent = await agent_manager.get_agent("test_agent")
        assert agent.error_count == 1
        assert agent.status == AgentStatus.ERROR
    
    @pytest.mark.asyncio
    async def test_get_agent_status(self, agent_manager, mock_handler):
        """Test getting agent status."""
        await agent_manager.register_agent(
            agent_id="test_agent",
            name="Test Agent",
            description="A test agent",
            handler=mock_handler,
            capabilities=["test"]
        )
        
        # Send a message to update activity
        await agent_manager.send_message("test_agent", "Hello")
        
        status = await agent_manager.get_agent_status("test_agent")
        
        assert status is not None
        assert status["id"] == "test_agent"
        assert status["name"] == "Test Agent"
        assert status["status"] == "ready"
        assert status["total_requests"] == 1
        assert status["error_count"] == 0
        assert status["capabilities"] == ["test"]
        assert status["last_activity"] is not None
    
    @pytest.mark.asyncio
    async def test_get_agent_status_nonexistent(self, agent_manager):
        """Test getting status of non-existent agent."""
        status = await agent_manager.get_agent_status("nonexistent")
        assert status is None
    
    @pytest.mark.asyncio
    async def test_reset_agent(self, agent_manager):
        """Test resetting agent state."""
        # Create handler that fails
        async def error_handler(message):
            raise ValueError("Test error")
        
        await agent_manager.register_agent(
            agent_id="test_agent",
            name="Test Agent",
            description="A test agent",
            handler=error_handler
        )
        
        # Cause some errors
        for _ in range(3):
            await agent_manager.send_message("test_agent", "Hello")
        
        agent = await agent_manager.get_agent("test_agent")
        assert agent.error_count == 3
        assert agent.status == AgentStatus.ERROR
        
        # Reset agent
        result = await agent_manager.reset_agent("test_agent")
        assert result is True
        
        agent = await agent_manager.get_agent("test_agent")
        assert agent.error_count == 0
        assert agent.status == AgentStatus.READY
        assert agent.avg_response_time == 0.0
    
    @pytest.mark.asyncio
    async def test_health_check(self, agent_manager, mock_handler):
        """Test health check functionality."""
        # Register multiple agents with different statuses
        await agent_manager.register_agent(
            agent_id="healthy_agent",
            name="Healthy Agent",
            description="A healthy agent",
            handler=mock_handler
        )
        
        # Create agent with errors
        async def error_handler(message):
            raise ValueError("Test error")
        
        await agent_manager.register_agent(
            agent_id="error_agent",
            name="Error Agent",
            description="An error agent",
            handler=error_handler
        )
        
        # Cause error in second agent
        await agent_manager.send_message("error_agent", "Hello")
        
        health = await agent_manager.health_check()
        
        assert health["total_agents"] == 2
        assert health["healthy_agents"] == 1
        assert health["error_agents"] == 1
        assert health["healthy"] is False
        assert len(health["agents"]) == 2
        
        # Find agents in health report
        agent_statuses = {a["id"]: a for a in health["agents"]}
        assert agent_statuses["healthy_agent"]["healthy"] is True
        assert agent_statuses["error_agent"]["healthy"] is False
    
    @pytest.mark.asyncio
    async def test_performance_tracking(self, agent_manager):
        """Test performance metrics tracking."""
        # Create handler with variable response times
        call_count = 0
        
        async def timed_handler(message):
            nonlocal call_count
            call_count += 1
            await asyncio.sleep(0.01 * call_count)  # Increasing delay
            return AgentResponse(agent_id="test_agent", content=f"Response {call_count}")
        
        await agent_manager.register_agent(
            agent_id="test_agent",
            name="Test Agent",
            description="A test agent",
            handler=timed_handler
        )
        
        # Send multiple messages
        for _ in range(5):
            await agent_manager.send_message("test_agent", "Hello")
        
        agent = await agent_manager.get_agent("test_agent")
        assert agent.total_requests == 5
        assert agent.avg_response_time > 0
        
        # Performance metrics should be recorded
        metrics = agent_manager._performance_metrics["test_agent"]
        assert len(metrics) == 5
        assert all(m > 0 for m in metrics)
    
    def test_get_agent_manager_singleton(self):
        """Test global agent manager singleton."""
        manager1 = get_agent_manager()
        manager2 = get_agent_manager()
        assert manager1 is manager2
