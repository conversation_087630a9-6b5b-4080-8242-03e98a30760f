"""
Factory for creating vector store instances
"""

from typing import Dict, Any
import structlog

from .base import VectorStore
from .sqlite_store import SqliteVecStore

logger = structlog.get_logger(__name__)


class VectorStoreFactory:
    """Factory to create appropriate vector store based on configuration."""
    
    @staticmethod
    def create(config: Dict[str, Any]) -> VectorStore:
        """
        Create a vector store instance based on configuration.
        
        Args:
            config: Configuration dictionary with backend and settings
            
        Returns:
            VectorStore instance
            
        Raises:
            ValueError: If backend is unknown or configuration is invalid
        """
        backend = config.get("backend", "sqlite")
        
        if backend == "sqlite":
            # Default - intelligent search from day one, no setup required
            # Provides semantic matching, relevance scoring, and pattern learning
            db_path = config.get("db_path", "data/ai_workspace_vectors.db")
            embedding_dimension = config.get("embedding_dimension")  # Auto-detect if None
            logger.info(
                "Creating SQLite vector store",
                db_path=db_path,
                embedding_dimension=embedding_dimension or "auto-detect",
                features="Full semantic search with zero configuration"
            )
            return SqliteVecStore(db_path=db_path, embedding_dimension=embedding_dimension)
            
        elif backend == "postgresql":
            # Enterprise option - user provides connection
            if not config.get("connection_string"):
                raise ValueError("PostgreSQL requires connection_string in config")
            
            # Import here to avoid dependency if not used
            try:
                from .pg_store import PgVectorStore
                logger.info(
                    "Creating PostgreSQL vector store",
                    features="Enterprise-grade scalability"
                )
                return PgVectorStore(
                    connection_string=config["connection_string"],
                    pool_size=config.get("pool_size", 10)
                )
            except ImportError:
                raise ValueError(
                    "PostgreSQL backend requires additional dependencies. "
                    "Install with: pip install asyncpg pgvector"
                )
        else:
            raise ValueError(f"Unknown vector store backend: {backend}")
    
    @staticmethod
    def get_default_config() -> Dict[str, Any]:
        """
        Get default configuration for vector store.
        
        Returns:
            Default configuration with SQLite backend
        """
        return {
            "backend": "sqlite",
            "db_path": "data/ai_workspace_vectors.db"
        }