"""
Intelligent Tool Filter

Provides always-intelligent tool filtering with semantic understanding
from day one. Uses vector search to find the most relevant tools
for any query without ever falling back to "dumb" mode.
"""

import asyncio
from typing import List, Dict, Any, Optional, Set
from datetime import datetime
import time
import structlog
import numpy as np

from .vector_db import VectorStore
from .embedding_service import <PERSON>lEmbedder
from .tool_discovery import UnifiedToolSchema, ToolCategory, ToolCapability
from .query_enhancer import QueryEnhancer
from .search_strategies import (
    SearchStrategyManager, SearchStage, SearchMetrics,
    SearchStageConfig, SearchStrategyProfile
)
from .search_metrics_tracker import SearchMetricsTracker

logger = structlog.get_logger(__name__)


class IntelligentToolFilter:
    """
    Always-intelligent tool filtering with semantic understanding from day one.
    
    Features:
    - Semantic search using embeddings (always enabled)
    - Progressive search strategies for comprehensive results
    - Context-aware filtering
    - Intelligent ranking based on multiple factors
    - NEVER sends all tools or falls back to non-intelligent mode
    """
    
    def __init__(
        self,
        vector_store: VectorStore,
        embedder: Optional[ToolEmbedder] = None,
        query_enhancer: Optional[QueryEnhancer] = None,
        max_tools: int = 20,
        search_strategy_manager: Optional[SearchStrategyManager] = None
    ):
        self.vector_store = vector_store
        self.embedder = embedder or ToolEmbedder()
        self.query_enhancer = query_enhancer or QueryEnhancer(vector_store, self.embedder)
        self.max_tools = max_tools
        self.strategy_manager = search_strategy_manager or SearchStrategyManager()
        self._initialized = False
        self._search_metrics_history: List[SearchMetrics] = []
        self.metrics_tracker = SearchMetricsTracker()
        
        logger.info(
            "IntelligentToolFilter initialized",
            max_tools=max_tools,
            active_strategy=self.strategy_manager.active_profile,
            features="Always intelligent, semantic search from day one"
        )
    
    async def _ensure_initialized(self):
        """Ensure the filter is ready to use."""
        if not self._initialized:
            await self.vector_store.initialize()
            self._initialized = True
    
    async def get_relevant_tools(
        self,
        query: str,
        context: Optional[Dict[str, Any]] = None,
        max_tools: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Get relevant tools using intelligent semantic strategies.
        
        ALWAYS uses embeddings and semantic understanding.
        NEVER falls back to "dumb" mode or sends all tools.
        
        Args:
            query: User query or request
            context: Additional context (conversation history, user preferences, etc.)
            max_tools: Override default max tools (default: 20)
            
        Returns:
            List of most relevant tools (always ≤ max_tools)
        """
        await self._ensure_initialized()
        
        max_tools = max_tools or self.max_tools
        context = context or {}
        
        logger.info(
            "Filtering tools intelligently",
            query_length=len(query),
            max_tools=max_tools
        )
        
        # Enhance query with context using QueryEnhancer
        enhanced_query = await self.query_enhancer.enhance_query(
            query=query,
            conversation_context=context.get("conversation_history", []),
            user_preferences=context.get("user_preferences", {})
        )
        
        # Execute progressive search with configurable strategies
        tools, metrics = await self._execute_progressive_search(
            query=enhanced_query,
            original_query=query,
            context=context,
            max_tools=max_tools
        )
        
        # Store metrics for analysis
        self._search_metrics_history.append(metrics)
        
        # Record metrics in persistent storage
        try:
            search_id = self.metrics_tracker.record_search(query, metrics)
            # Store search_id in context for potential feedback recording
            if context:
                context['_search_metric_id'] = search_id
        except Exception as e:
            logger.warning(f"Failed to record search metrics: {e}")
        
        # Apply dynamic threshold adjustment if enabled
        if self.strategy_manager.get_profile().enable_dynamic_adjustment:
            await self._apply_dynamic_adjustments(metrics)
        
        logger.info(
            f"Intelligent filtering complete",
            metrics=metrics.get_summary()
        )
        
        return tools
    
    
    async def _infer_task_type(self, query: str) -> Optional[str]:
        """
        Infer the type of task from the query using semantic understanding.
        Returns the primary action verb or intent.
        """
        # Extract the first word as it often indicates intent
        words = query.strip().split()
        if not words:
            return None
            
        # The first word is often the action verb
        first_word = words[0].lower()
        
        # Common action verbs that indicate task type
        # We keep a minimal set just for capability mapping
        action_mapping = {
            "search": "search", "find": "search", "get": "search", "list": "search",
            "create": "create", "add": "create", "new": "create", "generate": "create",
            "update": "update", "modify": "update", "edit": "update", "change": "update",
            "delete": "delete", "remove": "delete", "cancel": "delete",
            "analyze": "analyze", "calculate": "analyze", "compute": "analyze",
            "report": "report", "summarize": "report", "export": "report",
            "validate": "validate", "check": "validate", "verify": "validate",
            "execute": "execute", "run": "execute", "perform": "execute"
        }
        
        return action_mapping.get(first_word)
    
    async def _semantic_search(
        self,
        query: str,
        k: int = 20,
        threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        Perform semantic search using vector embeddings.
        """
        # Generate query embedding
        query_embedding = await self.embedder.encode(query)
        
        # Search vector store
        results = await self.vector_store.search(
            query_embedding=query_embedding,
            k=k,
            threshold=threshold
        )
        
        # Convert results to tool format
        tools = []
        for tool_id, similarity, metadata in results:
            tool = {
                "id": tool_id,
                "name": metadata.get("tool_name", ""),
                "server": metadata.get("server", ""),
                "description": metadata.get("description", ""),
                "category": metadata.get("category", "general"),
                "capabilities": metadata.get("capabilities", []),
                "parameters": metadata.get("parameters", {}),
                "keywords": metadata.get("keywords", []),
                "similarity_score": similarity,
                "execution_stats": metadata.get("execution_stats", {})
            }
            tools.append(tool)
        
        return tools
    
    async def _infer_categories(self, query: str) -> List[str]:
        """
        Infer relevant categories from the query using embeddings.
        Instead of hardcoded prompts, we learn from the actual tools in our database.
        """
        # Get query intent embedding
        query_embedding = await self.query_enhancer.get_query_intent_embedding(query)
        
        # Search for tools with similar intent
        similar_tools = await self.vector_store.search(
            query_embedding=query_embedding,
            k=30,
            threshold=0.5
        )
        
        # Extract categories from similar tools
        category_counts = {}
        for tool_id, similarity, metadata in similar_tools:
            category = metadata.get("category", "general")
            if category not in category_counts:
                category_counts[category] = 0
            # Weight by similarity
            category_counts[category] += similarity
        
        # Sort categories by weighted count
        sorted_categories = sorted(
            category_counts.items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        # Return top categories with significant weight
        threshold = 0.3 * max(category_counts.values()) if category_counts else 0
        return [cat for cat, weight in sorted_categories if weight > threshold][:5]
    
    async def _search_by_categories(
        self,
        categories: List[str],
        limit: int = 15
    ) -> List[Dict[str, Any]]:
        """
        Search for tools by inferred categories.
        """
        all_tools = []
        
        for category in categories:
            # Search with category filter
            # Note: Current vector store doesn't support filters, so we use category in query
            results = await self.vector_store.search(
                query_embedding=await self.embedder.encode(f"tools for {category}"),
                k=limit // len(categories) if categories else limit,
                threshold=0.5,
                # filters={"category": category}  # TODO: Add filter support to vector store
            )
            
            for tool_id, similarity, metadata in results:
                tool = {
                    "id": tool_id,
                    "name": metadata.get("tool_name", ""),
                    "server": metadata.get("server", ""),
                    "description": metadata.get("description", ""),
                    "category": metadata.get("category", "general"),
                    "capabilities": metadata.get("capabilities", []),
                    "parameters": metadata.get("parameters", {}),
                    "keywords": metadata.get("keywords", []),
                    "similarity_score": similarity * 0.8,  # Slightly lower weight
                    "execution_stats": metadata.get("execution_stats", {})
                }
                all_tools.append(tool)
        
        return all_tools
    
    async def _get_contextually_relevant_tools(
        self,
        context: Dict[str, Any],
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Get tools that are contextually relevant based on patterns and usage.
        """
        # Even with no execution history, we can use tool metadata quality
        
        # Search for well-documented, general-purpose tools
        quality_query = "comprehensive tool with examples and clear documentation"
        results = await self.vector_store.search(
            query_embedding=await self.embedder.encode(quality_query),
            k=limit,
            threshold=0.4  # Lower threshold for broad matches
        )
        
        tools = []
        for tool_id, similarity, metadata in results:
            # Boost tools with good metadata
            quality_score = 0.0
            
            # Has examples
            if metadata.get("examples"):
                quality_score += 0.2
            
            # Has comprehensive description
            if len(metadata.get("description", "")) > 50:
                quality_score += 0.1
            
            # Has multiple capabilities
            if len(metadata.get("capabilities", [])) > 1:
                quality_score += 0.1
            
            # Has been used successfully
            exec_stats = metadata.get("execution_stats", {})
            if exec_stats.get("success_rate", 0) > 0.8:
                quality_score += 0.2
            
            tool = {
                "id": tool_id,
                "name": metadata.get("tool_name", ""),
                "server": metadata.get("server", ""),
                "description": metadata.get("description", ""),
                "category": metadata.get("category", "general"),
                "capabilities": metadata.get("capabilities", []),
                "parameters": metadata.get("parameters", {}),
                "keywords": metadata.get("keywords", []),
                "similarity_score": similarity * 0.6 + quality_score,
                "execution_stats": exec_stats
            }
            tools.append(tool)
        
        return tools
    
    def _merge_unique(
        self,
        tools1: List[Dict[str, Any]],
        tools2: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Merge two tool lists, keeping unique tools."""
        seen_ids = {tool["id"] for tool in tools1}
        merged = tools1.copy()
        
        for tool in tools2:
            if tool["id"] not in seen_ids:
                merged.append(tool)
                seen_ids.add(tool["id"])
        
        return merged
    
    async def _rank_and_limit(
        self,
        tools: List[Dict[str, Any]],
        query: str,
        context: Dict[str, Any],
        max_tools: int
    ) -> List[Dict[str, Any]]:
        """
        Apply intelligent ranking and limit to max tools.
        
        Ranking factors:
        - Semantic similarity score
        - Execution success rate
        - Usage frequency
        - Recency of use
        - User preferences
        - Category/capability match
        """
        # Calculate composite scores
        scored_tools = []
        
        for tool in tools:
            score = 0.0
            
            # Base similarity score (40% weight)
            score += tool.get("similarity_score", 0) * 0.4
            
            # Execution stats (30% weight)
            exec_stats = tool.get("execution_stats", {})
            
            # Success rate
            success_rate = exec_stats.get("success_rate", 0.5)
            score += success_rate * 0.15
            
            # Usage frequency (normalized)
            usage_count = exec_stats.get("count", 0)
            if usage_count > 0:
                # Logarithmic scale to prevent extreme bias
                usage_score = min(np.log(usage_count + 1) / 10, 1.0)
                score += usage_score * 0.1
            
            # Recency (5% weight)
            last_used = exec_stats.get("last_used")
            if last_used:
                try:
                    last_used_date = datetime.fromisoformat(last_used)
                    days_ago = (datetime.now() - last_used_date).days
                    recency_score = max(0, 1 - (days_ago / 30))  # Decay over 30 days
                    score += recency_score * 0.05
                except:
                    pass
            
            # User preferences (10% weight)
            user_prefs = context.get("user_preferences", {})
            preferred_tools = user_prefs.get("preferred_tools", [])
            if tool["name"] in preferred_tools or tool["id"] in preferred_tools:
                score += 0.1
            
            # Category match bonus (10% weight)
            task_type = await self._infer_task_type(query)
            if task_type:
                # Map task types to capabilities
                capability_map = {
                    "search": ["search", "read"],
                    "create": ["create", "write"],
                    "update": ["update", "write"],
                    "delete": ["delete"],
                    "analyze": ["analyze", "read"],
                    "report": ["report", "read"],
                    "validate": ["validate", "read"],
                    "execute": ["execute", "write"]
                }
                
                required_capabilities = capability_map.get(task_type, [])
                tool_capabilities = tool.get("capabilities", [])
                
                matching_caps = sum(1 for cap in required_capabilities if cap in tool_capabilities)
                if required_capabilities:
                    score += (matching_caps / len(required_capabilities)) * 0.1
            
            scored_tools.append((score, tool))
        
        # Sort by score descending
        scored_tools.sort(key=lambda x: x[0], reverse=True)
        
        # Return top tools
        return [tool for score, tool in scored_tools[:max_tools]]
    
    async def _execute_progressive_search(
        self,
        query: str,
        original_query: str,
        context: Dict[str, Any],
        max_tools: int
    ) -> tuple[List[Dict[str, Any]], SearchMetrics]:
        """Execute progressive search using configured strategy."""
        profile = self.strategy_manager.get_profile()
        metrics = SearchMetrics(profile_used=profile.name)
        all_tools = []
        seen_ids = set()
        
        for stage_config in profile.get_enabled_stages():
            stage_start = time.time()
            
            # Skip if we already have enough results
            if len(all_tools) >= profile.max_total_results:
                break
            
            # Execute stage-specific search
            stage_tools = await self._execute_search_stage(
                stage_config=stage_config,
                query=query,
                original_query=original_query,
                context=context,
                seen_ids=seen_ids
            )
            
            # Apply weight modifier to similarity scores
            if stage_config.weight_modifier != 1.0:
                for tool in stage_tools:
                    tool["similarity_score"] *= stage_config.weight_modifier
            
            # Merge unique tools
            new_tools = []
            for tool in stage_tools:
                if tool["id"] not in seen_ids:
                    new_tools.append(tool)
                    seen_ids.add(tool["id"])
                    all_tools.append(tool)
            
            # Record metrics
            stage_time = time.time() - stage_start
            metrics.add_stage_result(stage_config.stage, len(new_tools), stage_time)
            
            logger.info(
                f"Completed search stage",
                stage=stage_config.stage.value,
                new_results=len(new_tools),
                total_results=len(all_tools),
                time_ms=int(stage_time * 1000)
            )
            
            # Check if we have enough results to stop
            if len(all_tools) >= stage_config.min_results:
                # We have enough results, but continue if the next stage might help
                if stage_config.stage == SearchStage.HIGH_PRECISION and len(all_tools) >= 10:
                    break
        
        # Rank and limit results
        ranked_tools = await self._rank_and_limit(
            all_tools, original_query, context, max_tools
        )
        
        metrics.total_results = len(ranked_tools)
        return ranked_tools, metrics
    
    async def _execute_search_stage(
        self,
        stage_config: SearchStageConfig,
        query: str,
        original_query: str,
        context: Dict[str, Any],
        seen_ids: set
    ) -> List[Dict[str, Any]]:
        """Execute a specific search stage based on configuration."""
        if stage_config.stage in (SearchStage.HIGH_PRECISION, SearchStage.RELAXED_PRECISION):
            # Semantic search stages
            return await self._semantic_search(
                query=query,
                k=stage_config.k,
                threshold=stage_config.threshold
            )
        
        elif stage_config.stage == SearchStage.CATEGORY_BASED:
            # Category-based search
            categories = await self._infer_categories(original_query)
            return await self._search_by_categories(
                categories=categories,
                limit=stage_config.k
            )
        
        elif stage_config.stage == SearchStage.CONTEXTUAL:
            # Contextual relevance search
            return await self._get_contextually_relevant_tools(
                context=context,
                limit=stage_config.k
            )
        
        elif stage_config.stage == SearchStage.BROAD_MATCH:
            # Broad match - use very relaxed semantic search
            return await self._semantic_search(
                query=query,
                k=stage_config.k,
                threshold=stage_config.threshold
            )
        
        else:
            logger.warning(f"Unknown search stage: {stage_config.stage}")
            return []
    
    async def _apply_dynamic_adjustments(self, metrics: SearchMetrics):
        """Apply dynamic threshold adjustments based on search quality."""
        # Analyze recent search quality
        recent_metrics = self._search_metrics_history[-10:]  # Last 10 searches
        
        adjustments = {}
        
        # If high precision frequently yields few results, lower threshold
        high_precision_results = [
            m.results_per_stage.get(SearchStage.HIGH_PRECISION, 0)
            for m in recent_metrics
            if SearchStage.HIGH_PRECISION in m.results_per_stage
        ]
        
        if high_precision_results:
            avg_results = sum(high_precision_results) / len(high_precision_results)
            if avg_results < 5:
                adjustments[SearchStage.HIGH_PRECISION] = -0.05
            elif avg_results > 15:
                adjustments[SearchStage.HIGH_PRECISION] = 0.02
        
        # If we frequently need category-based search, adjust relaxed threshold
        category_usage = sum(
            1 for m in recent_metrics
            if SearchStage.CATEGORY_BASED in m.results_per_stage
        )
        
        if category_usage > len(recent_metrics) * 0.7:
            adjustments[SearchStage.RELAXED_PRECISION] = -0.03
        
        # Apply adjustments
        if adjustments:
            self.strategy_manager.adjust_thresholds(
                self.strategy_manager.active_profile,
                adjustments
            )
            metrics.dynamic_adjustments = adjustments
    
    def get_search_metrics(self, last_n: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get search metrics history."""
        metrics = self._search_metrics_history
        if last_n:
            metrics = metrics[-last_n:]
        return [m.get_summary() for m in metrics]
    
    def set_search_strategy(self, profile_name: str):
        """Set the active search strategy profile."""
        self.strategy_manager.set_active_profile(profile_name)
        logger.info(f"Search strategy changed to: {profile_name}")
    
    def record_tool_selection_feedback(
        self,
        context: Dict[str, Any],
        tool_selected: str,
        was_successful: bool
    ):
        """Record feedback about tool selection for learning."""
        search_id = context.get('_search_metric_id')
        if search_id:
            try:
                self.metrics_tracker.record_feedback(
                    search_metric_id=search_id,
                    tool_selected=tool_selected,
                    was_successful=was_successful
                )
            except Exception as e:
                logger.warning(f"Failed to record feedback: {e}")
    
    def get_performance_report(self, hours: int = 24) -> Dict[str, Any]:
        """Get a performance report for the search system."""
        return self.metrics_tracker.get_stage_performance_report(hours)
    
    def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """Get recommendations for optimizing search performance."""
        return self.metrics_tracker.get_optimization_recommendations()