"""
Test suite for parallel tool execution functionality
"""
import asyncio
import pytest
from datetime import datetime, timezone
from unittest.mock import AsyncMock, MagicMock, patch

from services.parallel_executor import (
    ParallelToolExecutor, 
    ToolCall, 
    ExecutionStatus,
    ToolDependencyAnalyzer,
    ExecutionBatch
)


class TestToolDependencyAnalyzer:
    """Test the tool dependency analyzer"""
    
    def test_analyze_simple_tools(self):
        """Test analyzing tools with no dependencies"""
        analyzer = ToolDependencyAnalyzer()
        
        tool_calls = [
            {'tool_name': 'health_check', 'parameters': {}},
            {'tool_name': 'list_enabled_modules', 'parameters': {}}
        ]
        
        result = analyzer.analyze_dependencies(tool_calls)
        
        assert len(result) == 2
        assert all(len(call.dependencies) == 0 for call in result)
        assert result[0].tool_name == 'health_check'
        assert result[1].tool_name == 'list_enabled_modules'
    
    def test_analyze_data_dependencies(self):
        """Test analyzing tools with data dependencies"""
        analyzer = ToolDependencyAnalyzer()
        
        tool_calls = [
            {'tool_name': 'get_financial_summary', 'parameters': {'modules': ['GL']}, 'id': 'summary'},
            {'tool_name': 'generate_consolidated_report', 
             'parameters': {'modules': ['GL'], 'report_type': 'monthly'}, 'id': 'report'}
        ]
        
        result = analyzer.analyze_dependencies(tool_calls)
        
        assert len(result) == 2
        assert len(result[0].dependencies) == 0  # First tool has no dependencies
        assert 'summary' in result[1].dependencies  # Second depends on first
    
    def test_analyze_parameter_references(self):
        """Test analyzing tools with parameter references"""
        analyzer = ToolDependencyAnalyzer()
        
        tool_calls = [
            {'tool_name': 'search_across_modules', 'parameters': {'query': 'invoices'}, 'id': 'search'},
            {'tool_name': 'get_financial_summary', 
             'parameters': {'data': '{{search.results}}'}, 'id': 'summary'}
        ]
        
        result = analyzer.analyze_dependencies(tool_calls)
        
        assert len(result) == 2
        assert len(result[0].dependencies) == 0
        assert 'search' in result[1].dependencies
    
    def test_create_execution_batches(self):
        """Test creating execution batches from analyzed tools"""
        analyzer = ToolDependencyAnalyzer()
        
        # Create tools with dependencies
        tools = [
            ToolCall(id='a', tool_name='tool_a', parameters={}),
            ToolCall(id='b', tool_name='tool_b', parameters={}, dependencies={'a'}),
            ToolCall(id='c', tool_name='tool_c', parameters={}),
            ToolCall(id='d', tool_name='tool_d', parameters={}, dependencies={'b', 'c'})
        ]
        
        batches = analyzer.create_execution_batches(tools)
        
        assert len(batches) == 3
        # Batch 0: a and c (no dependencies)
        assert len(batches[0].tool_calls) == 2
        assert set(call.id for call in batches[0].tool_calls) == {'a', 'c'}
        
        # Batch 1: b (depends on a)
        assert len(batches[1].tool_calls) == 1
        assert batches[1].tool_calls[0].id == 'b'
        
        # Batch 2: d (depends on b and c)
        assert len(batches[2].tool_calls) == 1
        assert batches[2].tool_calls[0].id == 'd'


class TestParallelToolExecutor:
    """Test the parallel tool executor"""
    
    @pytest.mark.asyncio
    async def test_execute_single_tool(self):
        """Test executing a single tool"""
        # Mock tool executor
        mock_executor = AsyncMock(return_value={'result': 'success'})
        
        executor = ParallelToolExecutor(mock_executor)
        
        tool_calls = [
            {'tool_name': 'test_tool', 'parameters': {'param': 'value'}}
        ]
        
        result = await executor.execute_parallel(tool_calls)
        
        assert result['success'] is True
        assert len(result['results']) == 1
        assert 'call_0' in result['results']
        assert result['results']['call_0'] == {'result': 'success'}
        assert result['metadata']['successful'] == 1
        assert result['metadata']['failed'] == 0
        mock_executor.assert_called_once_with('test_tool', {'param': 'value'})
    
    @pytest.mark.asyncio
    async def test_execute_parallel_tools(self):
        """Test executing multiple tools in parallel"""
        call_count = 0
        call_order = []
        
        async def mock_executor(tool_name, params):
            nonlocal call_count, call_order
            call_count += 1
            call_order.append(tool_name)
            await asyncio.sleep(0.1)  # Simulate some work
            return {f'{tool_name}_result': f'data_{call_count}'}
        
        executor = ParallelToolExecutor(mock_executor)
        
        tool_calls = [
            {'tool_name': 'tool_1', 'parameters': {}},
            {'tool_name': 'tool_2', 'parameters': {}},
            {'tool_name': 'tool_3', 'parameters': {}}
        ]
        
        result = await executor.execute_parallel(tool_calls)
        
        assert result['success'] is True
        assert len(result['results']) == 3
        assert result['metadata']['successful'] == 3
        assert result['metadata']['batches'] == 1  # All independent, single batch
        
        # Verify all were called
        assert call_count == 3
        assert set(call_order) == {'tool_1', 'tool_2', 'tool_3'}
    
    @pytest.mark.asyncio
    async def test_execute_with_dependencies(self):
        """Test executing tools with dependencies"""
        execution_order = []
        
        async def mock_executor(tool_name, params):
            execution_order.append(tool_name)
            if tool_name == 'data_provider':
                return {'data': [1, 2, 3]}
            elif tool_name == 'data_consumer':
                # Should receive resolved parameter
                assert params.get('input_data') == [1, 2, 3]
                return {'processed': sum(params['input_data'])}
            return {}
        
        executor = ParallelToolExecutor(mock_executor)
        
        tool_calls = [
            {'tool_name': 'data_provider', 'parameters': {}, 'id': 'provider'},
            {'tool_name': 'data_consumer', 
             'parameters': {'input_data': '{{provider.data}}'}, 
             'id': 'consumer',
             'dependencies': ['provider']}
        ]
        
        result = await executor.execute_parallel(tool_calls)
        
        assert result['success'] is True
        assert len(result['results']) == 2
        assert result['results']['provider'] == {'data': [1, 2, 3]}
        assert result['results']['consumer'] == {'processed': 6}
        
        # Verify execution order
        assert execution_order == ['data_provider', 'data_consumer']
    
    @pytest.mark.asyncio
    async def test_partial_failure_handling(self):
        """Test handling partial failures"""
        async def mock_executor(tool_name, params):
            if tool_name == 'failing_tool':
                raise Exception("Tool failed")
            return {'result': f'{tool_name}_success'}
        
        executor = ParallelToolExecutor(mock_executor)
        
        tool_calls = [
            {'tool_name': 'good_tool_1', 'parameters': {}},
            {'tool_name': 'failing_tool', 'parameters': {}},
            {'tool_name': 'good_tool_2', 'parameters': {}}
        ]
        
        result = await executor.execute_parallel(tool_calls, allow_partial_failure=True)
        
        assert result['success'] is False
        assert len(result['results']) == 2
        assert len(result['errors']) == 1
        assert 'call_1' in result['errors']
        assert result['metadata']['successful'] == 2
        assert result['metadata']['failed'] == 1
    
    @pytest.mark.asyncio
    async def test_retry_logic(self):
        """Test retry logic for failed tools"""
        attempt_count = 0
        
        async def mock_executor(tool_name, params):
            nonlocal attempt_count
            attempt_count += 1
            if attempt_count < 2:
                raise Exception("Temporary failure")
            return {'result': 'success after retry'}
        
        executor = ParallelToolExecutor(mock_executor)
        
        tool_calls = [
            {'tool_name': 'retry_tool', 'parameters': {}}
        ]
        
        result = await executor.execute_parallel(tool_calls)
        
        assert result['success'] is True
        assert attempt_count == 2  # Failed once, succeeded on retry
        assert result['results']['call_0'] == {'result': 'success after retry'}
    
    @pytest.mark.asyncio
    async def test_result_aggregation(self):
        """Test intelligent result aggregation"""
        async def mock_executor(tool_name, params):
            if 'summary' in tool_name:
                return {'total': 100, 'count': 10}
            elif 'list' in tool_name:
                return ['item1', 'item2']
            return {}
        
        executor = ParallelToolExecutor(mock_executor)
        
        tool_calls = [
            {'tool_name': 'get_summary_1', 'parameters': {}},
            {'tool_name': 'get_summary_2', 'parameters': {}},
            {'tool_name': 'list_items_1', 'parameters': {}},
            {'tool_name': 'list_items_2', 'parameters': {}}
        ]
        
        result = await executor.execute_parallel(tool_calls)
        
        assert result['success'] is True
        
        # Check aggregation
        aggregated = result['aggregated']
        assert 'get_summary_1' in aggregated or 'get_summary_2' in aggregated
        assert 'list_items_1' in aggregated or 'list_items_2' in aggregated
    
    @pytest.mark.asyncio
    async def test_concurrency_limit(self):
        """Test that concurrency is limited"""
        concurrent_count = 0
        max_concurrent_seen = 0
        
        async def mock_executor(tool_name, params):
            nonlocal concurrent_count, max_concurrent_seen
            concurrent_count += 1
            max_concurrent_seen = max(max_concurrent_seen, concurrent_count)
            await asyncio.sleep(0.1)
            concurrent_count -= 1
            return {'result': 'done'}
        
        executor = ParallelToolExecutor(mock_executor)
        executor.max_concurrent = 3  # Set limit
        
        # Create more tools than the limit
        tool_calls = [
            {'tool_name': f'tool_{i}', 'parameters': {}} 
            for i in range(10)
        ]
        
        result = await executor.execute_parallel(tool_calls)
        
        assert result['success'] is True
        assert len(result['results']) == 10
        assert max_concurrent_seen <= executor.max_concurrent


@pytest.mark.asyncio
async def test_orchestrator_parallel_execution():
    """Test parallel execution through the orchestrator"""
    from agents.orchestrator import DynamicOrchestrator
    
    # Mock the registry and discovery
    mock_registry = MagicMock()
    mock_registry.list_servers = AsyncMock(return_value=[{'name': 'test-server'}])
    
    orchestrator = DynamicOrchestrator(registry=mock_registry)
    
    # Mock the fast agent
    orchestrator.fast_agent = MagicMock()
    orchestrator._initialized = True
    
    # Mock tool executor
    async def mock_tool_executor(tool_name, params):
        return {'tool': tool_name, 'status': 'completed'}
    
    orchestrator._execute_tool_via_agent = mock_tool_executor
    
    # Test parallel tool execution
    tool_calls = [
        {'tool_name': 'tool_1', 'parameters': {'param': 'value1'}},
        {'tool_name': 'tool_2', 'parameters': {'param': 'value2'}}
    ]
    
    result = await orchestrator.execute_parallel_tools(tool_calls)
    
    assert result['success'] is True
    assert len(result['results']) == 2
    assert result['metadata']['successful'] == 2
    assert result['metadata']['failed'] == 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])