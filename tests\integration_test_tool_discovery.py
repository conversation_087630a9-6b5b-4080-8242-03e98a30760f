"""
Integration test for Tool Discovery Service with MCP Registry
"""

import asyncio
from services import (
    get_mcp_registry, 
    get_tool_discovery_service,
    MCPServerConfig,
    ToolCategory,
    ToolCapability
)


async def test_integration():
    """Test the integration between MCP Registry and Tool Discovery Service."""
    print("Starting Tool Discovery Service Integration Test\n")
    
    # Get services
    registry = get_mcp_registry()
    discovery = get_tool_discovery_service()
    
    # Register Sage Intacct server
    print("1. Registering Sage Intacct MCP Server...")
    config = MCPServerConfig(
        name="sage-intacct",
        command="node",
        args=["sage-intacct-mcp-server/dist/index.js"],
        env={
            "INTACCT_COMPANY_ID": "test-company",
            "INTACCT_USER_ID": "test-user"
        }
    )
    
    success = await registry.register_server(config)
    print(f"   Registration {'successful' if success else 'failed'}")
    
    # List servers
    print("\n2. Listing registered servers:")
    servers = await registry.list_servers()
    for server in servers:
        print(f"   - {server['name']}: {server['status']} ({server['tool_count']} tools)")
    
    # Discover all tools
    print("\n3. Discovering all tools...")
    tools = await discovery.discover_all_tools()
    print(f"   Found {len(tools)} tools")
    
    # Get catalog statistics
    print("\n4. Tool Catalog Statistics:")
    stats = discovery.get_statistics()
    print(f"   Total tools: {stats['total_tools']}")
    print(f"   Servers: {stats['servers']}")
    print("   Categories:")
    for cat, count in stats['categories'].items():
        print(f"     - {cat}: {count}")
    print("   Capabilities:")
    for cap, count in stats['capabilities'].items():
        print(f"     - {cap}: {count}")
    
    # Search tools
    print("\n5. Searching for tools:")
    
    # Search by query
    print("   a) Search for 'financial':")
    results = await discovery.search_tools("financial")
    for tool in results[:3]:
        print(f"      - {tool.display_name} ({tool.full_name})")
    
    # Search by category
    print("\n   b) Search by category (REPORTING):")
    results = await discovery.search_tools("", category=ToolCategory.REPORTING)
    for tool in results:
        print(f"      - {tool.display_name}: {tool.description}")
    
    # Search by capability
    print("\n   c) Search by capability (SEARCH):")
    results = await discovery.search_tools("", capabilities=[ToolCapability.SEARCH])
    for tool in results:
        print(f"      - {tool.display_name}: {', '.join(c.value for c in tool.capabilities)}")
    
    # Get specific tool
    print("\n6. Getting specific tool:")
    tool = await discovery.get_tool_by_name("search_across_modules")
    if tool:
        print(f"   Tool: {tool.display_name}")
        print(f"   Description: {tool.description}")
        print(f"   Category: {tool.category.value}")
        print(f"   Capabilities: {', '.join(c.value for c in tool.capabilities)}")
        print(f"   Parameters: {list(tool.parameters.get('properties', {}).keys())}")
    
    # Get related tools
    print("\n7. Getting related tools for 'get_financial_summary':")
    related = await discovery.get_related_tools("get_financial_summary")
    for tool in related:
        print(f"   - {tool.display_name} (Category: {tool.category.value})")
    
    # Export catalog for LLM
    print("\n8. Exporting catalog for LLM consumption:")
    export = await discovery.export_catalog()
    print(f"   Exported {len(export)} tool definitions")
    if export:
        print("   Sample tool export:")
        tool_export = export[0]
        print(f"     Name: {tool_export['name']}")
        print(f"     Display: {tool_export['display_name']}")
        print(f"     Category: {tool_export['category']}")
        print(f"     Capabilities: {tool_export['capabilities']}")
    
    # Tool mapping for backward compatibility
    print("\n9. Tool mapping for agent compatibility:")
    mapping = registry.get_tool_mapping()
    print("   Agent method -> MCP tool mappings:")
    sample_mappings = [
        "get_financial_statements",
        "search",
        "health_check"
    ]
    for method in sample_mappings:
        mcp_tool = mapping.get(method, "Not mapped")
        print(f"     {method} -> {mcp_tool}")
    
    # Health report
    print("\n10. System health report:")
    health = await registry.health_report()
    print(f"    Overall health: {'Healthy' if health['healthy'] else 'Unhealthy'}")
    print(f"    Servers: {health['healthy_servers']}/{health['total_servers']}")
    print(f"    Total tools available: {health['total_tools']}")
    
    print("\n✅ Integration test completed successfully!")


if __name__ == "__main__":
    asyncio.run(test_integration())
