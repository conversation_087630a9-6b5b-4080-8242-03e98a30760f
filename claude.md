**Project Folder**:
`C:\Users\<USER>\Documents\GitHub\ai-workspace-agents`

**Decoupled From**:
`C:\Users\<USER>\Documents\GitHub\ai-workspace-web-app`

**Sage Intacct MCP Server**:
'C:\Users\<USER>\Documents\GitHub\sage-intacct-mcp-server'

---

### **Instructions**

1. **Start by reading `C:\Users\<USER>\Documents\GitHub\ai-workspace-agents\Progress.md`**

   * If it doesn’t exist, create it.
   * This will track your session-by-session notes and task progress.
   * Do not move on to a new sub task or task without stopping for review. Also update the progress.md file after every task or subtask

2. **Read and follow the task document**:

   * `C:\Users\<USER>\Documents\GitHub\ai-workspace-agents\docs\PRD\fix-vector-search-mcp-delegation-repair.md`.

3. **Stop after completing each task and request for a review.**

4. **Update**:

   * `Progress.md` with what was done.
   * `fix-vector-search-mcp-delegation-repair.md` to reflect the task status.
   * Use ✅ to mark completed tasks.

6. **Reference Tools and documentations**:

   * `fastmcp` (for mcp client sdk)
   * `assistant-ui` (for interface context)
   * `sqlite-vec Docs` (for SQLite extension for fast vector similarity search)

7. For any complex or unclear task, use the documentation tools and the web to gather additional insights.

8. **Important Notes**:

   * Do **not touch or modify the MCP server** code—it is prebuilt and includes its own authentication.
   * You are to **use the actual MCP server** (not a mock). Refer to the **Intacct MCP server** to locate available tools. The actual tools are not in the codebase. Look up the installed mcp servers to find intacct tools.
   * Tool names in the MCP server may differ from those in the codebase.
     → **Create a mapping layer** between agent tool names and actual MCP server tool names.
     → For tools not available yet, implement the placeholder and mark them as **not available**.

---

### **Code Structure Guidelines**

* Keep individual files between **200–300 lines** where practical.
* For larger components, **split logically** and use clear, purposeful filenames.

---

### **Developer Expectations**

You are a **senior full-stack developer** with deep experience in modern architectures. You own the implementation end-to-end.

* Think through each implementation before coding—consider **scalability, edge cases, and integration flow**.
* Use **Test-Driven Development (TDD)** when possible.
* Favor **clean, modular, and robust code**—keep it minimal but not fragile.
* Maintain clarity and maintainability across all layers of the project.
