"""
Temporary fix for token limit error - limits tools to 20 without vector database

This is a quick workaround until the Intelligent Tool Filtering System is properly initialized.
"""

def apply_temporary_fix():
    """Apply temporary fix to limit tools sent to LLM"""
    import os
    import sys
    
    # Add project root to path
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    # Patch the EnhancedLLMServiceWithFiltering to limit tools
    from services import enhanced_llm_service_with_filtering
    
    original_analyze = enhanced_llm_service_with_filtering.EnhancedLLMServiceWithFiltering.analyze_and_respond
    
    async def patched_analyze_and_respond(self, message, tool_catalog, context=None):
        """Temporarily limit tools to prevent token overflow"""
        # If catalog has more than 20 tools, take first 20
        if len(tool_catalog) > 20:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Temporarily limiting tools from {len(tool_catalog)} to 20 to prevent token overflow")
            
            # Convert to list and take first 20
            limited_catalog = {}
            for i, (key, value) in enumerate(tool_catalog.items()):
                if i >= 20:
                    break
                limited_catalog[key] = value
            
            tool_catalog = limited_catalog
        
        # Call original method with limited catalog
        return await original_analyze(self, message, tool_catalog, context)
    
    # Apply patch
    enhanced_llm_service_with_filtering.EnhancedLLMServiceWithFiltering.analyze_and_respond = patched_analyze_and_respond
    
    print("✅ Temporary token limit fix applied!")
    print("   - Tools will be limited to 20 per request")
    print("   - This prevents the 48k token overflow")
    print("\n⚠️  This is a temporary fix. For proper functionality:")
    print("   1. Install dependencies: pip install structlog sentence-transformers numpy sqlite-vec")
    print("   2. Run: python scripts/initialize_intelligent_filtering.py")


if __name__ == "__main__":
    apply_temporary_fix()
    print("\nYou can now run: python run.py")