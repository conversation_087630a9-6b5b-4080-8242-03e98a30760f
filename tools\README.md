# Tools Directory

This directory contains utility scripts and CLI tools for development and debugging.

## Available Tools

### agent_cli.py
Interactive CLI tool for testing agents directly using the fast-agent framework.
```bash
python tools/agent_cli.py
```

### check_connectivity.py
Quick script to verify MCP server connectivity.
```bash
python tools/check_connectivity.py
```

### verify_intacct_tools.py
Direct verification of Intacct MCP server tools (health check, module listing, etc.).
```bash
python tools/verify_intacct_tools.py
```

## Note
These are development tools, not tests. Actual tests are located in the `tests/` directory.