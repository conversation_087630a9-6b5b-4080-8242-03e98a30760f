"""
Test script for FastAgent Client

This script tests the FastAgent client functionality including:
- REST API communication
- WebSocket communication
- Error handling
- Agent listing and status
"""

import asyncio
import logging
from datetime import datetime
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.fast_agent_client import FastAgentClient, chat_with_agent, list_available_agents

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_rest_api():
    """Test REST API functionality"""
    logger.info("Testing REST API functionality...")
    
    async with FastAgentClient() as client:
        # Test health check
        logger.info("1. Testing health check...")
        try:
            health = await client.health_check()
            logger.info(f"   Health status: {health['status']}")
            logger.info(f"   Version: {health['version']}")
            logger.info(f"   Available agents: {len(health['agents'])}")
        except Exception as e:
            logger.error(f"   Health check failed: {e}")
        
        # Test listing agents
        logger.info("\n2. Testing agent listing...")
        try:
            agents = await client.list_agents()
            logger.info(f"   Found {len(agents)} agents:")
            for agent in agents:
                logger.info(f"     - {agent.id}: {agent.name} (status: {agent.status})")
        except Exception as e:
            logger.error(f"   Agent listing failed: {e}")
        
        # Test agent status
        if agents:
            logger.info("\n3. Testing agent status...")
            agent_id = agents[0].id
            try:
                status = await client.get_agent_status(agent_id)
                logger.info(f"   Agent {agent_id} status:")
                logger.info(f"     Status: {status.status}")
                logger.info(f"     Requests: {status.total_requests}")
                logger.info(f"     Avg Response Time: {status.avg_response_time:.2f}s")
            except Exception as e:
                logger.error(f"   Agent status failed: {e}")
        
        # Test sending a message
        logger.info("\n4. Testing message sending...")
        try:
            response = await client.send_message(
                "gl_agent",
                "What's the current cash balance?",
                {"test": True},
                f"test-{datetime.now().timestamp()}"
            )
            logger.info(f"   Response received:")
            logger.info(f"     Agent: {response.agent_id}")
            logger.info(f"     Status: {response.status}")
            logger.info(f"     Response: {response.response[:100]}...")
        except Exception as e:
            logger.error(f"   Message sending failed: {e}")


async def test_websocket():
    """Test WebSocket functionality"""
    logger.info("\n\nTesting WebSocket functionality...")
    
    client = FastAgentClient()
    
    try:
        # Connect WebSocket
        logger.info("1. Connecting WebSocket...")
        await client.connect_websocket()
        logger.info("   WebSocket connected successfully")
        
        # Send message via WebSocket
        logger.info("\n2. Sending message via WebSocket...")
        response = await client.send_message_ws(
            "ar_agent",
            "Show me the top 5 customers by revenue",
            {"test": True, "limit": 5}
        )
        logger.info(f"   Response received:")
        logger.info(f"     Agent: {response.agent_id}")
        logger.info(f"     Status: {response.status}")
        logger.info(f"     Response: {response.response[:100]}...")
        
    except Exception as e:
        logger.error(f"WebSocket test failed: {e}")
    finally:
        # Disconnect
        logger.info("\n3. Disconnecting WebSocket...")
        await client.disconnect_websocket()
        logger.info("   WebSocket disconnected")
        await client.disconnect()


async def test_convenience_functions():
    """Test convenience functions"""
    logger.info("\n\nTesting convenience functions...")
    
    # Test chat function
    logger.info("1. Testing chat_with_agent...")
    try:
        response = await chat_with_agent(
            "analysis_agent",
            "Calculate the gross margin for last quarter",
            {"period": "2024-Q4"}
        )
        logger.info(f"   Response: {response.response[:100]}...")
    except Exception as e:
        logger.error(f"   Chat function failed: {e}")
    
    # Test list agents function
    logger.info("\n2. Testing list_available_agents...")
    try:
        agents = await list_available_agents()
        logger.info(f"   Found {len(agents)} agents")
    except Exception as e:
        logger.error(f"   List agents function failed: {e}")


async def test_error_handling():
    """Test error handling"""
    logger.info("\n\nTesting error handling...")
    
    async with FastAgentClient(timeout=2.0, max_retries=2) as client:
        # Test invalid agent
        logger.info("1. Testing invalid agent...")
        try:
            await client.send_message("invalid_agent", "test")
            logger.error("   Expected error but got success!")
        except Exception as e:
            logger.info(f"   Got expected error: {e}")
        
        # Test connection error (assuming service might not be running)
        logger.info("\n2. Testing connection error...")
        bad_client = FastAgentClient(base_url="http://localhost:9999")
        try:
            await bad_client.connect()
            await bad_client.health_check()
            logger.error("   Expected connection error but got success!")
        except Exception as e:
            logger.info(f"   Got expected error: {type(e).__name__}")
        finally:
            await bad_client.disconnect()


async def main():
    """Run all tests"""
    logger.info("Starting FastAgent Client Tests\n" + "="*50)
    
    # Check if service is running
    try:
        async with FastAgentClient() as client:
            await client.health_check()
            logger.info("Agent service is running!\n")
    except Exception as e:
        logger.error(f"Agent service is not running: {e}")
        logger.error("Please start the service with: python services/api_service.py")
        return
    
    # Run tests
    await test_rest_api()
    await test_websocket()
    await test_convenience_functions()
    await test_error_handling()
    
    logger.info("\n" + "="*50)
    logger.info("Tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
