# Direct MCP Client Architecture

## Overview

The AI Workspace Agents system has been refactored to use direct MCP (Model Context Protocol) client connections instead of the FastAgent framework. This architecture provides better control, cleaner separation of concerns, and resolves the architectural conflicts that arose from embedding FastAgent as an application framework.

## Architecture Components

### 1. MCP Client Wrapper (`services/mcp_client_wrapper.py`)

The MCPClientWrapper provides a clean interface for connecting to MCP servers using the FastMCP library.

**Key Features:**
- Automatic transport detection (stdio, SSE, HTTP)
- Connection management with proper async context handling
- Tool discovery with caching
- Direct tool execution
- Health monitoring

**Transport Support:**
- **stdio**: For local process-based MCP servers
- **SSE**: For server-sent events streaming
- **HTTP**: For standard HTTP-based servers

### 2. MCP Server Registry (`services/mcp_registry.py`)

The registry manages multiple MCP server connections and provides a unified interface for tool discovery and execution.

**Key Features:**
- Dynamic server registration
- Connection lifecycle management
- Tool discovery aggregation
- Server health monitoring
- Tool name mapping support

### 3. Dynamic Orchestrator (`agents/orchestrator.py`)

The orchestrator coordinates tool execution without FastAgent, using direct MCP client connections.

**Key Features:**
- LLM-based tool selection
- Direct tool execution via MCP clients
- Parallel and workflow execution support
- Response processing integration

### 4. Response Processing (`services/response_processor.py`)

Handles formatting of tool results for user consumption.

**Key Features:**
- Response type detection
- Data formatting with highlights
- Action confirmations
- Error handling and reporting

### 5. LLM Service (`services/llm_service.py`)

Analyzes user requests to determine appropriate tool calls.

**Key Features:**
- Request analysis with context
- Tool selection and parameter extraction
- Pattern-matching fallback
- OpenAI API integration (optional)

## Data Flow

```
User Request
    ↓
Orchestrator
    ↓
LLM Service (analyzes request)
    ↓
Tool Selection & Parameters
    ↓
MCP Registry (routes to server)
    ↓
MCP Client Wrapper (executes tool)
    ↓
MCP Server (returns data)
    ↓
Response Processor (formats result)
    ↓
User Response
```

## Tool Naming Conventions

The system supports two tool naming formats:

1. **Colon format**: `server:tool_name`
   - Example: `sage-intacct:get_financial_summary`

2. **MCP format**: `mcp__server__tool_name`
   - Example: `mcp__sage-intacct__get_financial_summary`

Both formats are automatically recognized and properly routed.

## Configuration

### MCP Server Configuration

Servers are configured in `fastagent.config.yaml`:

```yaml
servers:
  sage-intacct:
    command: node
    args: ["path/to/server.js"]
    env:
      INTACCT_API_KEY: ${INTACCT_API_KEY}
    tools:
      get_financial_summary:
        description: "Get financial summary data"
        input_schema:
          type: object
          properties:
            start_date: {type: string}
            end_date: {type: string}
```

### Environment Variables

Required environment variables should be set in your `.env` file or system environment:

```bash
INTACCT_API_KEY=your_api_key
OPENAI_API_KEY=your_openai_key  # Optional, for LLM tool selection
```

## Error Handling

The architecture includes comprehensive error handling at each layer:

1. **Connection Errors**: Handled by MCPClientWrapper with retry logic
2. **Tool Execution Errors**: Caught and formatted by the orchestrator
3. **LLM Errors**: Falls back to pattern matching when API unavailable
4. **Response Errors**: Gracefully formatted for user feedback

## Performance Considerations

1. **Connection Pooling**: MCP clients are reused across requests
2. **Tool Caching**: Discovered tools are cached per server
3. **Async Execution**: All operations use async/await for efficiency
4. **Parallel Execution**: Multiple tools can run concurrently

## Testing

The system includes comprehensive test coverage:

- **Unit Tests**: For individual components (MCPClientWrapper, ResponseProcessor, etc.)
- **Integration Tests**: For component interactions
- **End-to-End Tests**: For complete request flows

Run tests with:
```bash
pytest tests/test_mcp_migration_integration.py -v
pytest tests/test_mcp_client_wrapper.py -v
```

## Migration from FastAgent

If you're migrating from the FastAgent-based system:

1. **Remove FastAgent imports**: No longer needed in orchestrator
2. **Update tool execution**: Use `registry.call_tool()` instead of FastAgent context
3. **Configuration**: Keep server definitions but remove FastAgent-specific settings
4. **Testing**: Update tests to mock MCP clients instead of FastAgent

## Benefits of Direct MCP Architecture

1. **Clean Separation**: Orchestrator controls flow, MCP handles execution
2. **Better Integration**: No conflicts with API server or configuration
3. **Flexibility**: Dynamic server discovery and configuration
4. **Maintainability**: Clear component boundaries and responsibilities
5. **Performance**: Direct execution without framework overhead

## Future Enhancements

1. **WebSocket Support**: Add WebSocket transport for real-time updates
2. **Connection Resilience**: Implement automatic reconnection strategies
3. **Tool Composition**: Build complex tools from simpler ones
4. **Caching Layer**: Add result caching for expensive operations
5. **Metrics & Monitoring**: Add performance tracking and alerts