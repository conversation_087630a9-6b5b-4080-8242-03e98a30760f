# FastAgent to Direct MCP Client Migration Guide

This guide helps developers migrate from the FastAgent-based implementation to the direct MCP client architecture.

## Why Migrate?

FastAgent is designed as a standalone application framework, not as an embedded library. The migration resolves:
- Architectural conflicts with API servers
- Tool execution failures
- Configuration conflicts
- Dynamic agent creation issues

## Migration Overview

### Before (FastAgent)
```python
from mcp_agent.core.fastagent import FastAgent

class DynamicOrchestrator:
    def __init__(self):
        self.fast_agent = FastAgent("orchestrator", parse_cli_args=False)
    
    @self.fast_agent.agent("orchestrator", "instruction", servers=["mcp_server"])
    async def orchestrate():
        # Tool execution happens inside FastAgent context
        pass
```

### After (Direct MCP)
```python
from services.mcp_client_wrapper import MCPClientWrapper
from services.mcp_registry import MCPServerRegistry

class DynamicOrchestrator:
    def __init__(self, registry: MCPServerRegistry):
        self.registry = registry
    
    async def _execute_tool_directly(self, tool_name: str, params: dict):
        server_name, tool = self._parse_tool_name(tool_name)
        return await self.registry.call_tool(server_name, tool, params)
```

## Step-by-Step Migration

### Step 1: Update Dependencies

Remove FastAgent and add FastMCP:

```bash
# Remove
uv pip uninstall mcp-agent

# Add
uv pip install fastmcp
```

### Step 2: Remove FastAgent Imports

Replace all FastAgent imports:

```python
# Remove
from mcp_agent.core.fastagent import FastAgent, FastAgentClient

# Add
from services.mcp_client_wrapper import MCPClientWrapper
from services.mcp_registry import MCPServerRegistry
```

### Step 3: Update Orchestrator

#### Remove FastAgent initialization:
```python
# Remove
self.fast_agent = FastAgent("orchestrator", parse_cli_args=False)

# Add
self.registry = registry  # MCPServerRegistry passed in
```

#### Remove agent decorators:
```python
# Remove
@self.fast_agent.agent("orchestrator", system_prompt, servers=servers)
async def orchestrate():
    pass

# No decorators needed with direct MCP
```

#### Update tool execution:
```python
# Before (FastAgent)
async with self.fast_agent.run() as agent:
    result = await agent.call_tool("server", "tool_name", params)

# After (Direct MCP)
result = await self.registry.call_tool("server", "tool_name", params)
```

### Step 4: Update Configuration

The server configuration remains similar but remove FastAgent-specific settings:

```yaml
# fastagent.config.yaml
servers:
  sage-intacct:
    command: node
    args: ["path/to/server.js"]
    env:
      API_KEY: ${API_KEY}
    # Remove any FastAgent-specific config
```

### Step 5: Update Tool Discovery

```python
# Before
tools = await agent.list_tools()

# After
tools = await self.registry.list_all_tools()
```

### Step 6: Handle Tool Name Formats

The system supports both formats:
- `server:tool_name`
- `mcp__server__tool_name`

Add parsing logic:
```python
def _parse_tool_name(self, tool_name: str) -> tuple[str, str]:
    if tool_name.startswith("mcp__") and "__" in tool_name[5:]:
        parts = tool_name.split("__")
        return parts[1], parts[2]
    elif ":" in tool_name:
        return tool_name.split(":", 1)
    else:
        raise ValueError(f"Invalid tool name format: {tool_name}")
```

### Step 7: Update Tests

#### Mock MCP clients instead of FastAgent:
```python
# Before
with patch('mcp_agent.core.fastagent.FastAgent') as mock_agent:
    pass

# After
with patch('services.mcp_client_wrapper.MCPClientWrapper') as mock_wrapper:
    pass
```

#### Update test assertions:
```python
# Before
mock_agent.call_tool.assert_called_with("server", "tool", params)

# After
mock_registry.call_tool.assert_called_with("server", "tool", params)
```

## Common Issues and Solutions

### Issue 1: "Client is not connected"
**Solution**: Ensure the MCP client is connected before use:
```python
await registry._start_server("server_name")
```

### Issue 2: Tool not found
**Solution**: Check tool discovery and mapping:
```python
tools = await registry.list_all_tools()
print(f"Available tools: {[t['name'] for t in tools]}")
```

### Issue 3: Async context errors
**Solution**: Use proper async context managers:
```python
# Each operation uses its own context
async with client as ctx:
    result = await ctx.call_tool(tool_name, params)
```

## Testing the Migration

1. **Unit Tests**: Run component tests
   ```bash
   pytest tests/test_mcp_client_wrapper.py -v
   ```

2. **Integration Tests**: Test full flow
   ```bash
   pytest tests/test_mcp_migration_integration.py -v
   ```

3. **Manual Testing**: Test with real MCP server
   ```python
   # Test script
   from services.mcp_registry import MCPServerRegistry
   
   registry = MCPServerRegistry()
   await registry.initialize()
   
   # List tools
   tools = await registry.list_all_tools()
   print(f"Found {len(tools)} tools")
   
   # Execute a tool
   result = await registry.call_tool("sage-intacct", "health_check", {})
   print(f"Result: {result}")
   ```

## Performance Improvements

The direct MCP architecture provides:

1. **Faster startup**: No FastAgent initialization overhead
2. **Better concurrency**: Direct async/await without framework layers
3. **Connection pooling**: Reuse MCP client connections
4. **Tool caching**: Discovered tools are cached

## Rollback Plan

If you need to rollback:

1. Keep the old FastAgent code in a branch
2. Configuration files are compatible (just add FastAgent settings back)
3. Tests can be reverted to FastAgent mocks

## Getting Help

- Check `docs/direct-mcp-client-architecture.md` for architecture details
- Review test files for implementation examples
- Check Progress.md for migration history

## Checklist

- [ ] Remove FastAgent dependencies
- [ ] Update imports in all files
- [ ] Refactor orchestrator to use MCPServerRegistry
- [ ] Update tool execution calls
- [ ] Update configuration files
- [ ] Update all tests
- [ ] Test with real MCP servers
- [ ] Update documentation
- [ ] Clean up unused code