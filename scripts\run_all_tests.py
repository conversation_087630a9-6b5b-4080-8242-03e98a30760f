import subprocess
import sys
import os

# Change to project directory
os.chdir(r"C:\Users\<USER>\Documents\GitHub\ai-workspace-agents")

# Run all tests
print("Running all tests from project root...")
print(f"Current directory: {os.getcwd()}")
print("Running: python -m pytest -v")

result = subprocess.run([sys.executable, "-m", "pytest", "-v", "--tb=short"], 
                       capture_output=True, text=True)
                       
print("STDOUT:")
print(result.stdout)
print("\nSTDERR:")
print(result.stderr)
print(f"\nReturn code: {result.returncode}")
