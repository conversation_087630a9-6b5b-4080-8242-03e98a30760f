"""
Dynamic MCP Server Registry

Provides runtime registration, discovery, and health monitoring for MCP servers.
Auto-discovers tools from registered servers and caches their schemas.
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
import structlog
from pydantic import BaseModel, Field
import hashlib
import os
from pathlib import Path

from .mcp_client_wrapper import MCPClientWrapper

logger = structlog.get_logger(__name__)


class ServerStatus(str, Enum):
    """MCP server status enumeration."""
    INITIALIZING = "initializing"
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    ERROR = "error"
    UNHEALTHY = "unhealthy"


class ToolSchema(BaseModel):
    """Schema for an MCP tool."""
    name: str
    description: str
    parameters: Dict[str, Any]
    server_name: str
    last_updated: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    def get_cache_key(self) -> str:
        """Generate cache key for this tool schema."""
        # Use server_name:tool_name as the key to avoid hash collisions
        # This ensures tools from different servers never overwrite each other
        return f"{self.server_name}:{self.name}"


class MCPServerConfig(BaseModel):
    """Configuration for an MCP server."""
    name: str
    command: str
    args: List[str] = Field(default_factory=list)
    env: Dict[str, str] = Field(default_factory=dict)
    health_check: Dict[str, Any] = Field(default_factory=lambda: {
        "enabled": True,
        "interval": 30,
        "timeout": 10
    })
    auto_discover: bool = True
    metadata: Dict[str, Any] = Field(default_factory=dict)


@dataclass
class MCPServerInfo:
    """Runtime information about an MCP server."""
    config: MCPServerConfig
    status: ServerStatus = ServerStatus.INITIALIZING
    connected_at: Optional[datetime] = None
    last_health_check: Optional[datetime] = None
    last_tool_discovery: Optional[datetime] = None
    error_count: int = 0
    tools: List[ToolSchema] = field(default_factory=list)
    client: Optional[MCPClientWrapper] = None  # MCP client wrapper
    health_task: Optional[asyncio.Task] = None


class MCPServerRegistry:
    """
    Dynamic registry for MCP servers.
    
    Features:
    - Runtime server registration/deregistration
    - Automatic tool discovery
    - Health monitoring
    - Tool schema caching
    - Server lifecycle management
    """
    
    def __init__(self, cache_dir: Optional[str] = None):
        self._servers: Dict[str, MCPServerInfo] = {}
        self._tool_cache: Dict[str, ToolSchema] = {}
        self._clients: Dict[str, MCPClientWrapper] = {}  # Client connections
        self._lock = asyncio.Lock()
        self._cache_dir = Path(cache_dir or ".cache/mcp_tools")
        self._cache_dir.mkdir(parents=True, exist_ok=True)
        self._discovery_interval = 300  # 5 minutes
        self._health_check_tasks: Dict[str, asyncio.Task] = {}
        
        # Load cached tool schemas
        self._load_cache()
        
        logger.info("MCPServerRegistry initialized", cache_dir=str(self._cache_dir))
    
    def _load_cache(self):
        """Load cached tool schemas from disk."""
        cache_file = self._cache_dir / "tool_schemas.json"
        if cache_file.exists():
            try:
                with open(cache_file, "r") as f:
                    data = json.load(f)
                    for tool_data in data.get("tools", []):
                        tool = ToolSchema(**tool_data)
                        self._tool_cache[tool.get_cache_key()] = tool
                logger.info(f"Loaded {len(self._tool_cache)} tool schemas from cache")
            except Exception as e:
                logger.error("Failed to load tool cache", error=str(e))
    
    def _save_cache(self):
        """Save tool schemas to disk cache."""
        cache_file = self._cache_dir / "tool_schemas.json"
        try:
            data = {
                "version": "1.0",
                "updated_at": datetime.now(timezone.utc).isoformat(),
                "tools": [tool.dict() for tool in self._tool_cache.values()]
            }
            with open(cache_file, "w") as f:
                json.dump(data, f, indent=2, default=str)
        except Exception as e:
            logger.error("Failed to save tool cache", error=str(e))
    
    async def register_server(self, config: Any) -> bool:
        """
        Register a new MCP server.
        
        Args:
            config: Server configuration (can be from mcp_config.py or local MCPServerConfig)
            
        Returns:
            True if successful, False otherwise
        """
        async with self._lock:
            # Convert external config to internal MCPServerConfig if needed
            if not isinstance(config, MCPServerConfig):
                # Handle MCPServerConfig from config/mcp_config.py
                internal_config = MCPServerConfig(
                    name=config.name,
                    command=config.command or "",  # Default to empty string
                    args=config.args or [],
                    env=config.env or {},
                    health_check=config.health_check or {"enabled": True, "interval": 30, "timeout": 10},
                    auto_discover=True,  # Default to True for auto-discovery
                    metadata={
                        "connection_type": getattr(config, "connection_type", "local"),
                        "endpoint": getattr(config, "endpoint", None),
                        "transport": getattr(config, "transport", None),
                        "url": getattr(config, "url", None),
                        "headers": getattr(config, "headers", {})
                    }
                )
            else:
                internal_config = config
                
            if internal_config.name in self._servers:
                logger.warning(f"Server {internal_config.name} already registered")
                return False
            
            server_info = MCPServerInfo(config=internal_config)
            self._servers[internal_config.name] = server_info
            
            # Start the server
            if await self._start_server(server_info):
                # Schedule health monitoring
                if internal_config.health_check.get("enabled", True):
                    self._health_check_tasks[internal_config.name] = asyncio.create_task(
                        self._monitor_server_health(internal_config.name)
                    )
                
                # Discover tools if auto-discovery enabled
                if internal_config.auto_discover:
                    await self._discover_server_tools(server_info)
                
                logger.info(f"Server {internal_config.name} registered successfully")
                return True
            else:
                del self._servers[internal_config.name]
                return False
    
    async def unregister_server(self, server_name: str) -> bool:
        """
        Unregister an MCP server.
        
        Args:
            server_name: Name of the server to unregister
            
        Returns:
            True if successful, False otherwise
        """
        async with self._lock:
            if server_name not in self._servers:
                logger.warning(f"Server {server_name} not found")
                return False
            
            server_info = self._servers[server_name]
            
            # Cancel health monitoring
            if server_name in self._health_check_tasks:
                self._health_check_tasks[server_name].cancel()
                del self._health_check_tasks[server_name]
            
            # Stop the server
            await self._stop_server(server_info)
            
            # Remove from registry
            del self._servers[server_name]
            
            # Remove cached tools
            tools_removed = 0
            for cache_key in list(self._tool_cache.keys()):
                if self._tool_cache[cache_key].server_name == server_name:
                    del self._tool_cache[cache_key]
                    tools_removed += 1
            
            logger.info(f"Server {server_name} unregistered, {tools_removed} tools removed")
            self._save_cache()
            return True
    
    async def _start_server(self, server_info: MCPServerInfo) -> bool:
        """Start an MCP server connection using MCPClientWrapper."""
        try:
            # Create client configuration from server info
            client_config = {
                'name': server_info.config.name,
                'command': server_info.config.command,
                'args': server_info.config.args,
                'env': server_info.config.env,
                'metadata': server_info.config.metadata
            }
            
            # Create and connect MCP client
            client = MCPClientWrapper(client_config)
            success = await client.connect()
            
            if success:
                server_info.client = client
                server_info.status = ServerStatus.CONNECTED
                server_info.connected_at = datetime.now(timezone.utc)
                self._clients[server_info.config.name] = client
                
                logger.info(f"Server {server_info.config.name} connected successfully")
                return True
            else:
                logger.error(f"Failed to connect to server {server_info.config.name}", 
                           error=client.get_last_error())
                server_info.status = ServerStatus.ERROR
                server_info.error_count += 1
                return False
            
        except Exception as e:
            logger.error(f"Failed to start server {server_info.config.name}", error=str(e))
            server_info.status = ServerStatus.ERROR
            server_info.error_count += 1
            return False
    
    async def _stop_server(self, server_info: MCPServerInfo):
        """Stop an MCP server connection."""
        try:
            # Disconnect the client if it exists
            if server_info.client:
                await server_info.client.disconnect()
                server_info.client = None
            
            # Remove from clients dict
            if server_info.config.name in self._clients:
                del self._clients[server_info.config.name]
            
            server_info.status = ServerStatus.DISCONNECTED
            logger.info(f"Server {server_info.config.name} disconnected")
        except Exception as e:
            logger.error(f"Failed to stop server {server_info.config.name}", error=str(e))
    
    async def _discover_server_tools(self, server_info: MCPServerInfo) -> List[ToolSchema]:
        """
        Discover available tools from an MCP server dynamically using MCPClientWrapper.
        
        Args:
            server_info: Server information
            
        Returns:
            List of discovered tool schemas
        """
        try:
            # Use the connected client if available
            client = server_info.client
            if not client or not client.is_connected():
                logger.warning(f"Client not connected for {server_info.config.name}, attempting to connect")
                # Try to start the server if not connected
                if not await self._start_server(server_info):
                    return await self._discover_server_tools_static(server_info)
                client = server_info.client
            
            # Get tools from the client
            mcp_tools = await client.list_tools()
            
            # Convert MCP tools to our ToolSchema format
            tools = []
            # mcp_tools is already a list from our wrapper
            for mcp_tool in mcp_tools:
                try:
                    # Extract tool info - handle both dict and object formats
                    if isinstance(mcp_tool, dict):
                        tool_name = mcp_tool.get('name', '')
                        tool_description = mcp_tool.get('description', '')
                        tool_parameters = mcp_tool.get('inputSchema', {})
                    else:
                        tool_name = getattr(mcp_tool, 'name', '')
                        tool_description = getattr(mcp_tool, 'description', '')
                        tool_parameters = getattr(mcp_tool, 'inputSchema', {})
                    
                    if tool_name:
                        tool = ToolSchema(
                            name=tool_name,
                            description=tool_description or f"Tool: {tool_name}",
                            parameters=tool_parameters or {
                                "type": "object",
                                "properties": {},
                                "required": []
                            },
                            server_name=server_info.config.name
                        )
                        tools.append(tool)
                except Exception as e:
                    logger.warning(f"Failed to process tool {mcp_tool}: {e}")
            
            # Update server info
            server_info.tools = tools
            server_info.last_tool_discovery = datetime.now(timezone.utc)
            
            # Clear old cached tools for this server before adding new ones
            tools_removed = 0
            for cache_key in list(self._tool_cache.keys()):
                if self._tool_cache[cache_key].server_name == server_info.config.name:
                    del self._tool_cache[cache_key]
                    tools_removed += 1
            
            if tools_removed > 0:
                logger.info(f"Cleared {tools_removed} old cached tools for {server_info.config.name}")
            
            # Cache new tools
            for tool in tools:
                self._tool_cache[tool.get_cache_key()] = tool
            
            self._save_cache()
            logger.info(f"Discovered {len(tools)} tools from {server_info.config.name}")
            return tools
            
        except Exception as e:
            logger.error(f"Failed to discover tools from {server_info.config.name}", error=str(e))
            server_info.error_count += 1
            # Try static discovery as fallback
            return await self._discover_server_tools_static(server_info)
    
    async def _discover_server_tools_static(self, server_info: MCPServerInfo) -> List[ToolSchema]:
        """
        Static fallback for tool discovery when dynamic discovery fails.
        
        Args:
            server_info: Server information
            
        Returns:
            List of discovered tool schemas
        """
        # For Sage Intacct, we have hardcoded tools as fallback
        if server_info.config.name == "sage-intacct":
            tools = [
                ToolSchema(
                    name="search_across_modules",
                    description="Search across multiple Intacct modules",
                    parameters={
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "Search query string"},
                            "modules": {"type": "array", "items": {"type": "string"}, "description": "List of modules to search"},
                            "limit": {"type": "integer", "default": 20, "description": "Maximum results per module"}
                        },
                        "required": ["query"]
                    },
                    server_name=server_info.config.name
                ),
                ToolSchema(
                    name="get_financial_summary",
                    description="Get a financial summary across modules",
                    parameters={
                        "type": "object",
                        "properties": {
                            "start_date": {"type": "string", "description": "Start date (YYYY-MM-DD)"},
                            "end_date": {"type": "string", "description": "End date (YYYY-MM-DD)"},
                            "include_modules": {"type": "array", "items": {"type": "string"}, "description": "Modules to include"}
                        }
                    },
                    server_name=server_info.config.name
                ),
                ToolSchema(
                    name="execute_month_end_close",
                    description="Execute month-end close procedures across modules",
                    parameters={
                        "type": "object",
                        "properties": {
                            "period": {"type": "string", "description": "Period to close (YYYY-MM)"},
                            "modules": {"type": "array", "items": {"type": "string"}, "description": "Modules to include"},
                            "dry_run": {"type": "boolean", "default": True, "description": "If True, simulate without making changes"}
                        },
                        "required": ["period"]
                    },
                    server_name=server_info.config.name
                ),
                ToolSchema(
                    name="generate_consolidated_report",
                    description="Generate a consolidated report across modules",
                    parameters={
                        "type": "object",
                        "properties": {
                            "report_type": {"type": "string", "description": "Type of report"},
                            "start_date": {"type": "string", "description": "Start date (YYYY-MM-DD)"},
                            "end_date": {"type": "string", "description": "End date (YYYY-MM-DD)"},
                            "include_modules": {"type": "array", "items": {"type": "string"}, "description": "Modules to include"},
                            "format": {"type": "string", "default": "json", "description": "Output format"}
                        },
                        "required": ["report_type", "start_date", "end_date"]
                    },
                    server_name=server_info.config.name
                ),
                ToolSchema(
                    name="list_enabled_modules",
                    description="List all enabled modules and their status",
                    parameters={
                        "type": "object",
                        "properties": {}
                    },
                    server_name=server_info.config.name
                ),
                ToolSchema(
                    name="health_check",
                    description="Check server health status",
                    parameters={
                        "type": "object",
                        "properties": {}
                    },
                    server_name=server_info.config.name
                )
            ]
            
            # Update server info
            server_info.tools = tools
            server_info.last_tool_discovery = datetime.now(timezone.utc)
            
            # Clear old cached tools for this server before adding new ones
            tools_removed = 0
            for cache_key in list(self._tool_cache.keys()):
                if self._tool_cache[cache_key].server_name == server_info.config.name:
                    del self._tool_cache[cache_key]
                    tools_removed += 1
            
            if tools_removed > 0:
                logger.info(f"Cleared {tools_removed} old cached tools for {server_info.config.name}")
            
            # Cache new tools
            for tool in tools:
                self._tool_cache[tool.get_cache_key()] = tool
            
            self._save_cache()
            logger.info(f"Discovered {len(tools)} tools from {server_info.config.name} (static)")
            return tools
        
        # For unknown servers, return empty list
        logger.warning(f"No static tool definitions for server {server_info.config.name}")
        return []
    
    async def _monitor_server_health(self, server_name: str):
        """Monitor server health in the background."""
        server_info = self._servers.get(server_name)
        if not server_info:
            return
        
        interval = server_info.config.health_check.get("interval", 30)
        
        while server_name in self._servers:
            try:
                await asyncio.sleep(interval)
                
                # Perform health check
                is_healthy = await self._check_server_health(server_info)
                
                # Update status
                if is_healthy:
                    if server_info.status != ServerStatus.CONNECTED:
                        logger.info(f"Server {server_name} recovered")
                    server_info.status = ServerStatus.CONNECTED
                    server_info.error_count = 0
                else:
                    server_info.status = ServerStatus.UNHEALTHY
                    server_info.error_count += 1
                
                server_info.last_health_check = datetime.now(timezone.utc)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Health check error for {server_name}", error=str(e))
                server_info.error_count += 1
    
    async def _check_server_health(self, server_info: MCPServerInfo) -> bool:
        """
        Check if a server is healthy.
        
        Args:
            server_info: Server information
            
        Returns:
            True if healthy, False otherwise
        """
        try:
            # Check if client is connected
            if not server_info.client or not server_info.client.is_connected():
                return False
            
            # Use the client's health check method
            is_healthy = await server_info.client.health_check()
            
            # For Sage Intacct, we could also call the health_check tool
            if is_healthy and server_info.config.name == "sage-intacct":
                try:
                    # Try calling the actual health_check tool
                    result = await server_info.client.call_tool("health_check", {})
                    # If the call succeeds, the server is healthy
                    return True
                except Exception:
                    # If health_check tool fails, but client is connected, still consider it healthy
                    return True
            
            return is_healthy
        except Exception:
            return False
    
    async def check_server_health(self, server_name: str) -> bool:
        """
        Public method to check server health.
        
        Args:
            server_name: Name of the server to check
            
        Returns:
            True if healthy, False otherwise
        """
        server_info = self._servers.get(server_name)
        if not server_info:
            return False
        
        is_healthy = await self._check_server_health(server_info)
        server_info.last_health_check = datetime.now(timezone.utc)
        
        if is_healthy:
            server_info.status = ServerStatus.CONNECTED
            server_info.error_count = 0
        else:
            server_info.status = ServerStatus.ERROR
            server_info.error_count += 1
        
        return is_healthy
    
    async def get_server(self, server_name: str) -> Optional[MCPServerInfo]:
        """Get information about a specific server."""
        return self._servers.get(server_name)
    
    def get_server_info(self, server_name: str) -> Optional[MCPServerInfo]:
        """Get information about a specific server (sync version)."""
        return self._servers.get(server_name)
    
    def get_server_status(self, server_name: str) -> Optional[str]:
        """Get the status of a specific server."""
        server_info = self._servers.get(server_name)
        if server_info:
            return server_info.status.value
        return None
    
    async def list_servers(self) -> List[Dict[str, Any]]:
        """List all registered servers with their status."""
        servers = []
        for name, info in self._servers.items():
            servers.append({
                "name": name,
                "status": info.status.value,
                "connected_at": info.connected_at.isoformat() if info.connected_at else None,
                "last_health_check": info.last_health_check.isoformat() if info.last_health_check else None,
                "tool_count": len(info.tools),
                "error_count": info.error_count,
                "config": info.config.dict()
            })
        return servers
    
    async def get_all_tools(self) -> List[ToolSchema]:
        """Get all available tools from all servers."""
        return list(self._tool_cache.values())
    
    async def get_server_tools(self, server_name: str) -> List[ToolSchema]:
        """Get tools for a specific server."""
        server_info = self._servers.get(server_name)
        if not server_info:
            return []
        return server_info.tools
    
    async def refresh_tools(self, server_name: Optional[str] = None):
        """
        Refresh tool discovery for one or all servers.
        
        Args:
            server_name: Specific server to refresh, or None for all
        """
        servers_to_refresh = []
        
        if server_name:
            server_info = self._servers.get(server_name)
            if server_info:
                servers_to_refresh.append(server_info)
        else:
            servers_to_refresh = list(self._servers.values())
        
        for server_info in servers_to_refresh:
            if server_info.config.auto_discover:
                await self._discover_server_tools(server_info)
    
    async def get_tool_by_name(self, tool_name: str, server_name: Optional[str] = None) -> Optional[ToolSchema]:
        """
        Get a specific tool by name.
        
        Args:
            tool_name: Name of the tool
            server_name: Optional server name to limit search
            
        Returns:
            Tool schema if found, None otherwise
        """
        for tool in self._tool_cache.values():
            if tool.name == tool_name:
                if server_name is None or tool.server_name == server_name:
                    return tool
        return None
    
    def get_tool_mapping(self) -> Dict[str, str]:
        """
        Get mapping between agent tool names and MCP server tool names.
        
        This creates a compatibility layer between the old agent-based tool names
        and the new MCP server tool names.
        
        Returns:
            Dictionary mapping agent tool names to MCP tool identifiers
        """
        mapping = {}
        
        # Map known agent tools to MCP tools
        # Format: agent_tool_name -> server_name:tool_name
        sage_mappings = {
            # GL Agent tools
            "get_account_balance": "sage-intacct:get_financial_summary",
            "get_trial_balance": "sage-intacct:generate_consolidated_report",
            "get_journal_entries": "sage-intacct:search_across_modules",
            "create_journal_entry": None,  # Not available in current MCP
            "get_financial_statements": "sage-intacct:generate_consolidated_report",
            
            # AR Agent tools
            "get_customer_balance": "sage-intacct:search_across_modules",
            "get_aging_report": "sage-intacct:generate_consolidated_report",
            "get_invoices": "sage-intacct:search_across_modules",
            "create_invoice": None,  # Not available
            "apply_payment": None,  # Not available
            
            # AP Agent tools
            "get_vendor_balance": "sage-intacct:search_across_modules",
            "get_bills": "sage-intacct:search_across_modules",
            "create_bill": None,  # Not available
            "create_payment": None,  # Not available
            "get_payment_batch": None,  # Not available
            
            # Analysis tools
            "analyze_variances": None,  # Handled by orchestrator logic
            "calculate_ratios": None,  # Handled by orchestrator logic
            "analyze_trends": None,  # Handled by orchestrator logic
            
            # Report tools
            "generate_report": "sage-intacct:generate_consolidated_report",
            "get_dashboard_data": "sage-intacct:get_financial_summary",
            
            # Validation tools
            "validate_data": None,  # Handled by orchestrator logic
            "check_compliance": None,  # Handled by orchestrator logic
            
            # General tools
            "search": "sage-intacct:search_across_modules",
            "get_modules": "sage-intacct:list_enabled_modules",
            "health_check": "sage-intacct:health_check",
            "month_end_close": "sage-intacct:execute_month_end_close"
        }
        
        # Build the mapping
        for agent_tool, mcp_tool in sage_mappings.items():
            if mcp_tool:
                mapping[agent_tool] = mcp_tool
        
        # Also create reverse mapping for all available tools
        for tool in self._tool_cache.values():
            full_name = f"{tool.server_name}:{tool.name}"
            # Direct tool name mapping
            mapping[tool.name] = full_name
            # Prefixed mapping (for fast-agent style)
            mapping[f"local__{tool.server_name}__{tool.name}"] = full_name
        
        return mapping
    
    async def get_tool_for_agent_method(self, agent_method: str) -> Optional[ToolSchema]:
        """
        Get the MCP tool that corresponds to an agent method.
        
        Args:
            agent_method: Name of the agent method
            
        Returns:
            ToolSchema if found, None if not available
        """
        mapping = self.get_tool_mapping()
        mcp_tool_id = mapping.get(agent_method)
        
        if not mcp_tool_id:
            return None
        
        # Parse server:tool format
        if ':' in mcp_tool_id:
            server_name, tool_name = mcp_tool_id.split(':', 1)
            return await self.get_tool_by_name(tool_name, server_name)
        
        return None
    
    def get_client(self, server_name: str) -> Optional[MCPClientWrapper]:
        """
        Get the MCP client for a specific server.
        
        Args:
            server_name: Name of the server
            
        Returns:
            MCPClientWrapper if connected, None otherwise
        """
        return self._clients.get(server_name)
    
    async def call_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """
        Execute a tool on a specific server.
        
        Args:
            server_name: Name of the server
            tool_name: Name of the tool to execute
            arguments: Tool arguments
            
        Returns:
            Tool execution result
            
        Raises:
            Exception: If server not found or tool execution fails
        """
        client = self.get_client(server_name)
        if not client:
            raise ValueError(f"Server {server_name} not found or not connected")
        
        return await client.call_tool(tool_name, arguments)
    
    async def health_report(self) -> Dict[str, Any]:
        """Generate a health report for all servers."""
        total_servers = len(self._servers)
        healthy_servers = sum(1 for s in self._servers.values() if s.status == ServerStatus.CONNECTED)
        total_tools = len(self._tool_cache)
        
        return {
            "healthy": healthy_servers == total_servers,
            "total_servers": total_servers,
            "healthy_servers": healthy_servers,
            "unhealthy_servers": total_servers - healthy_servers,
            "total_tools": total_tools,
            "servers": await self.list_servers()
        }


# Global instance
_registry: Optional[MCPServerRegistry] = None


def get_mcp_registry() -> MCPServerRegistry:
    """Get the global MCPServerRegistry instance."""
    global _registry
    if _registry is None:
        _registry = MCPServerRegistry()
    return _registry
