"""
Quick test to verify orchestrator functionality
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.orchestrator import DynamicOrchestrator


async def test_orchestrator():
    """Test basic orchestrator functionality"""
    print("Testing Dynamic Orchestrator...")
    
    orchestrator = DynamicOrchestrator()
    
    # Test intent detection
    print("\n1. Testing Intent Detection:")
    test_messages = [
        "What's the balance in account 1000?",
        "Create a journal entry for $5000",
        "Show me customer ABC Corp balance",
        "Run month-end close for January 2024",
        "Generate financial statements"
    ]
    
    for msg in test_messages:
        intent_info = await orchestrator._detect_intent(msg)
        print(f"   '{msg[:40]}...' -> Intent: {intent_info['primary_intent']} (confidence: {intent_info['confidence']:.2f})")
    
    # Test tool catalog
    print("\n2. Testing Tool Discovery:")
    try:
        catalog = await orchestrator.get_available_tools()
        print(f"   Total tools available: {catalog['total_tools']}")
        print("   Tools by category:")
        for category, tools in catalog['by_category'].items():
            if tools:
                print(f"     - {category}: {len(tools)} tools")
    except Exception as e:
        print(f"   Tool discovery error: {e}")
    
    print("\nBasic orchestrator tests completed!")


if __name__ == "__main__":
    asyncio.run(test_orchestrator())
