# Data Architecture Addendum
**Comprehensive Data Schema & Multi-Tenant Architecture for Intelligent Conversation Backend**

---

## Overview

This addendum provides detailed data architecture specifications for the Intelligent Conversation Backend, addressing multi-tenant B2B SaaS requirements, comprehensive timestamping strategies, and business intelligence needs that were not fully detailed in the main PRD.

## Critical Design Principles

### **1. Multi-Tenant First**
- Companies are top-level tenants (your customers)
- All data access patterns are company-scoped
- Strong data isolation between companies
- Company-specific configuration and policies

### **2. Privacy by Design**
- GDPR and compliance-ready from day one
- User data deletion and export capabilities
- Audit trails for all data access
- Configurable retention policies per company

### **3. Analytics-Ready Structure**
- Time-series optimized for trend analysis
- Efficient aggregation patterns for dashboards
- Real-time and batch analytics support
- Cross-company anonymized insights capability

### **4. Performance & Scale Optimized**
- Query patterns based on real usage
- Proper indexing for multi-tenant access
- Partitioning strategies for large datasets
- Efficient data archival and cleanup

---

## Core Entity Relationship Model

### **Hierarchical Structure**
```
Company (Tenant)
├── Users (Employees/Team Members)
│   ├── Sessions (Multiple concurrent sessions)
│   │   ├── Conversations (Messages within sessions)
│   │   └── Session Analytics
│   ├── User Preferences
│   └── User Analytics
├── Company Settings
└── Company Analytics
```

### **Entity Definitions**

#### **Companies Table**
```sql
CREATE TABLE companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255), -- Company email domain for auto-assignment
    industry VARCHAR(100),
    company_size VARCHAR(50), -- startup, small, medium, large, enterprise
    
    -- Subscription & Account Info
    subscription_tier VARCHAR(50) DEFAULT 'free', -- free, pro, enterprise
    subscription_status VARCHAR(50) DEFAULT 'active',
    trial_ends_at TIMESTAMP WITH TIME ZONE,
    
    -- Data Policies
    data_retention_days INTEGER DEFAULT 365,
    data_region VARCHAR(10) DEFAULT 'us', -- us, eu, apac for compliance
    privacy_settings JSONB DEFAULT '{}',
    
    -- Compliance Flags
    gdpr_enabled BOOLEAN DEFAULT false,
    hipaa_enabled BOOLEAN DEFAULT false,
    sox_compliance BOOLEAN DEFAULT false,
    
    -- Configuration
    features_enabled JSONB DEFAULT '{}', -- Feature flags per company
    integrations_config JSONB DEFAULT '{}', -- Sage Intacct configs, etc.
    branding_config JSONB DEFAULT '{}', -- Custom branding settings
    
    -- Audit Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_activity_at TIMESTAMP WITH TIME ZONE,
    
    -- Soft Delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Search & Analytics
    search_vector tsvector, -- Full-text search on name, domain
    
    CONSTRAINT companies_name_check CHECK (char_length(name) >= 2),
    CONSTRAINT companies_domain_format CHECK (domain ~* '^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
);

-- Indexes for Companies
CREATE INDEX idx_companies_domain ON companies(domain) WHERE deleted_at IS NULL;
CREATE INDEX idx_companies_subscription ON companies(subscription_tier, subscription_status);
CREATE INDEX idx_companies_activity ON companies(last_activity_at DESC);
CREATE INDEX idx_companies_search ON companies USING gin(search_vector);
```

#### **Users Table**
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    
    -- Identity
    email VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(500),
    
    -- Authorization
    role VARCHAR(50) DEFAULT 'user', -- admin, user, viewer, accountant, manager
    permissions JSONB DEFAULT '[]', -- Granular permissions array
    
    -- Authentication
    auth_provider VARCHAR(50) DEFAULT 'email', -- email, google, microsoft, okta
    auth_provider_id VARCHAR(255), -- External auth system ID
    email_verified BOOLEAN DEFAULT false,
    
    -- Status
    status VARCHAR(50) DEFAULT 'active', -- active, inactive, suspended, invited
    invitation_token VARCHAR(255), -- For pending invitations
    invitation_expires_at TIMESTAMP WITH TIME ZONE,
    
    -- User Preferences
    preferences JSONB DEFAULT '{}', -- UI preferences, notification settings
    timezone VARCHAR(50) DEFAULT 'UTC',
    locale VARCHAR(10) DEFAULT 'en-US',
    
    -- Activity Tracking
    last_login_at TIMESTAMP WITH TIME ZONE,
    last_activity_at TIMESTAMP WITH TIME ZONE,
    login_count INTEGER DEFAULT 0,
    
    -- Audit Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Soft Delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Data Export/Privacy
    data_export_requested_at TIMESTAMP WITH TIME ZONE,
    data_deletion_requested_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT users_email_company_unique UNIQUE (email, company_id),
    CONSTRAINT users_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT users_name_check CHECK (char_length(name) >= 1)
);

-- Indexes for Users
CREATE INDEX idx_users_company_id ON users(company_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_email ON users(email) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_status ON users(company_id, status);
CREATE INDEX idx_users_activity ON users(company_id, last_activity_at DESC);
CREATE INDEX idx_users_role ON users(company_id, role);
```

#### **Sessions Table**
```sql
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    
    -- Session Identity
    session_token VARCHAR(255) UNIQUE NOT NULL, -- Secure session identifier
    
    -- Session Metadata
    device_type VARCHAR(50), -- desktop, mobile, tablet
    browser VARCHAR(100),
    os VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    
    -- Geographic Info
    country VARCHAR(2), -- ISO country code
    region VARCHAR(100),
    city VARCHAR(100),
    
    -- Session Lifecycle
    status VARCHAR(50) DEFAULT 'active', -- active, expired, terminated, hijacked
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE, -- When session should expire
    terminated_at TIMESTAMP WITH TIME ZONE,
    termination_reason VARCHAR(100), -- timeout, logout, admin, security
    
    -- Session Analytics
    total_messages INTEGER DEFAULT 0,
    total_tools_used INTEGER DEFAULT 0,
    session_duration_seconds INTEGER, -- Calculated on termination
    
    -- Security
    security_flags JSONB DEFAULT '{}', -- Suspicious activity markers
    concurrent_session_count INTEGER DEFAULT 1, -- How many sessions user had when this started
    
    -- Session Context
    initial_referrer VARCHAR(500), -- What brought user to this session
    session_metadata JSONB DEFAULT '{}', -- Custom session data
    
    CONSTRAINT sessions_user_company_consistency 
        CHECK ((SELECT company_id FROM users WHERE id = user_id) = company_id)
);

-- Indexes for Sessions
CREATE INDEX idx_sessions_user_id ON sessions(user_id, created_at DESC);
CREATE INDEX idx_sessions_company_id ON sessions(company_id, created_at DESC);
CREATE INDEX idx_sessions_token ON sessions(session_token) WHERE status = 'active';
CREATE INDEX idx_sessions_activity ON sessions(last_activity_at DESC) WHERE status = 'active';
CREATE INDEX idx_sessions_expires ON sessions(expires_at) WHERE status = 'active';
CREATE INDEX idx_sessions_analytics ON sessions(company_id, created_at) 
    WHERE status = 'active' AND total_messages > 0;
```

#### **Conversations Table**
```sql
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES sessions(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    
    -- Message Content
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    content_hash VARCHAR(64), -- SHA-256 hash for deduplication
    
    -- Message Metadata
    message_type VARCHAR(50) DEFAULT 'text', -- text, tool_call, tool_result, error
    parent_message_id UUID REFERENCES conversations(id), -- For threading/replies
    thread_position INTEGER DEFAULT 0, -- Position in thread
    
    -- Tool Integration
    tool_calls JSONB DEFAULT '[]', -- Tools called in this message
    tool_results JSONB DEFAULT '[]', -- Results from tool execution
    tool_execution_time_ms INTEGER, -- How long tools took to execute
    
    -- AI/LLM Metadata
    model_used VARCHAR(100), -- Which AI model generated response
    model_version VARCHAR(50),
    confidence_score DECIMAL(3,2), -- 0.00 to 1.00
    prompt_tokens INTEGER, -- For cost tracking
    completion_tokens INTEGER,
    total_tokens INTEGER,
    
    -- Timing
    message_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processing_started_at TIMESTAMP WITH TIME ZONE,
    processing_completed_at TIMESTAMP WITH TIME ZONE,
    processing_duration_ms INTEGER,
    
    -- Context & Analytics
    conversation_turn INTEGER, -- Which turn in the conversation (1, 2, 3...)
    cumulative_context_length INTEGER, -- Total context size at this point
    
    -- Semantic Search (for advanced backends)
    embedding_vector vector(384), -- Sentence transformer embeddings
    semantic_keywords TEXT[], -- Extracted keywords for search
    
    -- Quality & Feedback
    user_feedback VARCHAR(20), -- thumbs_up, thumbs_down, neutral
    user_feedback_text TEXT,
    quality_score DECIMAL(3,2), -- Calculated quality metric
    
    -- Privacy & Compliance
    contains_pii BOOLEAN DEFAULT false, -- Automatically detected PII
    anonymized BOOLEAN DEFAULT false, -- Has been anonymized
    retention_override_days INTEGER, -- Override company retention policy
    
    -- Soft Delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT conversations_user_session_consistency 
        CHECK ((SELECT user_id FROM sessions WHERE id = session_id) = user_id),
    CONSTRAINT conversations_company_consistency
        CHECK ((SELECT company_id FROM sessions WHERE id = session_id) = company_id),
    CONSTRAINT conversations_turn_positive CHECK (conversation_turn > 0),
    CONSTRAINT conversations_confidence_range CHECK (confidence_score BETWEEN 0.00 AND 1.00)
);

-- Indexes for Conversations
CREATE INDEX idx_conversations_session_timestamp ON conversations(session_id, message_timestamp);
CREATE INDEX idx_conversations_user_timestamp ON conversations(user_id, message_timestamp DESC);
CREATE INDEX idx_conversations_company_timestamp ON conversations(company_id, message_timestamp DESC);
CREATE INDEX idx_conversations_role ON conversations(company_id, role, message_timestamp DESC);
CREATE INDEX idx_conversations_tools ON conversations USING gin(tool_calls) WHERE jsonb_array_length(tool_calls) > 0;
CREATE INDEX idx_conversations_search ON conversations USING gin(to_tsvector('english', content));
CREATE INDEX idx_conversations_semantic_keywords ON conversations USING gin(semantic_keywords);

-- Vector similarity search index (for PostgreSQL with pgvector)
CREATE INDEX idx_conversations_embedding ON conversations USING ivfflat (embedding_vector vector_cosine_ops) 
    WITH (lists = 100) WHERE embedding_vector IS NOT NULL;

-- Partial indexes for active data
CREATE INDEX idx_conversations_active_user ON conversations(user_id, message_timestamp DESC) 
    WHERE deleted_at IS NULL;
CREATE INDEX idx_conversations_active_company ON conversations(company_id, message_timestamp DESC) 
    WHERE deleted_at IS NULL;
```

---

## Supporting Tables

### **User Preferences & Learning**
```sql
CREATE TABLE user_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    
    -- Preference Categories
    category VARCHAR(50) NOT NULL, -- ui, notifications, ai_behavior, reporting
    preference_key VARCHAR(100) NOT NULL,
    preference_value JSONB NOT NULL,
    
    -- Learning & Adaptation
    learned_automatically BOOLEAN DEFAULT false, -- System learned vs user set
    confidence DECIMAL(3,2) DEFAULT 1.00, -- How confident in this preference
    usage_count INTEGER DEFAULT 0, -- How often this preference was applied
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP WITH TIME ZONE,
    
    UNIQUE(user_id, category, preference_key)
);

CREATE INDEX idx_user_preferences_user_category ON user_preferences(user_id, category);
```

### **Analytics Aggregations**
```sql
-- Daily User Activity Aggregations
CREATE TABLE daily_user_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    
    -- Activity Metrics
    sessions_count INTEGER DEFAULT 0,
    messages_sent INTEGER DEFAULT 0,
    messages_received INTEGER DEFAULT 0,
    tools_used INTEGER DEFAULT 0,
    unique_tools_used INTEGER DEFAULT 0,
    
    -- Time Metrics
    total_session_time_seconds INTEGER DEFAULT 0,
    avg_session_time_seconds INTEGER DEFAULT 0,
    avg_response_time_ms INTEGER DEFAULT 0,
    
    -- Engagement Metrics
    conversation_turns INTEGER DEFAULT 0,
    avg_conversation_length DECIMAL(5,2) DEFAULT 0,
    feedback_positive INTEGER DEFAULT 0,
    feedback_negative INTEGER DEFAULT 0,
    
    -- Tool Performance
    successful_tool_calls INTEGER DEFAULT 0,
    failed_tool_calls INTEGER DEFAULT 0,
    most_used_tools JSONB DEFAULT '[]',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, date)
);

-- Daily Company Activity Aggregations
CREATE TABLE daily_company_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    
    -- User Activity
    active_users INTEGER DEFAULT 0,
    new_users INTEGER DEFAULT 0,
    total_sessions INTEGER DEFAULT 0,
    
    -- Content Activity
    total_messages INTEGER DEFAULT 0,
    total_tool_calls INTEGER DEFAULT 0,
    
    -- Performance
    avg_response_time_ms INTEGER DEFAULT 0,
    error_rate DECIMAL(5,4) DEFAULT 0,
    
    -- Engagement
    avg_session_duration_seconds INTEGER DEFAULT 0,
    high_engagement_users INTEGER DEFAULT 0, -- Users with >10 messages
    
    -- Tool Usage Distribution
    tool_usage_distribution JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(company_id, date)
);

-- Indexes for Analytics
CREATE INDEX idx_daily_user_analytics_company_date ON daily_user_analytics(company_id, date DESC);
CREATE INDEX idx_daily_user_analytics_user_date ON daily_user_analytics(user_id, date DESC);
CREATE INDEX idx_daily_company_analytics_date ON daily_company_analytics(date DESC);
```

### **Audit Log**
```sql
CREATE TABLE audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL, -- Can be system actions
    
    -- Audit Details
    action VARCHAR(100) NOT NULL, -- user_login, data_export, message_delete, etc.
    resource_type VARCHAR(50) NOT NULL, -- user, session, conversation, company
    resource_id UUID, -- ID of the affected resource
    
    -- Context
    ip_address INET,
    user_agent TEXT,
    session_id UUID REFERENCES sessions(id) ON DELETE SET NULL,
    
    -- Details
    details JSONB DEFAULT '{}', -- Action-specific details
    old_values JSONB, -- Before values for updates
    new_values JSONB, -- After values for updates
    
    -- Risk Assessment
    risk_level VARCHAR(20) DEFAULT 'low', -- low, medium, high, critical
    automated_action BOOLEAN DEFAULT false, -- System vs user action
    
    -- Timestamp
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Compliance
    compliance_tags TEXT[] DEFAULT '{}' -- gdpr, hipaa, sox tags
);

-- Indexes for Audit Log
CREATE INDEX idx_audit_log_company_timestamp ON audit_log(company_id, timestamp DESC);
CREATE INDEX idx_audit_log_user_timestamp ON audit_log(user_id, timestamp DESC) WHERE user_id IS NOT NULL;
CREATE INDEX idx_audit_log_action ON audit_log(company_id, action, timestamp DESC);
CREATE INDEX idx_audit_log_resource ON audit_log(resource_type, resource_id, timestamp DESC);
CREATE INDEX idx_audit_log_risk ON audit_log(company_id, risk_level, timestamp DESC) WHERE risk_level IN ('high', 'critical');
```

---

## Data Access Patterns & Query Optimization

### **Primary Query Patterns**

#### **1. Real-time Conversation Queries**
```sql
-- Get conversation context for active session
SELECT c.* FROM conversations c
JOIN sessions s ON c.session_id = s.id
WHERE s.session_token = ? 
  AND s.status = 'active'
  AND c.deleted_at IS NULL
ORDER BY c.message_timestamp DESC
LIMIT 20;

-- Save new conversation message
INSERT INTO conversations (session_id, user_id, company_id, role, content, ...)
VALUES (...);
```

#### **2. User Analytics Queries**
```sql
-- User activity summary
SELECT 
  DATE_TRUNC('day', c.message_timestamp) as date,
  COUNT(*) as messages,
  COUNT(DISTINCT c.session_id) as sessions,
  jsonb_array_length(jsonb_agg(DISTINCT tool_calls)) as unique_tools
FROM conversations c
JOIN sessions s ON c.session_id = s.id
WHERE c.user_id = ? 
  AND c.message_timestamp >= ?
  AND c.deleted_at IS NULL
GROUP BY DATE_TRUNC('day', c.message_timestamp)
ORDER BY date DESC;
```

#### **3. Company-wide Analytics**
```sql
-- Company engagement metrics
SELECT 
  COUNT(DISTINCT u.id) as active_users,
  COUNT(DISTINCT s.id) as total_sessions,
  COUNT(c.id) as total_messages,
  AVG(s.session_duration_seconds) as avg_session_duration
FROM companies comp
LEFT JOIN users u ON u.company_id = comp.id AND u.deleted_at IS NULL
LEFT JOIN sessions s ON s.user_id = u.id AND s.created_at >= ?
LEFT JOIN conversations c ON c.session_id = s.id AND c.deleted_at IS NULL
WHERE comp.id = ?;
```

### **Performance Optimization Strategies**

#### **1. Partitioning**
```sql
-- Partition conversations by company_id for large datasets
CREATE TABLE conversations_template (LIKE conversations INCLUDING ALL);

-- Create monthly partitions for high-volume companies
CREATE TABLE conversations_company_123_2025_01 
PARTITION OF conversations_template
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01')
WHERE company_id = '123';
```

#### **2. Materialized Views for Analytics**
```sql
-- Real-time company dashboard view
CREATE MATERIALIZED VIEW company_dashboard_metrics AS
SELECT 
  c.company_id,
  DATE_TRUNC('hour', conv.message_timestamp) as hour,
  COUNT(DISTINCT u.id) as active_users,
  COUNT(DISTINCT s.id) as active_sessions,
  COUNT(conv.id) as messages_count,
  AVG(conv.processing_duration_ms) as avg_response_time,
  COUNT(conv.id) FILTER (WHERE conv.role = 'assistant' AND conv.user_feedback = 'thumbs_up') as positive_feedback
FROM companies c
LEFT JOIN users u ON u.company_id = c.id
LEFT JOIN sessions s ON s.user_id = u.id
LEFT JOIN conversations conv ON conv.session_id = s.id
WHERE conv.message_timestamp >= NOW() - INTERVAL '24 hours'
  AND conv.deleted_at IS NULL
GROUP BY c.company_id, DATE_TRUNC('hour', conv.message_timestamp);

CREATE UNIQUE INDEX ON company_dashboard_metrics(company_id, hour);
```

#### **3. Read Replicas for Analytics**
- Route real-time conversation writes to primary database
- Route analytics queries to read replicas
- Use connection pooling with different pools for different workloads

---

## Data Retention & Privacy Implementation

### **Company-Level Retention Policies**
```sql
-- Automated cleanup based on company policies
CREATE OR REPLACE FUNCTION cleanup_expired_data()
RETURNS void AS $$
DECLARE
    company_record RECORD;
    cutoff_date TIMESTAMP WITH TIME ZONE;
BEGIN
    FOR company_record IN 
        SELECT id, data_retention_days FROM companies 
        WHERE deleted_at IS NULL 
    LOOP
        cutoff_date := CURRENT_TIMESTAMP - INTERVAL '1 day' * company_record.data_retention_days;
        
        -- Soft delete expired conversations
        UPDATE conversations 
        SET deleted_at = CURRENT_TIMESTAMP,
            content = '[DELETED]'
        WHERE company_id = company_record.id
          AND message_timestamp < cutoff_date
          AND deleted_at IS NULL;
          
        -- Log the cleanup action
        INSERT INTO audit_log (company_id, action, details, automated_action)
        VALUES (
            company_record.id,
            'data_retention_cleanup',
            jsonb_build_object('cutoff_date', cutoff_date, 'retention_days', company_record.data_retention_days),
            true
        );
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Schedule cleanup to run daily
SELECT cron.schedule('data-cleanup', '0 2 * * *', 'SELECT cleanup_expired_data();');
```

### **GDPR Data Export**
```sql
-- Complete user data export for GDPR compliance
CREATE OR REPLACE FUNCTION export_user_data(target_user_id UUID)
RETURNS JSONB AS $$
DECLARE
    user_data JSONB;
    conversations_data JSONB;
    sessions_data JSONB;
    preferences_data JSONB;
BEGIN
    -- User profile data
    SELECT to_jsonb(u.*) INTO user_data
    FROM users u WHERE u.id = target_user_id;
    
    -- All conversation data
    SELECT jsonb_agg(to_jsonb(c.*)) INTO conversations_data
    FROM conversations c 
    WHERE c.user_id = target_user_id AND c.deleted_at IS NULL;
    
    -- Session data
    SELECT jsonb_agg(to_jsonb(s.*)) INTO sessions_data
    FROM sessions s 
    WHERE s.user_id = target_user_id;
    
    -- Preferences data
    SELECT jsonb_agg(to_jsonb(p.*)) INTO preferences_data
    FROM user_preferences p
    WHERE p.user_id = target_user_id;
    
    RETURN jsonb_build_object(
        'user_profile', user_data,
        'conversations', conversations_data,
        'sessions', sessions_data,
        'preferences', preferences_data,
        'export_timestamp', CURRENT_TIMESTAMP
    );
END;
$$ LANGUAGE plpgsql;
```

### **GDPR Data Deletion**
```sql
-- Complete user data deletion for GDPR right to be forgotten
CREATE OR REPLACE FUNCTION delete_user_data(target_user_id UUID)
RETURNS void AS $$
DECLARE
    company_record RECORD;
    affected_conversations INTEGER;
    affected_sessions INTEGER;
BEGIN
    -- Get user's company for audit logging
    SELECT c.* INTO company_record
    FROM companies c
    JOIN users u ON u.company_id = c.id
    WHERE u.id = target_user_id;
    
    -- Count affected records for audit
    SELECT COUNT(*) INTO affected_conversations
    FROM conversations WHERE user_id = target_user_id AND deleted_at IS NULL;
    
    SELECT COUNT(*) INTO affected_sessions
    FROM sessions WHERE user_id = target_user_id;
    
    -- Anonymize conversations (keep for company analytics but remove PII)
    UPDATE conversations 
    SET content = '[USER DATA DELETED]',
        user_feedback_text = NULL,
        deleted_at = CURRENT_TIMESTAMP,
        anonymized = true
    WHERE user_id = target_user_id AND deleted_at IS NULL;
    
    -- Delete sessions
    DELETE FROM sessions WHERE user_id = target_user_id;
    
    -- Delete preferences
    DELETE FROM user_preferences WHERE user_id = target_user_id;
    
    -- Soft delete user
    UPDATE users 
    SET deleted_at = CURRENT_TIMESTAMP,
        email = CONCAT('deleted_', id, '@deleted.local'),
        name = '[DELETED USER]',
        auth_provider_id = NULL
    WHERE id = target_user_id;
    
    -- Audit log the deletion
    INSERT INTO audit_log (company_id, user_id, action, details, risk_level)
    VALUES (
        company_record.id,
        NULL, -- User is being deleted
        'gdpr_user_deletion',
        jsonb_build_object(
            'deleted_user_id', target_user_id,
            'conversations_affected', affected_conversations,
            'sessions_affected', affected_sessions
        ),
        'high'
    );
END;
$$ LANGUAGE plpgsql;
```

---

## Analytics Data Models

### **Real-Time Analytics Views**

#### **Company Health Dashboard**
```sql
CREATE VIEW company_health_dashboard AS
SELECT 
    c.id as company_id,
    c.name as company_name,
    c.subscription_tier,
    
    -- User Metrics (Last 30 days)
    COUNT(DISTINCT u.id) FILTER (WHERE u.last_activity_at >= CURRENT_DATE - INTERVAL '30 days') as active_users_30d,
    COUNT(DISTINCT u.id) FILTER (WHERE u.last_activity_at >= CURRENT_DATE - INTERVAL '7 days') as active_users_7d,
    COUNT(DISTINCT u.id) FILTER (WHERE u.created_at >= CURRENT_DATE - INTERVAL '30 days') as new_users_30d,
    
    -- Engagement Metrics
    AVG(s.session_duration_seconds) FILTER (WHERE s.created_at >= CURRENT_DATE - INTERVAL '30 days') as avg_session_duration_30d,
    COUNT(conv.id) FILTER (WHERE conv.message_timestamp >= CURRENT_DATE - INTERVAL '30 days') as total_messages_30d,
    
    -- Tool Usage
    COUNT(DISTINCT jsonb_array_elements_text(conv.tool_calls)) FILTER (
        WHERE conv.message_timestamp >= CURRENT_DATE - INTERVAL '30 days'
        AND jsonb_array_length(conv.tool_calls) > 0
    ) as unique_tools_used_30d,
    
    -- Satisfaction
    COUNT(conv.id) FILTER (
        WHERE conv.user_feedback = 'thumbs_up' 
        AND conv.message_timestamp >= CURRENT_DATE - INTERVAL '30 days'
    ) * 100.0 / NULLIF(COUNT(conv.id) FILTER (
        WHERE conv.user_feedback IS NOT NULL 
        AND conv.message_timestamp >= CURRENT_DATE - INTERVAL '30 days'
    ), 0) as satisfaction_rate_30d,
    
    -- Last Activity
    MAX(u.last_activity_at) as last_user_activity,
    MAX(conv.message_timestamp) as last_conversation
    
FROM companies c
LEFT JOIN users u ON u.company_id = c.id AND u.deleted_at IS NULL
LEFT JOIN sessions s ON s.user_id = u.id
LEFT JOIN conversations conv ON conv.session_id = s.id AND conv.deleted_at IS NULL
WHERE c.deleted_at IS NULL
GROUP BY c.id, c.name, c.subscription_tier;
```

#### **User Engagement Scoring**
```sql
CREATE VIEW user_engagement_scores AS
WITH user_activity AS (
    SELECT 
        u.id as user_id,
        u.company_id,
        u.name,
        u.created_at as user_created_at,
        
        -- Recent activity (last 30 days)
        COUNT(DISTINCT s.id) FILTER (WHERE s.created_at >= CURRENT_DATE - INTERVAL '30 days') as sessions_30d,
        COUNT(conv.id) FILTER (WHERE conv.message_timestamp >= CURRENT_DATE - INTERVAL '30 days') as messages_30d,
        COUNT(DISTINCT DATE(conv.message_timestamp)) FILTER (
            WHERE conv.message_timestamp >= CURRENT_DATE - INTERVAL '30 days'
        ) as active_days_30d,
        
        -- Tool usage sophistication
        COUNT(DISTINCT jsonb_array_elements_text(conv.tool_calls)) FILTER (
            WHERE conv.message_timestamp >= CURRENT_DATE - INTERVAL '30 days'
            AND jsonb_array_length(conv.tool_calls) > 0
        ) as unique_tools_30d,
        
        -- Feedback patterns
        COUNT(conv.id) FILTER (
            WHERE conv.user_feedback = 'thumbs_up' 
            AND conv.message_timestamp >= CURRENT_DATE - INTERVAL '30 days'
        ) as positive_feedback_30d,
        
        -- Recency
        EXTRACT(DAYS FROM CURRENT_DATE - DATE(MAX(conv.message_timestamp))) as days_since_last_message
        
    FROM users u
    LEFT JOIN sessions s ON s.user_id = u.id
    LEFT JOIN conversations conv ON conv.session_id = s.id AND conv.deleted_at IS NULL
    WHERE u.deleted_at IS NULL
    GROUP BY u.id, u.company_id, u.name, u.created_at
)
SELECT 
    *,
    -- Engagement score calculation (0-100)
    LEAST(100, 
        (CASE WHEN sessions_30d >= 10 THEN 25 ELSE sessions_30d * 2.5 END) +
        (CASE WHEN messages_30d >= 50 THEN 25 ELSE messages_30d * 0.5 END) +
        (CASE WHEN active_days_30d >= 15 THEN 25 ELSE active_days_30d * 1.67 END) +
        (CASE WHEN unique_tools_30d >= 5 THEN 25 ELSE unique_tools_30d * 5 END)
    ) as engagement_score,
    
    -- Engagement tier
    CASE 
        WHEN sessions_30d = 0 THEN 'Inactive'
        WHEN sessions_30d <= 2 AND messages_30d <= 10 THEN 'Low'
        WHEN sessions_30d <= 5 AND messages_30d <= 25 THEN 'Medium'
        WHEN sessions_30d >= 10 OR messages_30d >= 50 OR unique_tools_30d >= 5 THEN 'High'
        ELSE 'Medium'
    END as engagement_tier,
    
    -- Risk indicators
    CASE 
        WHEN days_since_last_message > 14 THEN 'At Risk'
        WHEN days_since_last_message > 7 THEN 'Declining'
        ELSE 'Active'
    END as activity_status
    
FROM user_activity;
```

### **Predictive Analytics Models**

#### **Churn Risk Calculation**
```sql
CREATE VIEW company_churn_risk AS
WITH company_trends AS (
    SELECT 
        c.id as company_id,
        c.name as company_name,
        c.subscription_tier,
        c.created_at as company_created_at,
        
        -- Activity trends
        COUNT(DISTINCT u.id) FILTER (WHERE u.last_activity_at >= CURRENT_DATE - INTERVAL '7 days') as active_users_7d,
        COUNT(DISTINCT u.id) FILTER (WHERE u.last_activity_at >= CURRENT_DATE - INTERVAL '30 days') as active_users_30d,
        COUNT(conv.id) FILTER (WHERE conv.message_timestamp >= CURRENT_DATE - INTERVAL '7 days') as messages_7d,
        COUNT(conv.id) FILTER (WHERE conv.message_timestamp >= CURRENT_DATE - INTERVAL '30 days') as messages_30d,
        
        -- Satisfaction trends
        AVG(conv.confidence_score) FILTER (WHERE conv.message_timestamp >= CURRENT_DATE - INTERVAL '30 days') as avg_confidence_30d,
        COUNT(conv.id) FILTER (
            WHERE conv.user_feedback = 'thumbs_down' 
            AND conv.message_timestamp >= CURRENT_DATE - INTERVAL '30 days'
        ) * 100.0 / NULLIF(COUNT(conv.id) FILTER (
            WHERE conv.user_feedback IS NOT NULL 
            AND conv.message_timestamp >= CURRENT_DATE - INTERVAL '30 days'
        ), 0) as negative_feedback_rate_30d,
        
        -- Support indicators
        COUNT(conv.id) FILTER (
            WHERE conv.message_timestamp >= CURRENT_DATE - INTERVAL '30 days'
            AND conv.content ILIKE '%error%'
        ) as error_mentions_30d,
        
        MAX(u.last_activity_at) as last_company_activity
        
    FROM companies c
    LEFT JOIN users u ON u.company_id = c.id AND u.deleted_at IS NULL
    LEFT JOIN sessions s ON s.user_id = u.id
    LEFT JOIN conversations conv ON conv.session_id = s.id AND conv.deleted_at IS NULL
    WHERE c.deleted_at IS NULL
    GROUP BY c.id, c.name, c.subscription_tier, c.created_at
)
SELECT 
    *,
    -- Churn risk score (0-100, higher = more risk)
    LEAST(100, 
        (CASE WHEN EXTRACT(DAYS FROM CURRENT_DATE - DATE(last_company_activity)) > 14 THEN 40 ELSE 0 END) +
        (CASE WHEN active_users_7d = 0 THEN 30 ELSE 0 END) +
        (CASE WHEN messages_7d < messages_30d * 0.1 THEN 20 ELSE 0 END) +
        (CASE WHEN COALESCE(negative_feedback_rate_30d, 0) > 20 THEN 10 ELSE 0 END)
    ) as churn_risk_score,
    
    -- Risk category
    CASE 
        WHEN EXTRACT(DAYS FROM CURRENT_DATE - DATE(last_company_activity)) > 30 THEN 'Critical'
        WHEN active_users_7d = 0 AND messages_7d = 0 THEN 'High'
        WHEN messages_7d < messages_30d * 0.1 THEN 'Medium'
        ELSE 'Low'
    END as churn_risk_category,
    
    -- Days since last activity
    EXTRACT(DAYS FROM CURRENT_DATE - DATE(last_company_activity)) as days_since_last_activity
    
FROM company_trends;
```

---

## Conversation MCP Server Tool Mappings

### **Updated Tool Specifications**

Given the comprehensive data model, the MCP server tools should map to these data structures:

```python
# Core conversation tools with proper multi-tenant context
@server.call_tool()
async def get_conversation_context(
    session_id: str, 
    include_messages: int = 10,
    include_user_preferences: bool = True
) -> Dict[str, Any]:
    """Get conversation context with user preferences and company context"""
    
@server.call_tool()
async def save_message(
    session_id: str,
    role: str,
    content: str,
    tool_calls: Optional[List[str]] = None,
    processing_duration_ms: Optional[int] = None,
    confidence_score: Optional[float] = None,
    model_used: Optional[str] = None
) -> Dict[str, Any]:
    """Save message with comprehensive metadata"""

# Analytics tools with company context
@server.call_tool()
async def get_user_analytics(
    user_id: str,
    company_id: str,  # Required for data isolation
    days_back: int = 30,
    include_predictions: bool = False
) -> Dict[str, Any]:
    """Get user analytics with engagement scoring and predictions"""

@server.call_tool()
async def get_company_analytics(
    company_id: str,
    days_back: int = 7,
    include_user_breakdown: bool = True,
    include_churn_risk: bool = False
) -> Dict[str, Any]:
    """Get company-wide analytics with user engagement breakdown"""

# Advanced analytics tools
@server.call_tool()
async def search_conversations(
    query: str,
    company_id: str,  # Required for data isolation
    user_id: Optional[str] = None,
    date_range: Optional[Dict[str, str]] = None,
    semantic_search: bool = False,
    limit: int = 20
) -> List[Dict[str, Any]]:
    """Search conversations with semantic capabilities"""

@server.call_tool()
async def get_usage_insights(
    company_id: str,
    insight_type: str,  # tool_optimization, user_behavior, workflow_patterns
    time_period: str = "30d"
) -> Dict[str, Any]:
    """Generate actionable insights from usage patterns"""
```

---

## Implementation Considerations

### **Database Selection by Deployment Scale**

#### **SQLite Backend (Development/Small Scale)**
- **Use Case**: < 1000 conversations/day, single instance
- **Benefits**: Zero configuration, file-based, excellent for development
- **Limitations**: No concurrent writers, limited analytics performance

#### **PostgreSQL Backend (Production/Enterprise)**
- **Use Case**: > 1000 conversations/day, multi-tenant production
- **Benefits**: ACID compliance, vector search, advanced analytics, partitioning
- **Requirements**: Proper connection pooling, read replicas for analytics

#### **Mem0 + Vector Database (AI-Native)**
- **Use Case**: Semantic search requirements, AI-native memory patterns
- **Benefits**: Built-in embeddings, relationship understanding, memory decay
- **Complexity**: Additional infrastructure (Qdrant/Pinecone), higher costs

### **Migration Strategy**

#### **Phase 1: Start with Comprehensive SQLite**
```sql
-- Single database with all tables for rapid prototyping
-- Include all foreign key relationships and constraints
-- Use WAL mode for better concurrent read performance
```

#### **Phase 2: Graduate to PostgreSQL**
```sql
-- Direct schema migration from SQLite to PostgreSQL
-- Add vector extensions for semantic search
-- Implement connection pooling and read replicas
-- Add partitioning for high-volume companies
```

#### **Phase 3: Hybrid with Mem0 for Semantic Features**
```python
# Use PostgreSQL for structured data, Mem0 for semantic memory
# Sync conversation content to Mem0 for semantic search
# Use Mem0 APIs for relationship discovery and context understanding
```

### **Data Privacy & Compliance Architecture**

#### **Company-Level Data Isolation**
- Every query must include company_id filter
- Use Row Level Security (RLS) in PostgreSQL for additional safety
- Implement application-level company context validation

#### **GDPR Compliance Features**
- Automated data export functionality for user data portability
- Right to be forgotten with proper anonymization
- Audit logging for all data access and modifications
- Configurable retention policies per company

#### **Audit Trail Requirements**
- Log all conversation access and modifications
- Track data export and deletion requests
- Monitor unusual access patterns
- Maintain compliance-ready audit reports

This comprehensive data architecture provides the foundation for a truly intelligent conversation backend that scales from startup to enterprise while maintaining privacy, compliance, and performance requirements.

---

**Next Steps**: This data architecture should be reviewed alongside the main PRD to ensure alignment with the MCP server implementation approach and overall system design.
