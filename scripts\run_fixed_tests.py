import subprocess
import sys

# Run WebSocket tests
print("Running WebSocket tests...")
result = subprocess.run([sys.executable, "-m", "pytest", "tests/test_websocket.py", "-xvs"], 
                       capture_output=True, text=True)
print(result.stdout)
print(result.stderr)

# Run API endpoint tests  
print("\nRunning API endpoint tests...")
result = subprocess.run([sys.executable, "-m", "pytest", "tests/test_api_endpoints.py", "-xvs", "-k", "not timeout"],
                       capture_output=True, text=True)
print(result.stdout)
print(result.stderr)
