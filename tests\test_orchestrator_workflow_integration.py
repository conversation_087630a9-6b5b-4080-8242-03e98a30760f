"""Integration tests for complete orchestrator workflow."""

import pytest
from unittest.mock import Async<PERSON>ock, MagicMock, patch
import async<PERSON>
from datetime import datetime

from agents.orchestrator import DynamicOrchestrator
from services.confidence_scorer import ConfidenceScorer
from services.mcp_registry import MCPServerRegistry
from services.tool_discovery import ToolDiscoveryService


class TestOrchestratorWorkflow:
    """Test complete workflow from request to response."""

    @pytest.fixture
    def setup_orchestrator(self):
        """Setup orchestrator with all dependencies."""
        # Mock MCP Registry
        mcp_registry = MagicMock(spec=MCPServerRegistry)
        mcp_registry.list_servers = AsyncMock(return_value=[{
            "name": "sage-intacct",
            "status": "connected",
            "tools_count": 2
        }])
        mcp_registry.get_server_tools = AsyncMock(return_value={
            "search_across_modules": {
                "name": "search_across_modules",
                "description": "Search across Intacct modules"
            },
            "get_financial_summary": {
                "name": "get_financial_summary",
                "description": "Get financial summary"
            }
        })
        
        
        # Mock Tool Discovery
        tool_discovery = MagicMock(spec=ToolDiscoveryService)
        tool_discovery.get_tool_catalog = AsyncMock(return_value={
            "by_category": {
                "Financial": [{
                    "name": "search_across_modules",
                    "full_name": "sage-intacct:search_across_modules",
                    "description": "Search across modules",
                    "category": "Financial",
                    "capabilities": ["search", "read"]
                }]
            },
            "total_tools": 1
        })
        tool_discovery.search_tools = AsyncMock(return_value=[{
            "name": "search_across_modules",
            "full_name": "sage-intacct:search_across_modules",
            "description": "Search across modules",
            "category": "Financial",
            "capabilities": ["search", "read"],
            "score": 0.95
        }])
        
        # Create orchestrator with mocked registry
        orchestrator = DynamicOrchestrator(registry=mcp_registry)
        
        # Mock the internal services
        orchestrator.discovery = tool_discovery
        
        return {
            "orchestrator": orchestrator,
            "mcp_registry": mcp_registry,
            "tool_discovery": tool_discovery
        }

    @pytest.mark.asyncio
    async def test_simple_search_workflow(self, setup_orchestrator):
        """Test simple search request workflow."""
        setup = setup_orchestrator
        orchestrator = setup["orchestrator"]
        mcp_registry = setup["mcp_registry"]
        
        # Mock the FastAgent execution
        with patch.object(orchestrator, '_execute_tool_via_agent') as mock_execute:
            # Configure mock to return search results
            mock_execute.return_value = {
                "results": [
                    {"id": "INV-001", "amount": 1000.00},
                    {"id": "INV-002", "amount": 2000.00}
                ]
            }
            
            # Execute request
            request = {
                "text": "Find invoices from last month",
                "context": {"user_id": "test123"}
            }
            
            result = await orchestrator.process(request)
            
            # Verify results
            assert "response" in result
            assert result["success"] is True
            assert "found" in result["response"].lower() or "invoice" in result["response"].lower()
            
            # Check metadata
            assert result["metadata"]["intent"] in ["ar_search", "invoice_search", "search"]
            assert result["metadata"]["confidence"] > 0.6
            
            # Verify tool was called
            mock_execute.assert_called()
            call_args = mock_execute.call_args[0]
            assert "search" in call_args[0].lower()  # tool name should contain search

    @pytest.mark.asyncio
    async def test_multi_agent_workflow(self, setup_orchestrator):
        """Test workflow involving multiple agents."""
        setup = setup_orchestrator
        orchestrator = setup["orchestrator"]
        agent_manager = setup["agent_manager"]
        
        # Create multiple mock agents
        intacct_agent = AsyncMock()
        intacct_agent.name = "intacct_agent"
        intacct_agent.business_system = "sage_intacct"
        intacct_agent.capabilities = ["financial_data"]
        intacct_agent.can_handle = MagicMock(return_value=True)
        intacct_agent.handle_request = AsyncMock(return_value={
            "success": True,
            "data": {"financial_summary": {"revenue": 100000}},
            "metadata": {"confidence": 0.9}
        })
        
        analytics_agent = AsyncMock()
        analytics_agent.name = "analytics_agent"
        analytics_agent.business_system = "analytics"
        analytics_agent.capabilities = ["reporting"]
        analytics_agent.can_handle = MagicMock(return_value=True)
        analytics_agent.handle_request = AsyncMock(return_value={
            "success": True,
            "data": {"report": "Financial report generated"},
            "metadata": {"confidence": 0.85}
        })
        
        agent_manager.list_agents = AsyncMock(return_value=[intacct_agent, analytics_agent])
        
        # Complex request requiring multiple agents
        request = {
            "query": "Generate financial report with revenue analysis",
            "context": {"require_analysis": True}
        }
        
        # Mock orchestrator's agent selection logic
        with patch.object(orchestrator, '_select_best_agent') as mock_select:
            # First call returns intacct_agent, second returns analytics_agent
            mock_select.side_effect = [intacct_agent, analytics_agent]
            
            result = await orchestrator.handle_request(request)
        
        # Both agents should have been called
        assert intacct_agent.handle_request.called
        assert analytics_agent.handle_request.called

    @pytest.mark.asyncio
    async def test_error_handling_workflow(self, setup_orchestrator):
        """Test error handling in workflow."""
        setup = setup_orchestrator
        orchestrator = setup["orchestrator"]
        mock_agent = setup["mock_agent"]
        
        # Configure agent to raise error
        mock_agent.handle_request.side_effect = Exception("Connection failed")
        
        request = {
            "query": "Get financial data",
            "context": {}
        }
        
        result = await orchestrator.handle_request(request)
        
        # Should handle error gracefully
        assert result["success"] is False
        assert "error" in result
        assert "Connection failed" in result["error"]

    @pytest.mark.asyncio
    async def test_fallback_workflow(self, setup_orchestrator):
        """Test fallback to different agent on failure."""
        setup = setup_orchestrator
        orchestrator = setup["orchestrator"]
        agent_manager = setup["agent_manager"]
        
        # Create primary and fallback agents
        primary_agent = AsyncMock()
        primary_agent.name = "primary_agent"
        primary_agent.capabilities = ["financial_data"]
        primary_agent.can_handle = MagicMock(return_value=True)
        primary_agent.handle_request = AsyncMock(side_effect=Exception("Primary failed"))
        
        fallback_agent = AsyncMock()
        fallback_agent.name = "fallback_agent"
        fallback_agent.capabilities = ["financial_data"]
        fallback_agent.can_handle = MagicMock(return_value=True)
        fallback_agent.handle_request = AsyncMock(return_value={
            "success": True,
            "data": {"source": "fallback"},
            "metadata": {"confidence": 0.7}
        })
        
        agent_manager.list_agents = AsyncMock(return_value=[primary_agent, fallback_agent])
        
        request = {
            "query": "Get financial data",
            "context": {"allow_fallback": True}
        }
        
        # Mock selection to try primary first, then fallback
        with patch.object(orchestrator, '_select_best_agent') as mock_select:
            mock_select.side_effect = [primary_agent, fallback_agent]
            
            result = await orchestrator.handle_request(request)
        
        # Should succeed with fallback
        assert result["success"] is True
        assert result["data"]["source"] == "fallback"
        assert primary_agent.handle_request.called
        assert fallback_agent.handle_request.called

    @pytest.mark.asyncio
    async def test_tool_mapping_workflow(self, setup_orchestrator):
        """Test tool name mapping in workflow."""
        setup = setup_orchestrator
        orchestrator = setup["orchestrator"]
        mock_agent = setup["mock_agent"]
        tool_discovery = setup["tool_discovery"]
        
        # Configure tool mapping
        tool_discovery.discover_tools.return_value = {
            "search_invoices": {
                "name": "search_invoices",
                "mcp_tool": "mcp__sage-intacct__search_across_modules",
                "mapped": True
            }
        }
        
        mock_agent.handle_request.return_value = {
            "success": True,
            "data": {"invoices": []},
            "metadata": {
                "tool_used": "search_invoices",
                "mcp_tool": "mcp__sage-intacct__search_across_modules"
            }
        }
        
        request = {
            "query": "Search for invoices",
            "tool": "search_invoices"
        }
        
        result = await orchestrator.handle_request(request)
        
        # Verify tool mapping was used
        assert result["success"] is True
        assert result["metadata"]["tool_used"] == "search_invoices"
        assert result["metadata"]["mcp_tool"] == "mcp__sage-intacct__search_across_modules"

    @pytest.mark.asyncio
    async def test_confidence_scoring_workflow(self, setup_orchestrator):
        """Test confidence scoring throughout workflow."""
        setup = setup_orchestrator
        orchestrator = setup["orchestrator"]
        mock_agent = setup["mock_agent"]
        
        # Configure response with various confidence indicators
        mock_agent.handle_request.return_value = {
            "success": True,
            "data": {
                "results": [{"id": 1}, {"id": 2}],
                "total": 2
            },
            "metadata": {
                "response_time": 0.5,
                "cache_hit": False,
                "data_completeness": 1.0
            }
        }
        
        request = {
            "query": "Get all active customers",
            "context": {"require_confidence": True}
        }
        
        result = await orchestrator.handle_request(request)
        
        # Should include confidence score
        assert result["success"] is True
        assert "confidence" in result["metadata"]
        assert 0 <= result["metadata"]["confidence"] <= 1.0

    @pytest.mark.asyncio
    async def test_parallel_execution_workflow(self, setup_orchestrator):
        """Test parallel execution of independent requests."""
        setup = setup_orchestrator
        orchestrator = setup["orchestrator"]
        mock_agent = setup["mock_agent"]
        
        # Configure responses
        async def delayed_response(request):
            await asyncio.sleep(0.1)  # Simulate processing time
            return {
                "success": True,
                "data": {"request_id": request.get("id", "unknown")},
                "metadata": {"timestamp": datetime.now().isoformat()}
            }
        
        mock_agent.handle_request.side_effect = delayed_response
        
        # Multiple independent requests
        requests = [
            {"id": "req1", "query": "Get invoices"},
            {"id": "req2", "query": "Get customers"},
            {"id": "req3", "query": "Get payments"}
        ]
        
        # Execute in parallel
        tasks = [orchestrator.handle_request(req) for req in requests]
        results = await asyncio.gather(*tasks)
        
        # All should succeed
        assert all(r["success"] for r in results)
        assert len(results) == 3
        
        # Verify they ran in parallel (total time should be ~0.1s, not 0.3s)
        assert mock_agent.handle_request.call_count == 3

    @pytest.mark.asyncio
    async def test_context_preservation_workflow(self, setup_orchestrator):
        """Test context preservation throughout workflow."""
        setup = setup_orchestrator
        orchestrator = setup["orchestrator"]
        mock_agent = setup["mock_agent"]
        
        # Capture context passed to agent
        captured_context = None
        async def capture_context(request):
            nonlocal captured_context
            captured_context = request.get("context", {})
            return {
                "success": True,
                "data": {},
                "metadata": {"context_preserved": True}
            }
        
        mock_agent.handle_request.side_effect = capture_context
        
        # Request with rich context
        request = {
            "query": "Get data",
            "context": {
                "user_id": "user123",
                "session_id": "session456",
                "permissions": ["read", "write"],
                "preferences": {"format": "json"}
            }
        }
        
        result = await orchestrator.handle_request(request)
        
        # Context should be preserved
        assert result["success"] is True
        assert captured_context == request["context"]
        assert captured_context["user_id"] == "user123"
        assert captured_context["session_id"] == "session456"
        assert "permissions" in captured_context
        assert "preferences" in captured_context


# Run tests
if __name__ == "__main__":
    pytest.main([__file__, "-v"])