# Guardrails Architecture for FastMCP-based Orchestrator

## Overview

Guardrails ensure safe, reliable, and controlled execution of AI operations across multiple MCP servers. Here's a comprehensive guardrails system for your architecture that builds upon FastMCP's built-in security features.

## What FastMCP Provides (Out-of-the-Box)

### 1. **Authentication** ✅
- Bearer token authentication with JWT validation
- OAuth 2.1 support
- Public key verification (no shared secrets)

### 2. **Parameter Type Validation** ✅
- Automatic validation through Python type hints
- Pydantic model support for complex inputs
- Field constraints (min/max, patterns, etc.)

### 3. **Type Safety** ✅
- JSON schema generation for LLMs
- Type coercion and error messages
- Automatic parameter validation

## What You Need to Build (Custom Guardrails)

### Required Security Layers:
- ❌ **Rate Limiting** - Prevent abuse
- ❌ **Role-Based Access Control** - Tool-level permissions
- ❌ **Risk Assessment** - Classify dangerous operations
- ❌ **Output Filtering** - Redact sensitive data
- ❌ **Audit Logging** - Compliance trail
- ❌ **Execution Monitoring** - Real-time oversight
- ❌ **Business Rule Validation** - Beyond type checking

## Guardrail Layers

```
┌─────────────────────────────────────────────────────────┐
│                   Request Flow                           │
├─────────────────────────────────────────────────────────┤
│  1. Input Validation & Sanitization                      │
│  2. User Authorization & Rate Limiting                   │
│  3. Intent Classification & Risk Assessment              │
│  4. Tool Selection Guardrails                            │
│  5. Pre-execution Validation                             │
│  6. Execution Monitoring                                 │
│  7. Output Validation & Filtering                        │
│  8. Response Sanitization                                │
└─────────────────────────────────────────────────────────┘
```

## Implementation

### 1. **Leverage FastMCP's Built-in Authentication**

```python
from fastmcp import Client
from fastmcp.client.transports import SSETransport
from fastmcp.server.auth import BearerAuthProvider

# Server-side: Configure authentication
auth = BearerAuthProvider(
    jwks_uri="https://your-auth-server.com/.well-known/jwks.json",
    issuer="https://your-auth-server.com/",
    audience="ai-workspace"
)

# Client-side: Use authenticated transport
transport = SSETransport(
    url="http://localhost:8001/sse",
    headers={"Authorization": f"Bearer {user_token}"}
)
client = Client(transport)
```

### 2. **Extend with Custom Input Validation Layer**

```python
from pydantic import BaseModel, validator, Field
from typing import Annotated
import re

class InputValidator:
    """Validates and sanitizes user inputs beyond FastMCP's type checking"""
    
    # Dangerous patterns to block (FastMCP doesn't check these)
    BLOCKED_PATTERNS = [
        r'(?i)(drop|delete|truncate)\s+table',
        r'(?i)exec(ute)?\s*\(',
        r'<script[^>]*>',
        r'javascript:',
        r'(?i)union\s+select',
    ]
    
    # Input size limits
    MAX_MESSAGE_LENGTH = 10000
    MAX_CONTEXT_SIZE = 50000
    
    @classmethod
    def validate_input(cls, message: str, context: Optional[Dict] = None) -> Tuple[bool, str]:
        """Validate user input for safety beyond type validation"""
        
        # Check length
        if len(message) > cls.MAX_MESSAGE_LENGTH:
            return False, f"Message too long (max {cls.MAX_MESSAGE_LENGTH} chars)"
        
        # Check for dangerous patterns
        for pattern in cls.BLOCKED_PATTERNS:
            if re.search(pattern, message):
                return False, "Input contains potentially unsafe content"
        
        # Validate context size
        if context and len(str(context)) > cls.MAX_CONTEXT_SIZE:
            return False, "Context data too large"
        
        return True, "Valid"

# Use with FastMCP's Pydantic validation
class PaymentRequest(BaseModel):
    """FastMCP will validate types, we add business rules"""
    amount: Annotated[float, Field(gt=0, le=1000000)]  # FastMCP validates this
    account_id: Annotated[str, Field(pattern=r"^[A-Z]{2}\d{8}$")]
    
    @validator('amount')
    def validate_business_rules(cls, v, values):
        """Additional business validation beyond FastMCP's type checking"""
        if v > 10000 and not values.get('approval_code'):
            raise ValueError("Amounts over $10,000 require approval code")
        return v
```

### 3. **User Authorization & Rate Limiting (Built on FastMCP Auth)**

```python
from datetime import datetime, timedelta
from collections import defaultdict
import asyncio
import jwt

class AuthorizationGuard:
    """Manages user permissions and rate limiting beyond FastMCP's authentication"""
    
    def __init__(self):
        self.user_permissions = {}  # user_id -> set of allowed operations
        self.rate_limiter = RateLimiter()
        
    async def extract_user_from_token(self, auth_header: str) -> Dict:
        """Extract user info from FastMCP-validated JWT"""
        # FastMCP has already validated the token signature
        # We just need to extract claims
        token = auth_header.replace("Bearer ", "")
        try:
            # Decode without verification (FastMCP already verified)
            claims = jwt.decode(token, options={"verify_signature": False})
            return {
                "user_id": claims.get("sub"),
                "roles": claims.get("roles", []),
                "permissions": claims.get("permissions", [])
            }
        except:
            return None
        
    async def check_authorization(self, user_info: Dict, operation: str) -> bool:
        """Check if user is authorized for operation"""
        
        # Check rate limits first
        if not await self.rate_limiter.check_limit(user_info['user_id']):
            raise RateLimitExceeded(f"User {user_info['user_id']} exceeded rate limit")
        
        # Check permissions from JWT claims
        user_perms = set(user_info.get('permissions', []))
        
        # Map operation to required permission
        required_perms = self._get_required_permissions(operation)
        
        return required_perms.issubset(user_perms)
    
    def _get_required_permissions(self, operation: str) -> Set[str]:
        """Map operations to required permissions"""
        permission_map = {
            'financial_read': {'read:financial'},
            'financial_write': {'write:financial', 'approve:transactions'},
            'system_config': {'admin:system'},
            'data_export': {'export:data'},
        }
        return permission_map.get(operation, {'read:basic'})


class RateLimiter:
    """Token bucket rate limiter"""
    
    def __init__(self):
        self.buckets = defaultdict(lambda: {
            'tokens': 100,
            'last_refill': datetime.now(),
            'max_tokens': 100,
            'refill_rate': 10  # tokens per minute
        })
        
    async def check_limit(self, user_id: str) -> bool:
        """Check if user has tokens available"""
        bucket = self.buckets[user_id]
        
        # Refill tokens
        now = datetime.now()
        time_passed = (now - bucket['last_refill']).total_seconds() / 60
        tokens_to_add = int(time_passed * bucket['refill_rate'])
        
        bucket['tokens'] = min(
            bucket['max_tokens'],
            bucket['tokens'] + tokens_to_add
        )
        bucket['last_refill'] = now
        
        # Check if tokens available
        if bucket['tokens'] > 0:
            bucket['tokens'] -= 1
            return True
        
        return False
```

### 3. **Risk Assessment**

```python
from enum import Enum

class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class RiskAssessor:
    """Assess risk level of operations"""
    
    # High-risk operations that need extra validation
    HIGH_RISK_OPERATIONS = {
        'delete', 'remove', 'drop', 'truncate',
        'payment', 'transfer', 'approve',
        'close_period', 'modify_locked',
    }
    
    # Operations affecting multiple records
    BULK_OPERATIONS = {
        'batch', 'bulk', 'all', 'mass',
    }
    
    @classmethod
    def assess_risk(cls, intent: str, tools: List[str], context: Dict) -> RiskLevel:
        """Assess risk level of intended operation"""
        
        risk_score = 0
        
        # Check for high-risk keywords
        intent_lower = intent.lower()
        for keyword in cls.HIGH_RISK_OPERATIONS:
            if keyword in intent_lower:
                risk_score += 3
        
        # Check for bulk operations
        for keyword in cls.BULK_OPERATIONS:
            if keyword in intent_lower:
                risk_score += 2
        
        # Check tool risk levels
        for tool in tools:
            tool_risk = cls._get_tool_risk(tool)
            risk_score += tool_risk
        
        # Assess based on score
        if risk_score >= 8:
            return RiskLevel.CRITICAL
        elif risk_score >= 5:
            return RiskLevel.HIGH
        elif risk_score >= 2:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    @staticmethod
    def _get_tool_risk(tool_name: str) -> int:
        """Get risk score for specific tool"""
        tool_risk_map = {
            'execute_payment': 5,
            'delete_records': 4,
            'modify_gl_entry': 3,
            'close_period': 4,
            'export_all_data': 2,
            'read_balance': 0,
        }
        
        # Extract tool name without server prefix
        if ':' in tool_name:
            _, tool = tool_name.split(':', 1)
        else:
            tool = tool_name
            
        return tool_risk_map.get(tool, 1)
```

### 4. **Tool Execution Guardrails**

```python
class ExecutionGuard:
    """Guards around tool execution"""
    
    def __init__(self):
        self.blocked_tools = set()  # Tools that should never be executed
        self.restricted_tools = {}  # tool -> restriction function
        self.execution_limits = {}  # tool -> max executions per time period
        
    async def validate_tool_execution(
        self, 
        tool_name: str, 
        parameters: Dict[str, Any],
        user_id: str,
        risk_level: RiskLevel
    ) -> Tuple[bool, Optional[str]]:
        """Validate if tool execution should proceed"""
        
        # Check if tool is blocked
        if tool_name in self.blocked_tools:
            return False, f"Tool {tool_name} is currently blocked"
        
        # Check restrictions
        if tool_name in self.restricted_tools:
            restriction_func = self.restricted_tools[tool_name]
            allowed, reason = await restriction_func(parameters, user_id)
            if not allowed:
                return False, reason
        
        # Check parameter validation
        valid, reason = self._validate_parameters(tool_name, parameters)
        if not valid:
            return False, reason
        
        # Check risk-based restrictions
        if risk_level == RiskLevel.CRITICAL:
            return False, "Critical risk operations require manual approval"
        
        # Check execution limits
        if not self._check_execution_limit(tool_name, user_id):
            return False, f"Execution limit exceeded for {tool_name}"
        
        return True, None
    
    def _validate_parameters(self, tool_name: str, parameters: Dict) -> Tuple[bool, str]:
        """Validate tool parameters"""
        
        # Example validations
        if 'amount' in parameters:
            amount = parameters.get('amount', 0)
            if amount > 1000000:  # $1M limit
                return False, "Amount exceeds maximum allowed"
        
        if 'date_range' in parameters:
            # Validate date range is reasonable
            start = parameters.get('start_date')
            end = parameters.get('end_date')
            # Add date validation logic
        
        return True, "Valid"
```

### 5. **Output Validation**

```python
class OutputValidator:
    """Validates and sanitizes tool outputs"""
    
    # Sensitive data patterns
    SENSITIVE_PATTERNS = [
        (r'\b\d{3}-\d{2}-\d{4}\b', '[SSN REDACTED]'),  # SSN
        (r'\b\d{16}\b', '[CARD REDACTED]'),  # Credit card
        (r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', '[EMAIL REDACTED]'),
    ]
    
    @classmethod
    def validate_output(cls, output: Any, user_permissions: Set[str]) -> Any:
        """Validate and sanitize output"""
        
        # Convert to string for pattern matching
        output_str = str(output)
        
        # Redact sensitive data if user lacks permission
        if 'view:pii' not in user_permissions:
            for pattern, replacement in cls.SENSITIVE_PATTERNS:
                output_str = re.sub(pattern, replacement, output_str)
        
        # Check output size
        if len(output_str) > 1000000:  # 1MB limit
            return "Output too large to display"
        
        # Return sanitized output
        return output_str if isinstance(output, str) else output
```

### 7. **Orchestrator Integration with FastMCP**

```python
from fastmcp import Client
from fastmcp.client.transports import SSETransport, StreamableHttpTransport

class GuardedOrchestrator(DynamicOrchestrator):
    """Orchestrator with integrated guardrails using FastMCP clients"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.input_validator = InputValidator()
        self.auth_guard = AuthorizationGuard()
        self.risk_assessor = RiskAssessor()
        self.execution_guard = ExecutionGuard()
        self.output_validator = OutputValidator()
        
    async def _create_guarded_client(self, server_config: Dict, user_token: str) -> Client:
        """Create FastMCP client with authentication"""
        # Choose transport based on server config
        if server_config['transport'] == 'sse':
            transport = SSETransport(
                url=server_config['url'],
                headers={"Authorization": f"Bearer {user_token}"}
            )
        else:
            transport = StreamableHttpTransport(
                url=server_config['url'],
                headers={"Authorization": f"Bearer {user_token}"}
            )
            
        return Client(transport)
        
    async def process(self, message: Union[str, Dict], auth_header: str) -> Dict[str, Any]:
        """Process request with full guardrail protection"""
        
        try:
            # 1. Input validation (beyond FastMCP's type checking)
            text = message if isinstance(message, str) else message.get('text', '')
            valid, reason = self.input_validator.validate_input(text)
            if not valid:
                return self._error_response(f"Invalid input: {reason}")
            
            # 2. Extract user from FastMCP-validated token
            user_info = await self.auth_guard.extract_user_from_token(auth_header)
            if not user_info:
                return self._error_response("Invalid authentication")
                
            # 3. Authorization check
            if not await self.auth_guard.check_authorization(user_info, 'basic_access'):
                return self._error_response("Unauthorized")
            
            # 4. Risk assessment
            intent_info = await self._detect_intent(text)
            selected_tools = await self._select_tools(intent_info['primary_intent'], text)
            risk_level = self.risk_assessor.assess_risk(text, selected_tools, user_info)
            
            # 5. High-risk approval
            if risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                approval = await self._request_approval(user_info['user_id'], text, risk_level)
                if not approval:
                    return self._error_response("Operation requires approval")
            
            # 6. Execute with monitoring via FastMCP
            result = await self._execute_with_guards(
                text, 
                selected_tools, 
                user_info, 
                risk_level,
                auth_header  # Pass through for FastMCP client
            )
            
            # 7. Output validation
            validated_output = self.output_validator.validate_output(
                result['response'], 
                set(user_info.get('permissions', []))
            )
            
            result['response'] = validated_output
            return result
            
        except Exception as e:
            logger.error(f"Guarded execution failed: {str(e)}", exc_info=True)
            return self._error_response("An error occurred during processing")
            
    async def _execute_with_guards(
        self, 
        message: str, 
        tools: List[str], 
        user_info: Dict,
        risk_level: RiskLevel,
        auth_header: str
    ) -> Dict:
        """Execute tools with guardrails via FastMCP clients"""
        
        for tool_full_name in tools:
            server_name, tool_name = tool_full_name.split(':', 1)
            
            # Get server config
            server_config = await self.registry.get_server_config(server_name)
            
            # Create authenticated FastMCP client
            client = await self._create_guarded_client(server_config, auth_header)
            
            async with client:
                # Pre-execution validation (business rules beyond FastMCP's type validation)
                params = self._extract_parameters(message, tool_name)
                valid, reason = await self.execution_guard.validate_tool_execution(
                    tool_full_name, params, user_info['user_id'], risk_level
                )
                if not valid:
                    raise GuardrailViolation(reason)
                
                # Execute via FastMCP (which handles its own type validation)
                result = await client.call_tool(tool_name, params)
                
                # Audit logging
                await self._audit_log(user_info, tool_full_name, params, result)
                
                return {"response": result, "tool": tool_full_name}
```

### 8. **Configuration & Policies**

```yaml
# config/guardrails.yaml - Custom guardrail policies
guardrails:
  # Input validation (custom)
  input_validation:
    max_message_length: 10000
    blocked_patterns:
      - "(?i)drop\\s+table"
      - "(?i)exec(ute)?\\s*\\("
      
  # Rate limits (custom)
  rate_limits:
    default:
      max_tokens: 100
      refill_rate: 10  # per minute
    premium:
      max_tokens: 1000
      refill_rate: 100
      
  # Risk thresholds (custom)
  risk_thresholds:
    auto_approve: low
    require_confirmation: medium
    require_approval: high
    block: critical
    
  # Tool restrictions (custom business rules)
  tool_restrictions:
    sage-intacct:execute_payment:
      max_amount: 1000000
      require_approval_above: 10000
      
    sage-intacct:delete_records:
      blocked: true
      
    sage-intacct:close_period:
      allowed_roles: ["admin", "controller"]

# fastagent.config.yaml - FastMCP authentication config
mcp:
  servers:
    sage-intacct:
      url: "http://localhost:8001/sse"
      transport: "sse"
      # FastMCP handles auth at transport level
      auth:
        type: "bearer"
        jwks_uri: "https://auth.company.com/.well-known/jwks.json"
        audience: "sage-intacct-mcp"
        issuer: "https://auth.company.com/"
```

## Best Practices

### 1. **Layered Defense**
- Multiple validation layers
- Fail-safe defaults
- Gradual permission escalation

### 2. **Audit Trail**
```python
@dataclass
class AuditEntry:
    timestamp: datetime
    user_id: str
    action: str
    tool: str
    parameters: Dict
    risk_level: RiskLevel
    result: str
    approved_by: Optional[str] = None
```

### 3. **Circuit Breakers**
```python
class CircuitBreaker:
    """Prevent cascading failures"""
    def __init__(self, threshold: int = 5, timeout: int = 60):
        self.failure_count = 0
        self.threshold = threshold
        self.timeout = timeout
        self.last_failure = None
        self.is_open = False
```

### 4. **Monitoring & Alerts**
```python
class GuardrailMonitor:
    """Monitor guardrail effectiveness"""
    async def track_metrics(self):
        metrics = {
            'blocked_requests': 0,
            'high_risk_operations': 0,
            'rate_limit_violations': 0,
            'output_redactions': 0,
        }
```

## Integration with FastMCP Architecture

The guardrails wrap around your FastMCP client calls in a clean, layered approach:

```python
# Architecture Flow:
User Request
    ↓
Input Validation (Custom)
    ↓
Authentication (FastMCP Bearer/OAuth)
    ↓
Authorization (Custom RBAC)
    ↓
Risk Assessment (Custom)
    ↓
Parameter Validation (FastMCP Pydantic + Custom Rules)
    ↓
Tool Execution (FastMCP Client)
    ↓
Output Filtering (Custom)
    ↓
Audit Logging (Custom)
    ↓
Response to User

# Example implementation:
async def execute_tool_with_guards(self, tool_name: str, params: Dict, user_token: str):
    # 1. FastMCP handles authentication via transport
    client = Client(
        SSETransport(url, headers={"Authorization": f"Bearer {user_token}"})
    )
    
    # 2. Custom pre-execution validation
    valid, reason = await self.execution_guard.validate_tool_execution(
        tool_name, params, user_id, risk_level
    )
    if not valid:
        raise GuardrailViolation(reason)
    
    # 3. Execute via FastMCP (with its type validation)
    async with client:
        result = await client.call_tool(tool_name, params)
    
    # 4. Custom post-execution filtering
    return self.output_validator.validate_output(result)
```

## Summary: FastMCP + Custom Guardrails

### Leverage FastMCP's Built-in Features:

1. **Authentication** ✅
   - Use FastMCP's Bearer/OAuth authentication
   - Extract user info from validated JWTs
   - Pass auth headers to MCP servers

2. **Type Validation** ✅
   - Define Pydantic models for complex inputs
   - Use Field constraints for basic validation
   - Let FastMCP handle type checking

3. **Error Handling** ✅
   - FastMCP provides protocol-level errors
   - Build business errors on top

### Build Custom Guardrails For:

1. **Business Rules** 🛡️
   - Validation beyond type checking
   - Cross-field validations
   - Business-specific constraints

2. **Access Control** 🔐
   - Tool-level permissions
   - Role-based access
   - Dynamic authorization

3. **Risk Management** ⚠️
   - Operation risk scoring
   - Approval workflows
   - Execution limits

4. **Monitoring & Compliance** 📊
   - Rate limiting
   - Audit logging
   - Output filtering

This comprehensive guardrail system provides:
- ✅ **Input protection** against malicious inputs
- ✅ **Access control** with role-based permissions
- ✅ **Risk assessment** for dangerous operations
- ✅ **Rate limiting** to prevent abuse
- ✅ **Output filtering** for sensitive data
- ✅ **Audit trails** for compliance
- ✅ **Circuit breakers** for system protection

All while leveraging FastMCP's excellent authentication and validation capabilities!