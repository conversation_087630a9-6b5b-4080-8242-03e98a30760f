# Centralized Model Configuration
# This file manages all AI model assignments across the application

# Current environment (set via ENV var or detect automatically)
# export APP_ENV=production
current_env: ${APP_ENV:production}

# Model configurations per environment
models:
  development:
    default: "gpt-4.1"
    orchestrator: "gpt-4.1"    # Override for specific agents
    analysis_agent: "gpt-4.1"        # Maybe you want better analysis even in dev
    
  staging:
    default: "gpt-4.1"
    orchestrator: "gpt-4.1"
    analysis_agent: "gpt-4.1"
    
  production:
    default: "gpt-4.1"
    orchestrator: "gpt-4.1"
    analysis_agent: "o3-mini"        # Premium model for analysis
    validation_agent: "gpt-4.1" # Save costs on simple validation

# Shared settings
settings:
  temperature: 0.7
  max_tokens: 2000
  fallback: "gpt-4.1"