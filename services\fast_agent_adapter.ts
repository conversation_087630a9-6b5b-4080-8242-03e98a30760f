/**
 * FastAgent Adapter for assistant-ui
 * 
 * This adapter integrates the FastAgent client with the assistant-ui framework,
 * allowing the chat interface to communicate with agents running in the 
 * decoupled service.
 */

import { Thread, Message, TextContentPart, ToolCallContentPart } from '@assistant-ui/react';
import { FastAgentClient, AgentResponse } from './fast_agent_client';

export interface FastAgentAdapterConfig {
  agentId: string;
  baseUrl?: string;
  wsUrl?: string;
  useWebSocket?: boolean;
  context?: Record<string, any>;
}

export class FastAgentAdapter {
  private client: FastAgentClient;
  private agentId: string;
  private useWebSocket: boolean;
  private context: Record<string, any>;

  constructor(config: FastAgentAdapterConfig) {
    this.client = new FastAgentClient(
      config.baseUrl || 'http://localhost:8000',
      config.wsUrl || 'ws://localhost:8000'
    );
    this.agentId = config.agentId;
    this.useWebSocket = config.useWebSocket || false;
    this.context = config.context || {};
  }

  /**
   * Initialize the adapter (connect WebSocket if needed)
   */
  async initialize(): Promise<void> {
    if (this.useWebSocket) {
      await this.client.connectWebSocket();
    }
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    if (this.useWebSocket) {
      await this.client.disconnectWebSocket();
    }
  }

  /**
   * Send a message to the agent and handle the response
   */
  async sendMessage(
    message: string,
    thread: Thread,
    onUpdate?: (content: string) => void
  ): Promise<Message> {
    try {
      // Add user message to thread
      const userMessage: Message = {
        id: this.generateMessageId(),
        role: 'user',
        content: [{ type: 'text', text: message }],
        createdAt: new Date(),
      };

      // Send to agent
      const response = this.useWebSocket
        ? await this.client.sendMessageWS(
            this.agentId,
            message,
            this.context,
            userMessage.id
          )
        : await this.client.sendMessage(
            this.agentId,
            message,
            this.context,
            userMessage.id
          );

      // Convert response to assistant-ui format
      return this.convertToAssistantMessage(response);
    } catch (error) {
      console.error('Error sending message to agent:', error);
      throw error;
    }
  }

  /**
   * Stream messages to the agent (WebSocket only)
   */
  async *streamMessage(
    message: string,
    thread: Thread
  ): AsyncGenerator<Partial<Message>, Message, unknown> {
    if (!this.useWebSocket) {
      throw new Error('Streaming requires WebSocket connection');
    }

    try {
      const userMessageId = this.generateMessageId();
      
      // Send initial message
      const response = await this.client.sendMessageWS(
        this.agentId,
        message,
        this.context,
        userMessageId
      );

      // For now, return the complete response
      // In the future, this could be enhanced to support true streaming
      const assistantMessage = this.convertToAssistantMessage(response);
      
      yield {
        id: assistantMessage.id,
        role: 'assistant',
        content: [],
        createdAt: assistantMessage.createdAt,
      };

      return assistantMessage;
    } catch (error) {
      console.error('Error streaming message:', error);
      throw error;
    }
  }

  /**
   * Get agent information
   */
  async getAgentInfo() {
    return this.client.getAgentStatus(this.agentId);
  }

  /**
   * Get conversation history
   */
  async getHistory(limit: number = 50) {
    const history = await this.client.getAgentHistory(this.agentId, limit);
    // Convert history to assistant-ui format if needed
    return history;
  }

  /**
   * Reset agent state
   */
  async resetAgent() {
    return this.client.resetAgent(this.agentId);
  }

  // Helper methods

  private convertToAssistantMessage(response: AgentResponse): Message {
    const content: (TextContentPart | ToolCallContentPart)[] = [];

    // Handle different response types
    if (typeof response.response === 'string') {
      content.push({
        type: 'text',
        text: response.response,
      });
    } else if (Array.isArray(response.response)) {
      // Handle structured content
      response.response.forEach((item) => {
        if (item.type === 'text') {
          content.push({
            type: 'text',
            text: item.text || '',
          });
        } else if (item.type === 'tool-call') {
          content.push({
            type: 'tool-call',
            toolCallId: item.toolCallId || '',
            toolName: item.toolName || '',
            args: item.args || {},
          } as ToolCallContentPart);
        }
      });
    } else {
      // Fallback for other response types
      content.push({
        type: 'text',
        text: JSON.stringify(response.response, null, 2),
      });
    }

    return {
      id: response.correlation_id || this.generateMessageId(),
      role: 'assistant',
      content,
      createdAt: new Date(response.timestamp),
      metadata: {
        agentId: response.agent_id,
        status: response.status,
        error: response.error,
        ...response.metadata,
      },
    };
  }

  private generateMessageId(): string {
    return `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Factory function for creating adapters
export function createFastAgentAdapter(
  agentId: string,
  options?: Partial<FastAgentAdapterConfig>
): FastAgentAdapter {
  return new FastAgentAdapter({
    agentId,
    ...options,
  });
}

// React hook for using the adapter
export function useFastAgentAdapter(
  agentId: string,
  options?: Partial<FastAgentAdapterConfig>
): FastAgentAdapter {
  const [adapter] = React.useState(() => createFastAgentAdapter(agentId, options));

  React.useEffect(() => {
    adapter.initialize();
    return () => {
      adapter.cleanup();
    };
  }, [adapter]);

  return adapter;
}
