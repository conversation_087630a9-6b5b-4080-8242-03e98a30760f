"""
Centralized Model Configuration

This module provides a single source of truth for AI model configurations
across all agents in the system.
"""

import os
import yaml
from typing import Optional, Dict, Any
from pathlib import Path
try:
    import structlog
except ImportError:
    from services.mock_logger import structlog

logger = structlog.get_logger(__name__)


class ModelConfig:
    """Centralized model configuration manager"""
    
    _instance = None
    _config_loaded = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._config_loaded:
            self._load_configuration()
            self._config_loaded = True
    
    def _load_configuration(self):
        """Load model configuration from YAML file"""
        config_path = Path(__file__).parent / "models.yaml"
        
        try:
            with open(config_path, 'r') as f:
                config_str = f.read()
                
                # Replace environment variables
                config_str = os.path.expandvars(config_str)
                
                self.config = yaml.safe_load(config_str)
                
                # Get current environment
                self.current_env = os.getenv('APP_ENV', 'production')
                
                # Override if specified in config
                if 'current_env' in self.config:
                    env_value = self.config['current_env']
                    # Handle ${APP_ENV:production} format
                    if env_value.startswith('${') and env_value.endswith('}'):
                        var_default = env_value[2:-1].split(':', 1)
                        var_name = var_default[0]
                        default_val = var_default[1] if len(var_default) > 1 else 'production'
                        self.current_env = os.getenv(var_name, default_val)
                    else:
                        self.current_env = env_value
                
                logger.info(f"Model configuration loaded for environment: {self.current_env}")
                
        except Exception as e:
            logger.error(f"Failed to load model configuration: {e}")
            # Fallback configuration
            self.config = {
                'models': {
                    'production': {
                        'default': 'gpt-4.1'
                    }
                },
                'settings': {
                    'temperature': 0.7,
                    'max_tokens': 2000,
                    'fallback': 'gpt-3.5-turbo'
                }
            }
            self.current_env = 'production'
    
    def get_model(self, agent_name: Optional[str] = None) -> str:
        """
        Get the model for a specific agent or the default model.
        
        Args:
            agent_name: Name of the agent (e.g., 'orchestrator', 'gl_agent')
                       If None, returns the default model for current environment
        
        Returns:
            Model name string (e.g., 'gpt-4.1')
        """
        try:
            env_models = self.config['models'].get(self.current_env, {})
            
            if agent_name:
                # Check for agent-specific override
                model = env_models.get(agent_name)
                if model:
                    return model
            
            # Return environment default
            return env_models.get('default', 'gpt-4.1')
            
        except Exception as e:
            logger.error(f"Error getting model config: {e}")
            return self.config['settings'].get('fallback', 'gpt-3.5-turbo')
    
    def get_settings(self) -> Dict[str, Any]:
        """Get shared model settings"""
        return self.config.get('settings', {
            'temperature': 0.7,
            'max_tokens': 2000,
            'fallback': 'gpt-3.5-turbo'
        })
    
    def get_temperature(self) -> float:
        """Get temperature setting"""
        return self.get_settings().get('temperature', 0.7)
    
    def get_max_tokens(self) -> int:
        """Get max tokens setting"""
        return self.get_settings().get('max_tokens', 2000)
    
    def get_fallback_model(self) -> str:
        """Get fallback model for error cases"""
        return self.get_settings().get('fallback', 'gpt-3.5-turbo')
    
    def list_all_models(self) -> Dict[str, Dict[str, str]]:
        """List all configured models for all environments"""
        return self.config.get('models', {})
    
    def get_current_environment(self) -> str:
        """Get the current environment name"""
        return self.current_env


# Singleton instance
_model_config = None


def get_model_config() -> ModelConfig:
    """Get the singleton ModelConfig instance"""
    global _model_config
    if _model_config is None:
        _model_config = ModelConfig()
    return _model_config


# Convenience functions
def get_model_for_agent(agent_name: Optional[str] = None) -> str:
    """Convenience function to get model for an agent"""
    return get_model_config().get_model(agent_name)


def get_default_model() -> str:
    """Get the default model for current environment"""
    return get_model_config().get_model()