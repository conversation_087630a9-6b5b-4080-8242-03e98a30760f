#!/usr/bin/env python3
"""
Reindex tools using the enhanced embedding service with OpenAI/local fallback
"""

import asyncio
import sys
import os
import time
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.vector_db.factory import VectorStoreFactory
from services.embedding_service import ToolEmbedder
from services.tool_indexing import ToolIndexingPipeline
from services.tool_discovery import ToolDiscoveryService
from services.mcp_registry import MCPServerRegistry
from config.mcp_config import MCPConfiguration
import structlog

logger = structlog.get_logger()


async def reindex_with_enhanced_embeddings():
    """Reindex all tools using the enhanced embedding service."""
    
    print("🚀 ENHANCED EMBEDDING REINDEXING")
    print("=" * 50)
    
    try:
        # 1. Check available providers
        print("\n1. Checking embedding providers...")
        
        openai_available = os.getenv("OPENAI_API_KEY") is not None
        try:
            from sentence_transformers import SentenceTransformer
            local_available = True
        except ImportError:
            local_available = False
        
        print(f"   OpenAI API: {'✅ Available' if openai_available else '❌ No API key'}")
        print(f"   Local models: {'✅ Available' if local_available else '❌ Not installed'}")
        
        if not openai_available and not local_available:
            print("❌ No embedding providers available!")
            return
        
        # 2. Initialize enhanced embedder (auto-selects best provider)
        print("\n2. Initializing enhanced embedder...")
        embedder = ToolEmbedder(provider="auto")
        
        print(f"   Selected provider: {embedder.active_provider}")
        print(f"   Model: {embedder.model_name}")
        print(f"   Dimension: {embedder.dimension}")
        
        # 3. Initialize vector database with dynamic dimension
        print("\n3. Initializing vector database...")
        db_config = {
            "backend": "sqlite",
            "db_path": "data/ai_workspace_vectors.db",
            "embedding_dimension": embedder.dimension
        }
        
        vector_store = VectorStoreFactory.create(db_config)
        await vector_store.initialize()
        
        print(f"   Database: {db_config['db_path']}")
        print(f"   Embedding dimension: {embedder.dimension}")
        
        # 4. Initialize tool discovery
        print("\n4. Discovering tools...")
        config = MCPConfiguration()
        registry = MCPServerRegistry()
        discovery = ToolDiscoveryService(registry)
        
        # Get all tools
        all_tools = await discovery.get_all_tools()
        print(f"   Found {len(all_tools)} tools from {len(await registry.list_servers())} servers")
        
        # 5. Clear existing data and reindex
        print("\n5. Clearing existing data...")
        await vector_store.clear()
        
        print("\n6. Reindexing with enhanced embeddings...")
        indexing_pipeline = ToolIndexingPipeline(
            vector_store=vector_store,
            embedder=embedder
        )
        
        start_time = time.time()
        stats = await indexing_pipeline.index_all_tools()
        total_time = time.time() - start_time
        
        print(f"\n✅ Reindexing completed!")
        print(f"   Total time: {total_time:.2f} seconds")
        print(f"   Tools indexed: {stats['indexed_count']}")
        print(f"   Failed: {stats['failed_count']}")
        print(f"   Speed: {stats['indexed_count'] / total_time:.2f} tools/second")
        
        # 6. Test search functionality
        print("\n7. Testing search functionality...")
        
        test_queries = [
            "financial reports",
            "user authentication", 
            "bank accounts"
        ]
        
        for query in test_queries:
            try:
                query_embedding = await embedder.encode(query)
                results = await vector_store.search(query_embedding, k=3, threshold=0.5)
                print(f"   '{query}' -> {len(results)} results")
                if results:
                    top_result = results[0]
                    print(f"     Top: {top_result[2].get('tool_name', 'unknown')} (score: {top_result[1]:.3f})")
            except Exception as e:
                print(f"   '{query}' -> Error: {e}")
        
        # 7. Performance comparison
        print(f"\n📊 Performance Analysis")
        print("=" * 30)
        
        if embedder.active_provider == "openai":
            expected_local_time = 165 * (total_time / stats['indexed_count']) * 60  # Rough estimate
            print(f"OpenAI embedding time: {total_time:.1f}s")
            print(f"Estimated local time: {expected_local_time:.1f}s")
            print(f"Speedup: {expected_local_time / total_time:.1f}x faster")
        else:
            print(f"Local embedding time: {total_time:.1f}s")
            print(f"With OpenAI, this would be ~3-5 seconds")
        
        print(f"\n🎯 Recommendation:")
        if not openai_available:
            print("   Consider adding OPENAI_API_KEY for 30-50x faster indexing")
            print("   Cost for 165 tools: ~$0.02 (negligible)")
        else:
            print("   ✅ Using optimal embedding provider")
        
    except Exception as e:
        logger.error("Reindexing failed", error=str(e))
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(reindex_with_enhanced_embeddings())
