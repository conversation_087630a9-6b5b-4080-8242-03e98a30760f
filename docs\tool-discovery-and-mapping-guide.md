# Tool Discovery and Mapping Guide

## Overview

When you add new tools to an MCP server (like Sage Intacct), you might expect them to be automatically available to your agents. This guide explains how tool discovery works in the AI Workspace Agents architecture and what steps are needed to make new tools available.

## How Tool Discovery Works

### 1. **MCP Server Level** ✅ Auto-Discovery
- When you add new tools to the MCP server, they are automatically exposed by the server
- No changes needed at this level
- The MCP server handles tool registration and exposure

### 2. **Tool Mapper Level** ⚠️ Manual Mapping Required
- The `IntacctToolMapper` class acts as a translation layer between agent-friendly names and MCP server tool names
- **New tools must be manually added to the mapper**
- This is where most developers get stuck when tools don't appear

### 3. **Agent Level** ✅ Automatic
- Once mapped, agents automatically have access to the tools
- No agent code changes needed
- Tools are available through the MCP server connection

## Adding New Tools - Step by Step

### Step 1: Verify Tool Availability in MCP Server
First, ensure your tool is actually available in the MCP server. You can test this directly with the MCP server.

### Step 2: Add Tool Mapping
Open `agents/intacct/intacct_tool_mapper.py` and add your new tool mapping:

```python
class IntacctToolMapper(BaseToolMapper):
    def __init__(self, mcp_server_name: str):
        super().__init__(mcp_server_name)
        self._tool_mappings = {
            # Existing GL tools
            "get_trial_balance": "intacct-gl.gl_balance_query",
            "create_journal_entry": "intacct-gl.journal_entry_create",
            
            # ADD YOUR NEW TOOL HERE
            "your_agent_tool_name": "intacct-module.actual_mcp_tool_name",
            #                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
            #                        This is the actual tool name in the MCP server
        }
```

### Step 3: Test the Tool
Run your agent and try using the new tool. It should now be available.

## Common Issues and Solutions

### Issue: "Tool not found" error
**Cause**: The tool name in the mapper doesn't match the actual MCP server tool name.
**Solution**: Check the exact tool name in the MCP server documentation or logs.

### Issue: Tool appears but returns placeholder
**Cause**: The tool mapper is configured to return placeholders for unavailable tools.
**Solution**: This is by design - it means the tool is mapped but not yet implemented in the MCP server.

### Issue: Tool works in MCP server but not in agent
**Cause**: Missing or incorrect mapping in the tool mapper.
**Solution**: Add the mapping as shown in Step 2 above.

## Why Manual Mapping?

You might wonder why we don't auto-discover tools. The current design provides several benefits:

1. **Abstraction**: Agents use friendly names (`get_trial_balance`) instead of technical MCP names (`intacct-gl.gl_balance_query`)
2. **Stability**: Agent code doesn't break if MCP tool names change
3. **Control**: You decide which tools are exposed to agents
4. **Documentation**: The mapper serves as documentation of available tools
5. **Placeholder Support**: Unavailable tools return helpful placeholders instead of errors

## Example: Adding a New AP Tool

Let's say the MCP server adds a new tool called `intacct-ap.vendor_payment_create`. Here's how to make it available:

1. **Open** `agents/intacct/intacct_tool_mapper.py`

2. **Add the mapping**:
```python
self._tool_mappings = {
    # ... existing mappings ...
    
    # AP tools
    "create_vendor_payment": "intacct-ap.vendor_payment_create",
}
```

3. **Use in your agent**:
```python
# In your agent code, you can now use:
result = await tool_mapper.execute_tool("create_vendor_payment", {...params...})
```

## Advanced: Implementing Auto-Discovery

If you want true auto-discovery, you could modify the `BaseToolMapper` to:

1. Query available tools from the MCP server on initialization
2. Automatically create mappings based on naming conventions
3. Cache the discovered tools for performance

Example approach:
```python
async def discover_tools(self):
    """Dynamically discover tools from MCP server"""
    # Query MCP server for available tools
    tools = await mcp_server.list_tools()
    
    # Auto-generate mappings
    for tool in tools:
        # Convert MCP name to friendly name
        friendly_name = tool.name.replace("intacct-", "").replace(".", "_")
        self._tool_mappings[friendly_name] = tool.name
```

## Best Practices

1. **Document new mappings** - Add comments explaining what each tool does
2. **Use consistent naming** - Follow the existing pattern for agent-friendly names
3. **Test thoroughly** - Verify the tool works end-to-end after adding the mapping
4. **Update tests** - Add unit tests for new tool mappings
5. **Coordinate with MCP team** - Ensure tool names are stable before adding mappings

## Troubleshooting Checklist

When a new tool isn't working:

- [ ] Is the tool available in the MCP server?
- [ ] Is the tool name correctly mapped in `IntacctToolMapper`?
- [ ] Are you using the correct agent-friendly name in your code?
- [ ] Have you restarted the agent service after adding the mapping?
- [ ] Check the logs for any error messages
- [ ] Verify the MCP server is running and accessible

## Future Improvements

Consider these enhancements for better tool discovery:

1. **Tool Registry Service** - Central service that maintains tool mappings
2. **Auto-Discovery Mode** - Optional mode that bypasses mapping for direct tool access
3. **Tool Versioning** - Handle different versions of the same tool
4. **Dynamic Reloading** - Reload mappings without restarting the service

---

**Remember**: When in doubt, check the `IntacctToolMapper` class first. 90% of "missing tool" issues are resolved by adding the proper mapping there.