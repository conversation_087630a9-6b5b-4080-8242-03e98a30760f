"""
Test Progressive Search Strategies

Tests the enhanced progressive search functionality with configurable
strategies, dynamic adjustments, and metrics tracking.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, MagicMock
from datetime import datetime
import numpy as np

from services.intelligent_tool_filter import IntelligentToolFilter
from services.search_strategies import (
    SearchStrategyManager, SearchStage, SearchStageConfig,
    SearchStrategyProfile, SearchMetrics
)
from services.search_metrics_tracker import SearchMetricsTracker
from services.vector_db import VectorStore
from services.embedding_service import ToolEmbedder
from services.query_enhancer import QueryEnhancer


@pytest.fixture
def mock_vector_store():
    """Create a mock vector store."""
    store = Mock(spec=VectorStore)
    store.initialize = AsyncMock()
    store.search = AsyncMock()
    return store


@pytest.fixture
def mock_embedder():
    """Create a mock embedder."""
    embedder = Mock(spec=ToolEmbedder)
    embedder.encode = AsyncMock(return_value=np.random.rand(768))
    return embedder


@pytest.fixture
def mock_query_enhancer():
    """Create a mock query enhancer."""
    enhancer = Mock(spec=QueryEnhancer)
    enhancer.enhance_query = AsyncMock(return_value="enhanced query")
    enhancer.get_query_intent_embedding = AsyncMock(return_value=np.random.rand(768))
    return enhancer


@pytest.fixture
def strategy_manager():
    """Create a strategy manager with test profiles."""
    manager = SearchStrategyManager()
    
    # Add a test profile
    test_profile = SearchStrategyProfile(
        name="test",
        description="Test profile",
        stages=[
            SearchStageConfig(
                stage=SearchStage.HIGH_PRECISION,
                threshold=0.9,
                k=10,
                min_results=5
            ),
            SearchStageConfig(
                stage=SearchStage.RELAXED_PRECISION,
                threshold=0.7,
                k=20,
                min_results=3,
                weight_modifier=0.8
            )
        ],
        max_total_results=25
    )
    manager.add_custom_profile(test_profile)
    
    return manager


@pytest.fixture
def intelligent_filter(mock_vector_store, mock_embedder, mock_query_enhancer, strategy_manager):
    """Create an IntelligentToolFilter with mocks."""
    return IntelligentToolFilter(
        vector_store=mock_vector_store,
        embedder=mock_embedder,
        query_enhancer=mock_query_enhancer,
        search_strategy_manager=strategy_manager
    )


def create_mock_search_results(count: int, base_similarity: float = 0.8):
    """Create mock search results."""
    results = []
    for i in range(count):
        tool_id = f"tool_{i}"
        similarity = base_similarity - (i * 0.05)  # Decreasing similarity
        metadata = {
            "tool_name": f"Tool {i}",
            "server": "test_server",
            "description": f"Test tool {i} description",
            "category": "general" if i % 2 == 0 else "specific",
            "capabilities": ["read", "write"] if i % 3 == 0 else ["read"],
            "parameters": {"param1": "string"},
            "keywords": ["test", f"keyword{i}"],
            "execution_stats": {
                "count": i * 10,
                "success_rate": 0.9 - (i * 0.1),
                "avg_execution_time": 100 + (i * 10),
                "last_used": datetime.now().isoformat()
            }
        }
        results.append((tool_id, similarity, metadata))
    return results


class TestSearchStrategies:
    """Test search strategy functionality."""
    
    @pytest.mark.asyncio
    async def test_balanced_strategy(self, intelligent_filter, mock_vector_store):
        """Test the balanced search strategy."""
        # Set up mock responses for different stages
        mock_vector_store.search.side_effect = [
            # High precision stage - returns 8 results (below min_results)
            create_mock_search_results(8, 0.85),
            # Relaxed precision stage - returns 15 more results
            create_mock_search_results(15, 0.65)
        ]
        
        # Use balanced profile
        intelligent_filter.set_search_strategy("balanced")
        
        # Execute search
        results = await intelligent_filter.get_relevant_tools(
            query="find customer invoices",
            context={"user_preferences": {}}
        )
        
        # Verify results
        assert len(results) <= 20  # Max tools limit
        assert mock_vector_store.search.call_count >= 2  # At least two stages
        
        # Check that metrics were recorded
        metrics = intelligent_filter.get_search_metrics(last_n=1)
        assert len(metrics) == 1
        assert metrics[0]["profile"] == "balanced"
        assert metrics[0]["total_stages"] >= 2
    
    @pytest.mark.asyncio
    async def test_precise_strategy(self, intelligent_filter, mock_vector_store):
        """Test the precise search strategy."""
        # High precision returns good results
        mock_vector_store.search.return_value = create_mock_search_results(12, 0.9)
        
        intelligent_filter.set_search_strategy("precise")
        
        results = await intelligent_filter.get_relevant_tools(
            query="calculate tax deductions",
            context={}
        )
        
        # Should stop after first stage with enough results
        assert mock_vector_store.search.call_count == 1
        assert len(results) <= 20
    
    @pytest.mark.asyncio
    async def test_broad_strategy(self, intelligent_filter, mock_vector_store):
        """Test the broad search strategy."""
        # Set up responses for multiple stages
        mock_vector_store.search.side_effect = [
            create_mock_search_results(10, 0.8),   # High precision
            create_mock_search_results(20, 0.6),   # Relaxed
            create_mock_search_results(15, 0.5),   # Category-based
            create_mock_search_results(10, 0.4),   # Contextual
        ]
        
        intelligent_filter.set_search_strategy("broad")
        
        results = await intelligent_filter.get_relevant_tools(
            query="analyze business performance",
            context={}
        )
        
        # Should execute multiple stages
        assert mock_vector_store.search.call_count >= 2
        assert len(results) <= 20
    
    @pytest.mark.asyncio
    async def test_custom_profile(self, intelligent_filter, mock_vector_store):
        """Test a custom search profile."""
        mock_vector_store.search.side_effect = [
            create_mock_search_results(4, 0.91),   # Below min_results
            create_mock_search_results(10, 0.75),  # Second stage
        ]
        
        intelligent_filter.set_search_strategy("test")
        
        results = await intelligent_filter.get_relevant_tools(
            query="test query",
            context={}
        )
        
        assert mock_vector_store.search.call_count == 2
        metrics = intelligent_filter.get_search_metrics(last_n=1)
        assert metrics[0]["profile"] == "test"


class TestDynamicThresholdAdjustment:
    """Test dynamic threshold adjustment."""
    
    @pytest.mark.asyncio
    async def test_threshold_adjustment_low_results(self, intelligent_filter, mock_vector_store):
        """Test threshold adjustment when getting few results."""
        # Simulate multiple searches with low results
        for i in range(10):
            mock_vector_store.search.return_value = create_mock_search_results(3, 0.82)
            
            await intelligent_filter.get_relevant_tools(
                query=f"query {i}",
                context={}
            )
        
        # Get current profile thresholds
        profile = intelligent_filter.strategy_manager.get_profile()
        high_precision_stage = next(
            s for s in profile.stages 
            if s.stage == SearchStage.HIGH_PRECISION
        )
        
        # Threshold should have been adjusted down
        assert high_precision_stage.threshold < 0.8  # Original was 0.8
    
    @pytest.mark.asyncio
    async def test_threshold_adjustment_disabled(self, intelligent_filter, mock_vector_store):
        """Test that adjustment doesn't happen when disabled."""
        intelligent_filter.set_search_strategy("precise")  # Has dynamic adjustment disabled
        
        original_profile = intelligent_filter.strategy_manager.get_profile("precise")
        original_threshold = original_profile.stages[0].threshold
        
        # Run multiple searches
        for i in range(10):
            mock_vector_store.search.return_value = create_mock_search_results(2, 0.86)
            await intelligent_filter.get_relevant_tools(f"query {i}", {})
        
        # Threshold should remain unchanged
        current_threshold = original_profile.stages[0].threshold
        assert current_threshold == original_threshold


class TestSearchMetrics:
    """Test search metrics tracking."""
    
    @pytest.mark.asyncio
    async def test_metrics_recording(self, intelligent_filter, mock_vector_store):
        """Test that metrics are properly recorded."""
        mock_vector_store.search.side_effect = [
            create_mock_search_results(8, 0.85),
            create_mock_search_results(12, 0.65)
        ]
        
        await intelligent_filter.get_relevant_tools("test query", {})
        
        # Get metrics
        metrics = intelligent_filter.get_search_metrics(last_n=1)
        assert len(metrics) == 1
        
        metric = metrics[0]
        assert "total_stages" in metric
        assert "total_results" in metric
        assert "total_time_ms" in metric
        assert "stages" in metric
        assert len(metric["stages"]) >= 1
    
    @pytest.mark.asyncio
    async def test_performance_report(self, intelligent_filter, mock_vector_store):
        """Test performance report generation."""
        # Run several searches
        for i in range(5):
            mock_vector_store.search.return_value = create_mock_search_results(10, 0.8)
            await intelligent_filter.get_relevant_tools(f"query {i}", {})
        
        # Get performance report
        report = intelligent_filter.get_performance_report(hours=1)
        
        assert "summary" in report
        assert report["summary"]["total_searches"] == 5
        assert "stages" in report
        assert "recommendations" in report
    
    @pytest.mark.asyncio
    async def test_feedback_recording(self, intelligent_filter, mock_vector_store):
        """Test feedback recording for tool selection."""
        mock_vector_store.search.return_value = create_mock_search_results(10, 0.8)
        
        context = {}
        await intelligent_filter.get_relevant_tools("test query", context)
        
        # Record feedback
        intelligent_filter.record_tool_selection_feedback(
            context=context,
            tool_selected="tool_0",
            was_successful=True
        )
        
        # Verify feedback was recorded (no exception thrown)
        assert True  # If we get here, feedback recording worked


class TestSearchStageExecution:
    """Test individual search stage execution."""
    
    @pytest.mark.asyncio
    async def test_stage_weight_modifier(self, intelligent_filter, mock_vector_store):
        """Test that weight modifiers are applied correctly."""
        # Create results with known similarity scores
        base_results = [
            ("tool_1", 0.9, {"tool_name": "Tool 1", "server": "test"}),
            ("tool_2", 0.8, {"tool_name": "Tool 2", "server": "test"})
        ]
        
        mock_vector_store.search.side_effect = [
            [],  # First stage returns nothing
            base_results  # Second stage with weight modifier
        ]
        
        # Use a profile where second stage has weight modifier
        intelligent_filter.set_search_strategy("balanced")
        
        results = await intelligent_filter.get_relevant_tools("test", {})
        
        # Check that similarity scores were modified
        # The relaxed stage in balanced profile has 0.9 weight modifier
        assert results[0]["similarity_score"] == pytest.approx(0.9 * 0.9, 0.01)
        assert results[1]["similarity_score"] == pytest.approx(0.8 * 0.9, 0.01)
    
    @pytest.mark.asyncio
    async def test_max_total_results_limit(self, intelligent_filter, mock_vector_store):
        """Test that max_total_results is respected."""
        # Return many results from multiple stages
        mock_vector_store.search.side_effect = [
            create_mock_search_results(30, 0.8),
            create_mock_search_results(30, 0.6),
            create_mock_search_results(30, 0.4)
        ]
        
        intelligent_filter.set_search_strategy("broad")  # Has max_total_results=80
        
        # Execute search
        await intelligent_filter.get_relevant_tools("test", {})
        
        # Get metrics to check total candidates
        metrics = intelligent_filter.get_search_metrics(last_n=1)
        
        # Should stop collecting after reaching max_total_results
        # Note: final results are limited to max_tools (20)
        assert metrics[0]["total_results"] <= 20


class TestOptimizationRecommendations:
    """Test optimization recommendation system."""
    
    @pytest.mark.asyncio
    async def test_slow_response_recommendation(self, intelligent_filter, mock_vector_store):
        """Test recommendation for slow responses."""
        # Simulate slow searches
        async def slow_search(*args, **kwargs):
            await asyncio.sleep(0.2)  # 200ms delay
            return create_mock_search_results(10, 0.8)
        
        mock_vector_store.search = slow_search
        
        # Run a few searches
        for i in range(3):
            await intelligent_filter.get_relevant_tools(f"query {i}", {})
        
        # Get recommendations
        recommendations = intelligent_filter.get_optimization_recommendations()
        
        # Should recommend fast profile for performance
        perf_recs = [r for r in recommendations if r["type"] == "performance"]
        assert any("fast" in r.get("params", {}).get("profile", "") for r in perf_recs)
    
    @pytest.mark.asyncio  
    async def test_low_results_recommendation(self, intelligent_filter, mock_vector_store):
        """Test recommendation for consistently low results."""
        # Return very few results
        mock_vector_store.search.return_value = create_mock_search_results(2, 0.85)
        
        # Run multiple searches
        for i in range(15):
            await intelligent_filter.get_relevant_tools(f"query {i}", {})
        
        recommendations = intelligent_filter.get_optimization_recommendations()
        
        # Should recommend broad profile
        quality_recs = [r for r in recommendations if r["type"] == "quality"]
        assert any("broad" in r.get("params", {}).get("profile", "") for r in quality_recs)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])