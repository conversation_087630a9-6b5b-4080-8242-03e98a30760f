"""
Base interface for vector stores
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, <PERSON>ple
import numpy as np


class VectorStore(ABC):
    """Abstract base class for vector storage implementations."""
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the vector store (create tables, indices, etc.)."""
        pass
    
    @abstractmethod
    async def upsert(
        self,
        id: str,
        embedding: np.ndarray,
        metadata: Dict[str, Any]
    ) -> None:
        """
        Insert or update a vector with metadata.
        
        Args:
            id: Unique identifier for the vector
            embedding: Vector embedding as numpy array
            metadata: Associated metadata
        """
        pass
    
    @abstractmethod
    async def search(
        self,
        query_embedding: np.ndarray,
        k: int = 20,
        threshold: float = 0.7,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Tuple[str, float, Dict[str, Any]]]:
        """
        Search for similar vectors.
        
        Args:
            query_embedding: Query vector
            k: Number of results to return
            threshold: Minimum similarity threshold
            filters: Optional metadata filters
            
        Returns:
            List of tuples (id, similarity_score, metadata)
        """
        pass
    
    @abstractmethod
    async def get(self, id: str) -> Optional[Tuple[np.ndarray, Dict[str, Any]]]:
        """
        Get a vector by ID.
        
        Args:
            id: Vector ID
            
        Returns:
            Tuple of (embedding, metadata) or None if not found
        """
        pass
    
    @abstractmethod
    async def delete(self, id: str) -> bool:
        """
        Delete a vector by ID.
        
        Args:
            id: Vector ID
            
        Returns:
            True if deleted, False if not found
        """
        pass
    
    @abstractmethod
    async def update_metadata(
        self,
        id: str,
        metadata_updates: Dict[str, Any]
    ) -> bool:
        """
        Update metadata for a vector.
        
        Args:
            id: Vector ID
            metadata_updates: Metadata fields to update
            
        Returns:
            True if updated, False if not found
        """
        pass
    
    @abstractmethod
    async def clear(self) -> None:
        """Clear all vectors from the store."""
        pass
    
    @abstractmethod
    async def count(self) -> int:
        """Get the total number of vectors in the store."""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check if the vector store is healthy and accessible."""
        pass