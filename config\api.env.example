# AI Workspace Dynamic Orchestration Configuration
# Copy this file to api.env and update with your values

# Server Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_ENV=development

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:3001"]

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# FastAPI Configuration
API_TITLE="AI Workspace Dynamic Orchestration Service"
API_VERSION="1.0.0"
API_DESCRIPTION="Dynamic orchestration service that routes requests to MCP tools"

# LLM Provider Configuration for Orchestrator
# REQUIRED: The orchestrator needs OpenAI API key to understand queries and select tools
# Get your key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-...  # REQUIRED - Replace with your actual OpenAI API key

# Optional: Additional providers
ANTHROPIC_API_KEY=your_anthropic_api_key_here  # Optional - for Claude models
# GOOGLE_API_KEY=your_google_api_key_here
# COHERE_API_KEY=your_cohere_api_key_here
# PERPLEXITY_API_KEY=your_perplexity_api_key_here  # For research capabilities

# Model Configuration for Orchestrator
DEFAULT_MODEL=gpt-4-turbo-preview
# Options: gpt-4, gpt-4-turbo-preview, gpt-3.5-turbo, claude-3-opus, claude-3-sonnet, claude-3-haiku
OPENAI_TEMPERATURE=0.3  # Lower temperature for more consistent responses

# Orchestration Settings
ORCHESTRATOR_CONFIDENCE_THRESHOLD=0.8  # Minimum confidence for automatic routing
ORCHESTRATOR_MAX_RETRIES=3  # Max retries for tool execution
ORCHESTRATOR_TIMEOUT_SECONDS=90  # Timeout for orchestrator operations

# Dynamic MCP Configuration
MCP_CONFIG_DIR=.config  # Directory for dynamic MCP configurations
MCP_DISCOVERY_INTERVAL=300  # Seconds between tool discovery refreshes
MCP_HEALTH_CHECK_INTERVAL=30  # Seconds between health checks

# Performance Settings
REQUEST_TIMEOUT_SECONDS=30
MAX_CONCURRENT_TOOLS=5  # Max tools that can run in parallel
TOOL_CACHE_TTL_SECONDS=3600  # Tool schema cache duration

# Security
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_MINUTES=60

# MCP Server Notes:
# - MCP servers run independently with their own credentials
# - Use the API endpoints to add/remove MCP servers at runtime
# - Default servers are configured in fastagent.config.yaml
# - Dynamic servers are persisted in the MCP_CONFIG_DIR
