"""
Unit tests for WebSocket functionality
"""

import pytest
import async<PERSON>
import json
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from fastapi.testclient import Test<PERSON>lient
from datetime import datetime, timezone

from services.api_service import app
from services.agent_manager import <PERSON><PERSON><PERSON><PERSON>, AgentResponse


class TestWebSocketEndpoint:
    """Test suite for WebSocket functionality"""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup for each test"""
        # Initialize app state for tests
        from services.agent_manager import get_agent_manager
        app.state.agent_manager = get_agent_manager()
    
    def test_websocket_connection(self):
        """Test basic WebSocket connection"""
        with Test<PERSON>lient(app) as client:
            with client.websocket_connect("/agents/ws") as websocket:
                # Connection should be established
                # Send a ping
                websocket.send_json({"type": "ping"})
                data = websocket.receive_json()
                assert data["type"] == "pong"
    
    def test_websocket_message_handling(self):
        """Test WebSocket message handling"""
        with Test<PERSON>lient(app) as client:
            # Mock the agent manager's send_message method
            mock_response = AgentResponse(
                agent_id="gl_agent",
                content="Balance is $10,000",
                correlation_id="ws-123",
                status="success",
                timestamp=datetime.now(timezone.utc),
                metadata={"query_type": "balance"}
            )
            
            with patch.object(app.state.agent_manager, 'send_message', return_value=mock_response):
                with client.websocket_connect("/agents/ws") as websocket:
                    # Send agent message
                    websocket.send_json({
                        "agent_id": "gl_agent",
                        "content": "What's the balance?",
                        "correlation_id": "ws-123"
                    })
                    
                    # Receive response
                    data = websocket.receive_json()
                    assert data["type"] == "response"
                    assert data["agent_id"] == "gl_agent"
                    assert data["content"] == "Balance is $10,000"
                    assert data["correlation_id"] == "ws-123"
    
    def test_websocket_error_handling(self):
        """Test WebSocket error handling"""
        with TestClient(app) as client:
            # Mock agent_manager to raise an exception
            with patch.object(app.state.agent_manager, 'send_message', side_effect=Exception("Agent unavailable")):
                with client.websocket_connect("/agents/ws") as websocket:
                    # Send message that will cause error
                    websocket.send_json({
                        "agent_id": "gl_agent",
                        "content": "Test error",
                        "correlation_id": "error-123"
                    })
                    
                    # Should receive error response
                    data = websocket.receive_json()
                    assert data["type"] == "error"
                    assert "Agent unavailable" in data["error"]
    
    def test_websocket_invalid_message(self):
        """Test WebSocket handling of invalid messages"""
        with TestClient(app) as client:
            with client.websocket_connect("/agents/ws") as websocket:
                # Send invalid JSON
                websocket.send_text("invalid json")
                
                # Should receive error response
                data = websocket.receive_json()
                assert data["type"] == "error"
                assert "Invalid message format" in data["error"]
    
    def test_websocket_missing_fields(self):
        """Test WebSocket handling of missing required fields"""
        with TestClient(app) as client:
            with client.websocket_connect("/agents/ws") as websocket:
                # Send message without agent_id
                websocket.send_json({
                    "content": "Test message"
                })
                
                # Should receive error response
                data = websocket.receive_json()
                assert data["type"] == "error"
                assert "agent_id" in data["error"]
    
    def test_websocket_heartbeat(self):
        """Test WebSocket heartbeat mechanism"""
        with TestClient(app) as client:
            with client.websocket_connect("/agents/ws") as websocket:
                # Send heartbeat
                websocket.send_json({"type": "heartbeat"})
                
                # Should receive heartbeat response
                data = websocket.receive_json()
                assert data["type"] == "heartbeat"
                assert "timestamp" in data
