"""Simple integration tests for orchestrator workflow components."""

import pytest
from unittest.mock import Async<PERSON>ock, MagicMock, patch
import asyncio

from agents.orchestrator import DynamicOrchestrator
from services.confidence_scorer import ConfidenceScorer
from services.mcp_registry import MCPServerRegistry
from services.tool_discovery import ToolDiscoveryService


class TestOrchestratorComponents:
    """Test orchestrator components integration."""
    
    @pytest.mark.asyncio
    async def test_confidence_scoring_integration(self):
        """Test confidence scoring in the orchestrator."""
        # Create a real confidence scorer
        scorer = ConfidenceScorer()
        
        # Test various queries
        test_cases = [
            {
                "query": "Show me the GL balance for account 1000",
                "expected_intent": "gl_balance",
                "min_confidence": 0.8
            },
            {
                "query": "Find invoices from last month",
                "expected_intent": "ar_search",
                "min_confidence": 0.7
            },
            {
                "query": "Generate financial report",
                "expected_intent": "financial_report",
                "min_confidence": 0.7
            },
            {
                "query": "Something vague about money",
                "expected_intent": None,  # Low confidence
                "max_confidence": 0.6
            }
        ]
        
        for test_case in test_cases:
            # Mock orchestrator with just confidence scoring
            orchestrator = DynamicOrchestrator()
            orchestrator.confidence_scorer = scorer
            
            # Test intent detection
            intent_info = await orchestrator._detect_intent(test_case["query"])
            
            print(f"\nQuery: {test_case['query']}")
            print(f"Detected intent: {intent_info['primary_intent']}")
            print(f"Confidence: {intent_info['confidence']:.2f}")
            print(f"Action: {intent_info['confidence_action']['action']}")
            
            # Verify confidence levels
            if test_case.get("min_confidence"):
                assert intent_info['confidence'] >= test_case["min_confidence"], \
                    f"Confidence too low for '{test_case['query']}'"
            
            if test_case.get("max_confidence"):
                assert intent_info['confidence'] <= test_case["max_confidence"], \
                    f"Confidence too high for '{test_case['query']}'"
            
            # Verify intent if expected
            if test_case["expected_intent"]:
                assert intent_info['primary_intent'] == test_case["expected_intent"], \
                    f"Wrong intent for '{test_case['query']}'"
    
    @pytest.mark.asyncio
    async def test_tool_discovery_integration(self):
        """Test tool discovery service integration."""
        # Mock registry with tools
        registry = MagicMock(spec=MCPServerRegistry)
        registry.list_servers = AsyncMock(return_value=[{
            "name": "test-server",
            "status": "connected"
        }])
        registry.get_server_tools = AsyncMock(return_value={
            "search_data": {
                "name": "search_data",
                "description": "Search across all data",
                "parameters": {}
            },
            "generate_report": {
                "name": "generate_report", 
                "description": "Generate financial reports",
                "parameters": {}
            }
        })
        
        # Create discovery service
        discovery = ToolDiscoveryService(registry)
        
        # Test tool discovery
        all_tools = await discovery.discover_all_tools()
        assert len(all_tools) > 0
        
        # Test tool search
        search_results = await discovery.search_tools("report")
        assert len(search_results) > 0
        assert any("report" in tool.name.lower() for tool in search_results)
        
        # Test catalog generation
        catalog = await discovery.get_tool_catalog()
        assert "total_tools" in catalog
        assert catalog["total_tools"] > 0
    
    @pytest.mark.asyncio
    async def test_orchestrator_initialization(self):
        """Test orchestrator can initialize with mocked dependencies."""
        # Mock registry
        registry = MagicMock(spec=MCPServerRegistry)
        registry.list_servers = AsyncMock(return_value=[])
        
        # Create orchestrator
        orchestrator = DynamicOrchestrator(registry=registry)
        
        # Verify it initializes properly
        assert orchestrator.registry is not None
        assert orchestrator.discovery is not None
        assert orchestrator.confidence_scorer is not None
        assert orchestrator.parallel_executor is None  # Not initialized until needed
        
        # Test workflow template initialization
        assert len(orchestrator._workflow_templates) > 0
        assert "month_end_close" in orchestrator._workflow_templates