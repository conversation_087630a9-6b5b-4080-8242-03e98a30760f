# FastAgent to Direct MCP Client Migration Guide

## Executive Summary

This document outlines the architectural refactoring needed to remove FastAgent from the orchestrator and use MCP client libraries directly. This change is necessary because FastAgent is designed as a standalone application framework, not as an embedded library within another system.

## Problem Statement

### Current Issues

1. **Architectural Mismatch**: FastAgent is designed to be the main application framework, managing its own event loop, CLI arguments, and lifecycle. We're trying to embed it inside our API service, causing conflicts.

2. **Tool Execution Failures**: The orchestrator is describing what tools it would use instead of actually executing them, despite multiple attempts to fix the issue.

3. **Configuration Conflicts**: FastAgent expects to load its own configuration and parse CLI arguments, which conflicts with our API server (uvicorn).

4. **Impedance Mismatch**: We're trying to dynamically create agents inside a class, but FastAgent expects agents to be defined at module level with decorators.

### Evidence of the Problem

```python
# What FastAgent expects:
@fast.agent("agent_name", "instruction", servers=["mcp_server"])
async def main():
    async with fast.run() as agent:
        await agent.send("message")

# What we're trying to do:
class DynamicOrchestrator:
    def __init__(self):
        self.fast_agent = FastAgent("name", parse_cli_args=False)
    
    async def initialize(self):
        @self.fast_agent.agent(...)  # Dynamic agent creation
        async def orchestrate():
            pass
```

## Proposed Solution

Replace FastAgent with direct MCP client usage, maintaining our orchestrator architecture while properly executing MCP tools.

## Implementation Plan

### Phase 1: Understand MCP Client Architecture

1. **Research MCP Python SDK**
   - Review the MCP Python client documentation
   - Understand how to create MCP client connections
   - Learn the tool calling interface

2. **Identify Required Components**
   - MCP client connection management
   - Tool discovery and enumeration
   - Tool execution interface
   - Response handling

### Phase 2: Refactor MCP Connection Management ✅

1. **Update MCPServerRegistry** ✅
   - Enhance to create and manage MCP client connections
   - Handle SSE, HTTP, and stdio transports
   - Implement connection pooling and health checks

2. **Create MCP Client Wrapper** ✅
   ```python
   class MCPClientWrapper:
       def __init__(self, server_config: Dict[str, Any]):
           self.client = self._create_client(server_config)
       
       async def list_tools(self) -> List[Tool]:
           """Get available tools from MCP server"""
           pass
       
       async def call_tool(self, tool_name: str, arguments: Dict) -> Any:
           """Execute a tool and return results"""
           pass
   ```

### Phase 3: Update Orchestrator Architecture

1. **Remove FastAgent Dependencies**
   - Remove FastAgent imports and initialization
   - Remove agent decorator usage
   - Clean up configuration loading

2. **Implement Direct Tool Execution**
   ```python
   class DynamicOrchestrator:
       async def process(self, message: str) -> Dict[str, Any]:
           # 1. Send message to LLM for analysis
           analysis = await self._analyze_with_llm(message)
           
           # 2. Extract tool calls from LLM response
           tool_calls = self._extract_tool_calls(analysis)
           
           # 3. Execute tools via MCP clients
           results = await self._execute_tools(tool_calls)
           
           # 4. Format and return results
           return self._format_response(results)
   ```

3. **LLM Integration for Tool Selection**
   - Use the LLM to analyze requests and suggest tools
   - Parse LLM responses to extract tool names and parameters
   - Handle multi-step workflows

### Phase 4: Tool Execution Engine ✅

1. **Single Tool Execution** ✅
   ```python
   async def _execute_tool(self, server_name: str, tool_name: str, args: Dict):
       client = self.registry.get_client(server_name)
       return await client.call_tool(tool_name, args)
   ```

2. **Parallel Tool Execution** ✅
   - Maintain existing ParallelToolExecutor
   - Update to use MCP clients instead of FastAgent

3. **Workflow Execution** ✅
   - Keep workflow templates
   - Update execution to use MCP clients

### Phase 5: Response Processing ✅

1. **Tool Result Handling**
   - Process MCP tool responses
   - Handle different response types (text, images, resources)
   - Error handling and retries

2. **LLM Response Generation**
   - Send tool results back to LLM for summarization
   - Generate user-friendly responses

## Benefits of This Approach

1. **Clean Architecture**: Our orchestrator remains in control, using MCP as a tool execution layer
2. **Better Integration**: No conflicts with our API server or configuration system
3. **Flexibility**: Can dynamically discover and use MCP servers without FastAgent constraints
4. **Maintainability**: Clearer separation of concerns between orchestration and tool execution

## Technical Considerations

### Dependencies
- MCP Python SDK (`mcp`)
- Existing orchestrator infrastructure
- LLM integration (already in place)

### Configuration Changes
- Keep `fastagent.config.yaml` for MCP server definitions
- Remove FastAgent-specific configuration
- Update to use our existing model configuration

### Error Handling
- Connection failures to MCP servers
- Tool execution errors
- LLM parsing errors
- Timeout handling

## Migration Steps

1. **Create Feature Branch**: `feature/direct-mcp-client`

2. **Implement MCP Client Wrapper**: Start with basic connection and tool listing

3. **Test with Single Tool**: Verify basic tool execution works

4. **Refactor Orchestrator**: Remove FastAgent, implement new flow

5. **Test End-to-End**: Ensure tools execute properly

6. **Update Tests**: Modify tests to work with new architecture

7. **Documentation**: Update README and architecture docs

## Success Criteria

1. **Tool Execution**: When user asks "What's my YTD sales?", the system executes the appropriate MCP tool and returns actual data

2. **No Tool Description**: System should not describe what it would do, but actually do it

3. **Clean Architecture**: No embedded application frameworks, clear separation of concerns

4. **Maintain Features**: Preserve parallel execution, workflows, and dynamic discovery

## Example Implementation Flow

```
User: "What's my current month sales?"
  ↓
Orchestrator: Analyze with LLM
  ↓
LLM: "Need to call sage-intacct:get_financial_summary with period=current_month"
  ↓
Orchestrator: Extract tool call from LLM response
  ↓
MCP Client: Execute get_financial_summary
  ↓
MCP Server: Return actual sales data
  ↓
Orchestrator: Format response with LLM
  ↓
User: Receives actual sales figures
```

## Timeline Estimate

- Phase 1: 1 day (research and planning)
- Phase 2: 2 days (MCP client implementation)
- Phase 3: 2 days (orchestrator refactoring)
- Phase 4: 1 day (tool execution engine)
- Phase 5: 1 day (response processing)
- Testing & Documentation: 2 days

**Total: ~9 days**

## Risks and Mitigations

1. **Risk**: MCP SDK documentation gaps
   - **Mitigation**: Review MCP specification and example implementations

2. **Risk**: Breaking existing functionality
   - **Mitigation**: Comprehensive test suite before migration

3. **Risk**: Performance degradation
   - **Mitigation**: Implement connection pooling and caching

## Conclusion

Moving from FastAgent to direct MCP client usage will resolve our current architectural conflicts and enable proper tool execution. This approach aligns with our system architecture where the orchestrator is the primary controller, using MCP servers as tool providers rather than trying to embed an entire application framework.

# Task Steps:
- Follow this task
- Update the Progress.md file at the end of each phase or task
- Stop and ask for review at the end of each phase or task

## Migration Status: COMPLETE ✅

All phases of the FastAgent to Direct MCP Client migration have been successfully completed:
- ✅ Phase 1: Research and Planning
- ✅ Phase 2: MCP Client Implementation
- ✅ Phase 3: Orchestrator Refactoring
- ✅ Phase 4: Tool Execution Engine
- ✅ Phase 5: Response Processing
- ✅ Testing & Documentation

### Post-Migration Notes (Session 6):
- Fixed configuration mismatch: Updated port from 3000 to 8001 for SSE transport
- The migration is fully functional; only runtime server startup is needed
- To test: Run `scripts/run-intacct-supergateway.bat` then `python run.py`
