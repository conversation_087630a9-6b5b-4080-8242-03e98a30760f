#!/usr/bin/env python
"""
Run script for AI Workspace Agent Service
"""

import uvicorn
from services.config import get_settings
# from services.logging import setup_logging

if __name__ == "__main__":
    settings = get_settings()
    
    # Setup logging
    # setup_logging(settings.log_level, settings.log_format)
    
    # Run the application
    uvicorn.run(
        "services.api_service:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.api_env == "development",
        log_config=None,  # Use our structured logging
    )
