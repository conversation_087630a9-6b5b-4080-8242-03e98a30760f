"""
SBCA agents module.

Example structure for Sage Business Cloud Accounting agents.
"""

# Future imports when agents are implemented
# from .sales_agent import sales_agent_instance, SBCASalesAgent
# from .inventory_agent import inventory_agent_instance, SBCAInventoryAgent
# from .sbca_tool_mapper import SBCAToolMapper

__all__ = [
    # 'sales_agent_instance',
    # 'SBCASalesAgent',
    # 'inventory_agent_instance',
    # 'SBCAInventoryAgent',
    # 'SBCAToolMapper'
]
