#!/usr/bin/env python3
"""Run specific integration test for orchestrator workflow."""

import subprocess
import sys
import os

# Ensure we're in the project directory
project_root = os.path.dirname(os.path.abspath(__file__))
os.chdir(project_root)

# Run the orchestrator workflow integration test
print("Running orchestrator workflow integration test...")
print(f"Current directory: {os.getcwd()}")

test_file = "tests/test_orchestrator_workflow_integration.py"
cmd = [sys.executable, "-m", "pytest", test_file, "-v", "--tb=short"]

print(f"Running: {' '.join(cmd)}")

result = subprocess.run(cmd, capture_output=True, text=True)

print("\n" + "="*80)
print("STDOUT:")
print(result.stdout)
print("\nSTDERR:")
print(result.stderr)
print(f"\nReturn code: {result.returncode}")