"""
Tests for Dynamic MCP Configuration
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock

from config.dynamic_mcp_config import (
    DynamicMCPConfig,
    DynamicMCPServerConfig,
    ConfigurationPersistence,
    get_dynamic_mcp_config
)


class TestConfigurationPersistence:
    """Test configuration persistence functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.persistence = ConfigurationPersistence(self.temp_dir)
        
    def teardown_method(self):
        """Cleanup test environment"""
        shutil.rmtree(self.temp_dir)
        
    def test_save_and_load_servers(self):
        """Test saving and loading server configurations"""
        # Create test servers
        servers = {
            "test-server-1": DynamicMCPServerConfig(
                name="test-server-1",
                command="python",
                args=["-m", "test_server"],
                env={"API_KEY": "test123"},
                tags=["test", "local"],
                added_at=datetime.now(),
                source="api"
            ),
            "test-server-2": DynamicMCPServerConfig(
                name="test-server-2",
                command="npx",
                args=["supergateway", "https://test.com/sse"],
                connection_type="hosted",
                endpoint="https://test.com/sse",
                persistent=False  # Should not be saved
            )
        }
        
        # Save servers
        self.persistence.save_servers(servers)
        
        # Verify file was created
        assert self.persistence.dynamic_config_file.exists()
        
        # Load servers
        loaded_servers = self.persistence.load_servers()
        
        # Only persistent servers should be loaded
        assert len(loaded_servers) == 1
        assert "test-server-1" in loaded_servers
        assert "test-server-2" not in loaded_servers
        
        # Verify loaded data
        loaded = loaded_servers["test-server-1"]
        assert loaded.command == "python"
        assert loaded.args == ["-m", "test_server"]
        assert loaded.env["API_KEY"] == "test123"
        assert loaded.tags == ["test", "local"]
        assert loaded.source == "api"
        
    def test_backup_creation(self):
        """Test that backups are created when saving"""
        servers = {
            "test-server": DynamicMCPServerConfig(
                name="test-server",
                command="test"
            )
        }
        
        # Save initial config
        self.persistence.save_servers(servers)
        
        # Modify and save again
        servers["test-server"].command = "modified"
        self.persistence.save_servers(servers)
        
        # Check backup was created
        backups = list(self.persistence.backup_dir.glob("dynamic_mcp_servers_*.yaml"))
        assert len(backups) == 1


class TestDynamicMCPConfig:
    """Test DynamicMCPConfig functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        # Reset singleton
        global _dynamic_config
        _dynamic_config = None
        
    def teardown_method(self):
        """Cleanup test environment"""
        shutil.rmtree(self.temp_dir)
        
    @pytest.mark.asyncio
    async def test_add_server(self):
        """Test adding a new MCP server"""
        config = DynamicMCPConfig(self.temp_dir)
        
        # Add a server
        server = await config.add_server(
            name="test-mcp",
            command="python",
            args=["-m", "mcp_server"],
            env={"PORT": "8080"},
            tags=["test", "python"],
            metadata={"version": "1.0"}
        )
        
        assert server.name == "test-mcp"
        assert server.command == "python"
        assert server.args == ["-m", "mcp_server"]
        assert server.env["PORT"] == "8080"
        assert "test" in server.tags
        assert server.metadata["version"] == "1.0"
        assert server.source == "api"
        assert server.added_at is not None
        
        # Verify server is in configuration
        assert "test-mcp" in config.mcp_servers
        assert "test-mcp" in config._dynamic_servers
        
    @pytest.mark.asyncio
    async def test_add_duplicate_server(self):
        """Test adding a duplicate server raises error"""
        config = DynamicMCPConfig(self.temp_dir)
        
        # Add initial server
        await config.add_server("test-mcp", "python")
        
        # Try to add duplicate
        with pytest.raises(ValueError, match="already exists"):
            await config.add_server("test-mcp", "python")
            
    @pytest.mark.asyncio
    async def test_remove_server(self):
        """Test removing an MCP server"""
        config = DynamicMCPConfig(self.temp_dir)
        
        # Add and then remove a server
        await config.add_server("test-mcp", "python")
        removed = await config.remove_server("test-mcp")
        
        assert removed is True
        assert "test-mcp" not in config.mcp_servers
        assert "test-mcp" not in config._dynamic_servers
        
    @pytest.mark.asyncio
    async def test_remove_nonexistent_server(self):
        """Test removing a non-existent server"""
        config = DynamicMCPConfig(self.temp_dir)
        
        removed = await config.remove_server("nonexistent")
        assert removed is False
        
    @pytest.mark.asyncio
    async def test_update_server(self):
        """Test updating server configuration"""
        config = DynamicMCPConfig(self.temp_dir)
        
        # Add a server
        await config.add_server(
            name="test-mcp",
            command="python",
            connection_type="local"
        )
        
        # Update it
        updated = await config.update_server(
            name="test-mcp",
            command="node",
            connection_type="hosted",
            endpoint="https://mcp.example.com",
            tags=["updated", "hosted"]
        )
        
        assert updated.command == "node"
        assert updated.connection_type == "hosted"
        assert updated.endpoint == "https://mcp.example.com"
        assert "updated" in updated.tags
        assert updated.modified_at is not None
        
    @pytest.mark.asyncio
    async def test_update_server_auth(self):
        """Test updating server authentication"""
        config = DynamicMCPConfig(self.temp_dir)
        
        # Add a server
        await config.add_server(
            name="test-mcp",
            command="python",
            env={"OLD_KEY": "old_value"}
        )
        
        # Update auth
        await config.update_server_auth(
            name="test-mcp",
            auth_env={"API_KEY": "new_secret", "TOKEN": "bearer_token"}
        )
        
        server = config.mcp_servers["test-mcp"]
        assert server.env["API_KEY"] == "new_secret"
        assert server.env["TOKEN"] == "bearer_token"
        assert server.env["OLD_KEY"] == "old_value"  # Original env preserved
        
    def test_list_servers(self):
        """Test listing servers with filtering"""
        config = DynamicMCPConfig(self.temp_dir)
        
        # Add test servers synchronously for simplicity
        config._dynamic_servers["server1"] = DynamicMCPServerConfig(
            name="server1",
            command="cmd1",
            tags=["production", "api"]
        )
        config._dynamic_servers["server2"] = DynamicMCPServerConfig(
            name="server2",
            command="cmd2",
            tags=["test", "local"]
        )
        config.mcp_servers.update(config._dynamic_servers)
        
        # List all servers
        all_servers = config.list_servers()
        assert len(all_servers) >= 2  # May include base servers
        
        # Filter by tags
        prod_servers = config.list_servers(tags=["production"])
        assert len(prod_servers) == 1
        assert prod_servers[0]["name"] == "server1"
        
        test_servers = config.list_servers(tags=["test"])
        assert len(test_servers) == 1
        assert test_servers[0]["name"] == "server2"
        
    @pytest.mark.asyncio
    async def test_validate_server_config(self):
        """Test server configuration validation"""
        config = DynamicMCPConfig(self.temp_dir)
        
        # Valid hosted server
        await config.add_server(
            name="valid-hosted",
            command="npx",
            connection_type="hosted",
            endpoint="https://mcp.example.com"
        )
        
        # Invalid hosted server (no endpoint)
        with pytest.raises(ValueError, match="require an endpoint"):
            await config.add_server(
                name="invalid-hosted",
                command="npx",
                connection_type="hosted"
            )
            
        # Invalid local server (no command)
        with pytest.raises(ValueError, match="require a command"):
            await config.add_server(
                name="invalid-local",
                command="",
                connection_type="local"
            )
            
    def test_export_import_configuration(self):
        """Test configuration export and import"""
        config1 = DynamicMCPConfig(self.temp_dir)
        
        # Add some servers
        config1._dynamic_servers["server1"] = DynamicMCPServerConfig(
            name="server1",
            command="cmd1"
        )
        config1._dynamic_servers["server2"] = DynamicMCPServerConfig(
            name="server2",
            command="cmd2"
        )
        config1.mcp_servers.update(config1._dynamic_servers)
        
        # Export configuration
        exported = config1.export_configuration()
        
        assert "dynamic_servers" in exported
        assert len(exported["dynamic_servers"]) == 2
        assert "exported_at" in exported
        
        # Import into new config
        config2 = DynamicMCPConfig(self.temp_dir + "_2")
        config2.import_configuration(exported)
        
        assert "server1" in config2.mcp_servers
        assert "server2" in config2.mcp_servers
        assert config2.mcp_servers["server1"].command == "cmd1"
        
    @pytest.mark.asyncio
    async def test_registry_integration(self):
        """Test integration with MCP registry"""
        config = DynamicMCPConfig(self.temp_dir)
        
        # Mock registry
        mock_registry = Mock()
        mock_registry.register_server = AsyncMock()
        mock_registry.unregister_server = AsyncMock()
        mock_registry.update_server = AsyncMock()
        mock_registry.get_server_status = Mock(return_value="connected")
        
        config.set_registry(mock_registry)
        
        # Add server should register with registry
        await config.add_server("test-mcp", "python")
        mock_registry.register_server.assert_called_once()
        
        # Update server should update registry
        await config.update_server("test-mcp", command="node")
        mock_registry.update_server.assert_called_once()
        
        # Remove server should unregister from registry
        await config.remove_server("test-mcp")
        mock_registry.unregister_server.assert_called_once()
        
    def test_singleton_behavior(self):
        """Test that get_dynamic_mcp_config returns singleton"""
        config1 = get_dynamic_mcp_config()
        config2 = get_dynamic_mcp_config()
        
        assert config1 is config2