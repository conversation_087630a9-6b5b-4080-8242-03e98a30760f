"""
Response Processor Service

Handles processing of MCP tool results and generates user-friendly responses
using LLM integration. This is part of Phase 5 of the FastAgent to MCP migration.
"""
import json
import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import re
from pathlib import Path

# Import LLM client (we'll use OpenAI API as example)
import os
import aiohttp
from config.model_config import get_model_for_agent, get_model_config
from services.config import get_settings

logger = logging.getLogger(__name__)


class ResponseProcessor:
    """
    Processes MCP tool results and generates formatted responses
    """
    
    def __init__(self, model_name: Optional[str] = None):
        """
        Initialize response processor
        
        Args:
            model_name: Optional model override, defaults to orchestrator config
        """
        self.model_config = get_model_config()
        self.model_name = model_name or get_model_for_agent("orchestrator")
        
        # Get API key from validated settings
        settings = get_settings()
        self.api_key = settings.openai_api_key
        self.base_url = "https://api.openai.com/v1/chat/completions"
        
        logger.info(f"Response processor initialized with model: {self.model_name}")
        logger.info(f"API key configured: {'Yes' if self.api_key and self.api_key.startswith('sk-') else 'No'}")
    
    async def process_tool_results(self, 
                                 user_request: str,
                                 tool_results: List[Dict[str, Any]],
                                 context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process tool execution results and generate user response
        
        Args:
            user_request: Original user request
            tool_results: List of tool execution results
            context: Additional context
            
        Returns:
            Processed response with formatted output
        """
        try:
            # Extract and format tool data
            formatted_results = self._format_tool_results(tool_results)
            
            # Handle different response types
            response_type = self._determine_response_type(tool_results)
            
            if response_type == "error":
                return await self._handle_error_response(user_request, tool_results)
            elif response_type == "data":
                return await self._generate_data_response(user_request, formatted_results, context)
            elif response_type == "action":
                return await self._generate_action_response(user_request, formatted_results, context)
            else:
                return await self._generate_generic_response(user_request, formatted_results, context)
                
        except Exception as e:
            logger.error(f"Failed to process tool results: {str(e)}", exc_info=True)
            return {
                "response": "I encountered an error while processing the results. Please try again.",
                "error": str(e),
                "type": "error"
            }
    
    def _format_tool_results(self, tool_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format raw tool results for processing"""
        formatted = []
        
        for result in tool_results:
            if result.get("success", False):
                # Extract meaningful data from result
                tool_data = result.get("result", {})
                
                # Handle different result structures
                if isinstance(tool_data, dict):
                    # Extract content if present (MCP format)
                    if "content" in tool_data:
                        actual_data = tool_data["content"]
                    else:
                        actual_data = tool_data
                else:
                    actual_data = tool_data
                
                formatted.append({
                    "tool": result.get("tool", "unknown"),
                    "data": actual_data,
                    "success": True
                })
            else:
                formatted.append({
                    "tool": result.get("tool", "unknown"),
                    "error": result.get("error", "Unknown error"),
                    "success": False
                })
        
        return formatted
    
    def _determine_response_type(self, tool_results: List[Dict[str, Any]]) -> str:
        """Determine the type of response needed based on results"""
        if all(not r.get("success", False) for r in tool_results):
            return "error"
        
        # Check if results contain data vs actions
        has_data = any(
            r.get("success") and isinstance(r.get("result"), (dict, list))
            for r in tool_results
        )
        
        has_action = any(
            r.get("success") and "execute" in str(r.get("tool", "")).lower()
            for r in tool_results
        )
        
        if has_data:
            return "data"
        elif has_action:
            return "action"
        else:
            return "generic"
    
    async def _generate_data_response(self, 
                                    request: str, 
                                    results: List[Dict[str, Any]], 
                                    context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Generate response for data queries"""
        # Build prompt for LLM
        system_prompt = """You are a financial data assistant. Your job is to summarize and present data from tool executions in a clear, concise manner.

Key guidelines:
- Present data in an easy-to-read format
- Highlight important numbers and trends
- Use bullet points or tables when appropriate
- Be concise but informative
- Include relevant context
"""
        
        # Format tool results for prompt
        results_text = self._format_results_for_llm(results)
        
        user_prompt = f"""User Request: {request}

Tool Results:
{results_text}

Please provide a clear, informative response that answers the user's request based on the tool results."""
        
        # Call LLM
        llm_response = await self._call_llm(system_prompt, user_prompt)
        
        return {
            "response": llm_response,
            "type": "data",
            "tools_used": [r["tool"] for r in results if r.get("success")],
            "metadata": {
                "result_count": len(results),
                "success_count": sum(1 for r in results if r.get("success"))
            }
        }
    
    async def _generate_action_response(self,
                                      request: str,
                                      results: List[Dict[str, Any]],
                                      context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Generate response for action executions"""
        system_prompt = """You are a financial operations assistant. Your job is to confirm successful actions and explain what was done.

Key guidelines:
- Clearly state what actions were completed
- Mention any important details or IDs
- Provide next steps if relevant
- Be professional and reassuring
"""
        
        results_text = self._format_results_for_llm(results)
        
        user_prompt = f"""User Request: {request}

Action Results:
{results_text}

Please provide a clear confirmation of what was done and any relevant details."""
        
        llm_response = await self._call_llm(system_prompt, user_prompt)
        
        return {
            "response": llm_response,
            "type": "action",
            "tools_used": [r["tool"] for r in results if r.get("success")],
            "metadata": {
                "actions_completed": sum(1 for r in results if r.get("success"))
            }
        }
    
    async def _handle_error_response(self,
                                   request: str,
                                   results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Handle error responses"""
        errors = [
            f"{r.get('tool', 'Unknown tool')}: {r.get('error', 'Unknown error')}"
            for r in results if not r.get("success", False)
        ]
        
        response = "I encountered some issues while processing your request:\n\n"
        for error in errors:
            response += f"• {error}\n"
        response += "\nPlease check your request and try again, or contact support if the issue persists."
        
        return {
            "response": response,
            "type": "error",
            "errors": errors
        }
    
    async def _generate_generic_response(self,
                                       request: str,
                                       results: List[Dict[str, Any]],
                                       context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Generate generic response"""
        successful_tools = [r["tool"] for r in results if r.get("success")]
        
        if successful_tools:
            response = f"I've completed the requested operations using: {', '.join(successful_tools)}"
        else:
            response = "I've processed your request. Please let me know if you need anything else."
        
        return {
            "response": response,
            "type": "generic",
            "tools_used": successful_tools
        }
    
    def _format_results_for_llm(self, results: List[Dict[str, Any]]) -> str:
        """Format results for LLM consumption"""
        formatted_parts = []
        
        for result in results:
            if result.get("success"):
                tool_name = result["tool"].replace("mcp__", "").replace("__", " - ")
                data = result.get("data", {})
                
                # Format based on data type
                if isinstance(data, dict):
                    formatted_parts.append(f"\n{tool_name}:")
                    for key, value in data.items():
                        if isinstance(value, (list, dict)):
                            formatted_parts.append(f"  {key}: {json.dumps(value, indent=2)}")
                        else:
                            formatted_parts.append(f"  {key}: {value}")
                elif isinstance(data, list):
                    formatted_parts.append(f"\n{tool_name}: {len(data)} items")
                    # Show first few items
                    for item in data[:3]:
                        formatted_parts.append(f"  - {item}")
                    if len(data) > 3:
                        formatted_parts.append(f"  ... and {len(data) - 3} more")
                else:
                    formatted_parts.append(f"\n{tool_name}: {data}")
            else:
                formatted_parts.append(f"\n{result['tool']} (FAILED): {result.get('error', 'Unknown error')}")
        
        return "\n".join(formatted_parts)
    
    async def _call_llm(self, system_prompt: str, user_prompt: str) -> str:
        """Call LLM API for response generation"""
        logger.debug(f"ResponseProcessor API key available: {bool(self.api_key)}")
        
        if not self.api_key:
            logger.warning("No OpenAI API key found, using fallback response")
            logger.info(f"Fallback for prompt: {user_prompt[:100]}...")
            return self._generate_fallback_response(user_prompt)
        
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": self.model_name,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "temperature": self.model_config.get_temperature(),
                "max_tokens": self.model_config.get_max_tokens()
            }
            
            logger.info(f"ResponseProcessor calling OpenAI API with model: {self.model_name}")
            logger.debug(f"Temperature: {self.model_config.get_temperature()}, Max tokens: {self.model_config.get_max_tokens()}")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.base_url, json=payload, headers=headers) as response:
                    logger.info(f"ResponseProcessor OpenAI API response status: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        content = data["choices"][0]["message"]["content"]
                        logger.info(f"ResponseProcessor API call successful, response length: {len(content)}")
                        logger.debug(f"Response content: {content[:200]}...")
                        return content
                    else:
                        error_text = await response.text()
                        logger.error(f"LLM API error: {response.status} - {error_text}")
                        logger.error(f"Failed payload: {json.dumps(payload, indent=2)}")
                        return self._generate_fallback_response(user_prompt)
                        
        except aiohttp.ClientError as e:
            logger.error(f"Network error calling LLM: {str(e)}", exc_info=True)
            return self._generate_fallback_response(user_prompt)
        except Exception as e:
            logger.error(f"Failed to call LLM: {str(e)}", exc_info=True)
            logger.error(f"Exception type: {type(e).__name__}")
            return self._generate_fallback_response(user_prompt)
    
    def _generate_fallback_response(self, prompt: str) -> str:
        """Generate a basic response without LLM"""
        prompt_lower = prompt.lower()
        
        # Handle common queries without LLM
        if any(word in prompt_lower for word in ['weather', 'temperature', 'forecast']):
            return "I'm not able to provide weather information. I'm specialized in accounting and financial operations. Is there anything related to your financial data I can help you with?"
        elif any(word in prompt_lower for word in ['hello', 'hi', 'hey', 'good morning']):
            return "Hello! I'm your AI assistant for accounting and financial operations. I can help with financial reports, account searches, and more. What would you like to know?"
        elif 'help' in prompt_lower or 'what can you' in prompt_lower:
            return "I can help you with financial summaries, account searches, reports, and other accounting operations. What specific information are you looking for?"
        elif "Tool Results:" in prompt:
            # Basic parsing of results
            return "I've completed the requested operations. The tools have been executed successfully."
        else:
            return "I can help with accounting and financial operations. Could you please specify what information you're looking for?"
    
    async def format_workflow_response(self,
                                     workflow_name: str,
                                     workflow_result: Dict[str, Any],
                                     context: Optional[Dict[str, Any]] = None) -> str:
        """
        Format response for workflow executions
        
        Args:
            workflow_name: Name of the executed workflow
            workflow_result: Workflow execution results
            context: Additional context
            
        Returns:
            Formatted response string
        """
        system_prompt = """You are a workflow execution assistant. Summarize workflow results clearly and concisely.

Guidelines:
- State the workflow name and overall status
- List completed steps
- Highlight key results
- Mention any failures or issues
- Suggest next steps if applicable
"""
        
        # Extract workflow data
        status = workflow_result.get("status", "unknown")
        completed_steps = workflow_result.get("completed_steps", [])
        failed_steps = workflow_result.get("failed_steps", [])
        final_results = workflow_result.get("final_results", {})
        
        user_prompt = f"""Workflow: {workflow_name.replace('_', ' ').title()}
Status: {status}

Completed Steps: {len(completed_steps)}
{chr(10).join(f"✓ {step}" for step in completed_steps[:5])}
{f"... and {len(completed_steps) - 5} more" if len(completed_steps) > 5 else ""}

Failed Steps: {len(failed_steps)}
{chr(10).join(f"✗ {step}" for step in failed_steps)}

Key Results:
{json.dumps(final_results, indent=2) if final_results else "No results available"}

Please provide a clear summary of the workflow execution."""
        
        return await self._call_llm(system_prompt, user_prompt)
    
    async def format_parallel_response(self,
                                     parallel_result: Dict[str, Any],
                                     context: Optional[Dict[str, Any]] = None) -> str:
        """
        Format response for parallel tool executions
        
        Args:
            parallel_result: Parallel execution results
            context: Additional context
            
        Returns:
            Formatted response string
        """
        system_prompt = """You are a parallel operations assistant. Summarize the results of multiple parallel tool executions.

Guidelines:
- Group related results together
- Highlight the most important findings
- Be concise but comprehensive
- Use formatting to improve readability
"""
        
        # Extract parallel execution data
        results = parallel_result.get("results", {})
        errors = parallel_result.get("errors", {})
        aggregated = parallel_result.get("aggregated", {})
        metadata = parallel_result.get("metadata", {})
        
        user_prompt = f"""Parallel Execution Results:

Successful Operations: {len(results)}
Failed Operations: {len(errors)}
Total Time: {metadata.get('total_time', 0):.2f}s

Results:
{json.dumps(aggregated, indent=2) if aggregated else json.dumps(results, indent=2)}

Errors:
{json.dumps(errors, indent=2) if errors else "None"}

Please provide a clear summary of the parallel execution results."""
        
        return await self._call_llm(system_prompt, user_prompt)