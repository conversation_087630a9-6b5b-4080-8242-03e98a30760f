# Dynamic Orchestration Migration Guide

## Issue: LLM Integration and Fallback Pattern Improvements

### Problem Statement

The current system has two separate LLM calls that are both failing:
1. Tool selection (via `LLMService.analyze_request()`)
2. Response generation

Both failures stem from:
- No OpenAI API key configured
- Improper fallback behavior that tries LLM first, then falls back to pattern matching
- Missing accounting terminology in pattern matching ("debtor" not recognized)

### Quick Fix Tasks

#### Task 1: Add "debtor" to Pattern Matching ⬜
**File**: `agents/orchestrator.py` - `_extract_tool_calls_from_message()` method

Add "debtor" to the accounts receivable pattern matching:
```python
# Current pattern (line ~674)
if any(phrase in message_lower for phrase in ['owe me', 'owe money', 'outstanding', 'receivable', 'unpaid invoice', 'customer balance']):

# Should be:
if any(phrase in message_lower for phrase in ['owe me', 'owe money', 'outstanding', 'receivable', 'unpaid invoice', 'customer balance', 'debtor', 'who owes']):
```

### Proper Fix Tasks

#### Task 2: Check API Key Before LLM Calls ⬜
**File**: `services/llm_service.py`

1. Add API key validation in `__init__`:
   ```python
   if not self.api_key:
       raise ValueError("OPENAI_API_KEY environment variable is required. No fallback mode available.")
   ```

2. Remove the fallback behavior in `_call_llm()` that returns empty JSON when no API key

#### Task 3: Add Comprehensive Logging ⬜
**Files**: `services/llm_service.py`, `agents/orchestrator.py`

Add logging for:
- API key presence/absence at initialization
- LLM call attempts and responses
- Pattern matching fallback triggers
- Tool execution decisions

#### Task 4: Improve LLM Prompt for Accounting Terms ⬜
**File**: `services/llm_service.py` - `_get_analysis_system_prompt()`

Add accounting context to the system prompt:
```
ACCOUNTING TERMINOLOGY:
- "Debtors" = "Accounts Receivable" = "People/companies who owe money"
- "Creditors" = "Accounts Payable" = "People/companies we owe money to"
- When users ask about debtors, they're asking about accounts receivable
```

### Better Architecture Tasks

#### Task 5: Implement Single Intelligent Layer ⬜
**Files**: Create new `services/intelligent_orchestrator.py`

Instead of multiple fallback layers, create ONE intelligent orchestration layer that:
1. **If API key present**: Use LLM for everything (tool selection + response generation)
2. **If no API key**: Fail immediately with clear error message

Key principles:
- No silent fallbacks that degrade quality
- Clear error messages when API key missing
- Single source of truth for orchestration logic

#### Task 6: Enforce API Key Requirement ⬜
**Files**: `config/base.py`, `main.py`, `api/server.py`

1. Check for API key at startup
2. Refuse to start without valid OpenAI API key
3. Add validation endpoint to check API key status
4. Clear error messages guiding users to set the key

#### Task 7: Unify Tool Selection and Response Generation ⬜
**Files**: `services/intelligent_orchestrator.py`

Create a single LLM call that handles both:
- Tool selection based on user request
- Response generation with tool results

This avoids the current two-call pattern that can fail independently.

### Implementation Order

1. **Quick Fix First** (Tasks 1-4): Get the system working better immediately
2. **Architecture Improvement** (Tasks 5-7): Implement the proper single-layer solution

### Testing Requirements

1. Test with API key present - should use LLM
2. Test without API key - should fail with clear error
3. Test accounting terminology: "debtors", "who owes me money", etc.
4. Test tool selection accuracy
5. Test response quality

### Success Criteria

1. System recognizes "debtor" and other accounting terms
2. Clear error when API key is missing (no silent fallback)
3. Single intelligent layer handles all orchestration
4. Better user experience with consistent quality
