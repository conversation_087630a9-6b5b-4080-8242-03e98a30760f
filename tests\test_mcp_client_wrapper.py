"""
Unit tests for MCPClientWrapper.
Tests the MCP client wrapper functionality in isolation.
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from typing import Dict, Any

from services.mcp_client_wrapper import MCPClientWrapper


class TestMCPClientWrapper:
    """Test MCPClientWrapper functionality."""
    
    @pytest.fixture
    def stdio_config(self):
        """Configuration for stdio transport."""
        return {
            "command": "node",
            "args": ["server.js"],
            "env": {"API_KEY": "test-key"}
        }
    
    @pytest.fixture
    def sse_config(self):
        """Configuration for SSE transport."""
        return {
            "url": "http://localhost:8080/sse",
            "transport": "sse"
        }
    
    @pytest.fixture
    def http_config(self):
        """Configuration for HTTP transport."""
        return {
            "url": "http://localhost:8080",
            "transport": "http"
        }
    
    def test_transport_detection(self, stdio_config, sse_config, http_config):
        """Test automatic transport type detection."""
        # Test stdio config
        wrapper_stdio = MCPClientWrapper(stdio_config)
        assert wrapper_stdio.config == stdio_config
        assert wrapper_stdio.server_name == "unknown"  # default when name not provided
        
        # Test SSE config
        wrapper_sse = MCPClientWrapper(sse_config)
        assert wrapper_sse.config == sse_config
        
        # Test HTTP config
        wrapper_http = MCPClientWrapper(http_config)
        assert wrapper_http.config == http_config
        
        # Test with server name
        config_with_name = {"name": "test-server", "command": "node"}
        wrapper_named = MCPClientWrapper(config_with_name)
        assert wrapper_named.server_name == "test-server"
    
    @pytest.mark.asyncio
    async def test_stdio_connection(self, stdio_config):
        """Test stdio transport connection."""
        with patch('services.mcp_client_wrapper.Client') as MockClient:
            mock_client = Mock()
            MockClient.return_value = mock_client
            
            wrapper = MCPClientWrapper(stdio_config)
            result = await wrapper.connect()
            
            assert result is True
            assert wrapper._connected is True
            assert wrapper.client is not None
            MockClient.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_sse_connection(self, sse_config):
        """Test SSE transport connection."""
        # Update config to have metadata
        sse_config['metadata'] = {'transport': 'sse', 'url': sse_config['url']}
        
        with patch('services.mcp_client_wrapper.Client') as MockClient, \
             patch('fastmcp.client.transports.SSETransport') as MockSSETransport:
            mock_client = Mock()
            MockClient.return_value = mock_client
            mock_transport = Mock()
            MockSSETransport.return_value = mock_transport
            
            wrapper = MCPClientWrapper(sse_config)
            result = await wrapper.connect()
            
            assert result is True
            MockSSETransport.assert_called_once_with(
                url="http://localhost:8080/sse",
                headers={}
            )
            MockClient.assert_called_once_with(mock_transport)
    
    @pytest.mark.asyncio
    async def test_list_tools(self, stdio_config):
        """Test listing tools from MCP server."""
        with patch('services.mcp_client_wrapper.Client') as MockClient:
            # Setup mock client
            mock_client = Mock()
            mock_context = AsyncMock()
            # Mock the list_tools result to match MCP protocol
            mock_tool1 = Mock()
            mock_tool1.name = "search_data"
            mock_tool1.description = "Search for data"
            mock_tool1.input_schema = {"type": "object", "properties": {"query": {"type": "string"}}}
            
            mock_tool2 = Mock()
            mock_tool2.name = "get_summary"
            mock_tool2.description = "Get data summary"
            mock_tool2.input_schema = {"type": "object"}
            
            mock_tools_result = Mock()
            mock_tools_result.tools = [mock_tool1, mock_tool2]
            mock_context.list_tools = AsyncMock(return_value=mock_tools_result)
            mock_client.__aenter__ = AsyncMock(return_value=mock_context)
            mock_client.__aexit__ = AsyncMock(return_value=None)
            MockClient.return_value = mock_client
            
            wrapper = MCPClientWrapper(stdio_config)
            await wrapper.connect()
            
            # Get tools
            tools = await wrapper.list_tools()
            
            assert len(tools) == 2
            assert tools[0].name == "search_data"
            assert tools[1].name == "get_summary"
            
            # Test caching - second call should not hit server
            tools2 = await wrapper.list_tools()
            assert tools2 == tools
            # Since caching is implemented, list_tools on context should only be called once
            mock_context.list_tools.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_call_tool(self, stdio_config):
        """Test calling a tool on MCP server."""
        with patch('services.mcp_client_wrapper.Client') as MockClient:
            # Setup mock client
            mock_client = Mock()
            mock_context = AsyncMock()
            
            # Mock tool result
            mock_result = Mock()
            mock_result.content = [
                Mock(type="text", text="Search results: 42 items found")
            ]
            mock_context.call_tool = AsyncMock(return_value=mock_result)
            
            mock_client.__aenter__ = AsyncMock(return_value=mock_context)
            mock_client.__aexit__ = AsyncMock(return_value=None)
            MockClient.return_value = mock_client
            
            wrapper = MCPClientWrapper(stdio_config)
            await wrapper.connect()
            
            # Call tool
            result = await wrapper.call_tool("search_data", {"query": "test"})
            
            assert result == "Search results: 42 items found"
            mock_context.call_tool.assert_called_once_with(
                "search_data",
                {"query": "test"}
            )
    
    @pytest.mark.asyncio
    async def test_call_tool_with_complex_content(self, stdio_config):
        """Test calling a tool that returns complex content."""
        with patch('services.mcp_client_wrapper.Client') as MockClient:
            # Setup mock client
            mock_client = Mock()
            mock_context = AsyncMock()
            
            # Mock complex result
            mock_result = Mock()
            # Create proper mock objects for content items
            text_item1 = Mock()
            text_item1.type = "text"
            text_item1.text = "Part 1"
            
            text_item2 = Mock()
            text_item2.type = "text"
            text_item2.text = "Part 2"
            
            # For image item, just use a regular mock
            image_item = Mock()
            image_item.type = "image"
            image_item.data = "base64data"
            image_item.mimeType = "image/png"
            # Don't give it a text attribute so it will be skipped
            
            mock_result.content = [text_item1, text_item2]  # Only include text items for now
            mock_context.call_tool = AsyncMock(return_value=mock_result)
            
            mock_client.__aenter__ = AsyncMock(return_value=mock_context)
            mock_client.__aexit__ = AsyncMock(return_value=None)
            MockClient.return_value = mock_client
            
            wrapper = MCPClientWrapper(stdio_config)
            await wrapper.connect()
            
            # Call tool
            result = await wrapper.call_tool("get_report", {})
            
            # Should combine text parts only
            assert result == "Part 1\nPart 2"
    
    @pytest.mark.asyncio
    async def test_health_check(self, stdio_config):
        """Test health check functionality."""
        with patch('services.mcp_client_wrapper.Client') as MockClient:
            # Setup mock client
            mock_client = Mock()
            mock_context = AsyncMock()
            mock_context.list_tools = AsyncMock(return_value=[Mock(name="test")])
            mock_client.__aenter__ = AsyncMock(return_value=mock_context)
            mock_client.__aexit__ = AsyncMock(return_value=None)
            MockClient.return_value = mock_client
            
            wrapper = MCPClientWrapper(stdio_config)
            await wrapper.connect()
            
            # Health check should succeed
            is_healthy = await wrapper.health_check()
            assert is_healthy is True
            
            # Test unhealthy case
            mock_context.list_tools.side_effect = Exception("Connection error")
            is_healthy = await wrapper.health_check()
            assert is_healthy is False
    
    @pytest.mark.asyncio
    async def test_disconnect(self, stdio_config):
        """Test disconnection handling."""
        with patch('services.mcp_client_wrapper.Client') as MockClient:
            mock_client = Mock()
            MockClient.return_value = mock_client
            
            wrapper = MCPClientWrapper(stdio_config)
            await wrapper.connect()
            assert wrapper._connected is True
            assert wrapper.client is not None
            
            # Disconnect should clear client and mark as disconnected
            await wrapper.disconnect()
            assert wrapper.client is None
            assert wrapper._connected is False
    
    @pytest.mark.asyncio
    async def test_error_handling(self, stdio_config):
        """Test error handling in various scenarios."""
        wrapper = MCPClientWrapper(stdio_config)
        
        # Test calling tool without connection
        # The wrapper auto-connects, so we expect a connection error instead
        with patch('services.mcp_client_wrapper.Client') as MockClient:
            MockClient.side_effect = Exception("Could not infer a valid transport")
            with pytest.raises(ConnectionError):
                await wrapper.call_tool("test", {})
        
        # Test listing tools without connection
        # The wrapper auto-connects, so it won't raise RuntimeError
        # Instead it will return empty list since discovery fails
        tools = await wrapper.list_tools()
        assert tools == []  # Empty because connection failed
    
    @pytest.mark.asyncio
    async def test_empty_tool_result(self, stdio_config):
        """Test handling of empty tool results."""
        with patch('services.mcp_client_wrapper.Client') as MockClient:
            # Setup mock client
            mock_client = Mock()
            mock_context = AsyncMock()
            
            # Mock empty result
            mock_result = Mock()
            mock_result.content = []
            mock_context.call_tool = AsyncMock(return_value=mock_result)
            
            mock_client.__aenter__ = AsyncMock(return_value=mock_context)
            mock_client.__aexit__ = AsyncMock(return_value=None)
            MockClient.return_value = mock_client
            
            wrapper = MCPClientWrapper(stdio_config)
            await wrapper.connect()
            
            # Call tool with empty result
            result = await wrapper.call_tool("empty_tool", {})
            # Empty content list returns string representation
            assert result == "[]"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])