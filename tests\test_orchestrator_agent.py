"""
Unit tests for the Dynamic Orchestrator

Note: The legacy orchestrator_agent.py has been removed.
This file now tests the main Dynamic Orchestrator from agents/orchestrator.py
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timezone

from agents.orchestrator import DynamicOrchestrator
from services.mcp_registry import MCPServerRegistry


class TestDynamicOrchestrator:
    """Test suite for DynamicOrchestrator"""
    
    @pytest.fixture
    def mock_registry(self):
        """Create a mock MCP registry"""
        registry = Mock(spec=MCPServerRegistry)
        registry.get_all_servers = Mock(return_value=[])
        return registry
    
    @pytest.fixture
    def orchestrator(self, mock_registry):
        """Create a DynamicOrchestrator instance for testing"""
        return DynamicOrchestrator(registry=mock_registry)
    
    def test_orchestrator_initialization(self, orchestrator):
        """Test that the orchestrator initializes correctly"""
        assert orchestrator.registry is not None
        assert orchestrator.discovery is not None
        assert orchestrator._initialized is False
        assert orchestrator.confidence_scorer is not None
    
    @pytest.mark.asyncio
    async def test_initialize(self, orchestrator):
        """Test orchestrator initialization"""
        # Mock the fast agent creation
        with patch.object(orchestrator, '_create_fast_agent', new_callable=AsyncMock) as mock_create:
            mock_fast_agent = Mock()
            mock_create.return_value = mock_fast_agent
            
            await orchestrator.initialize()
            
            assert orchestrator._initialized is True
            assert orchestrator.fast_agent == mock_fast_agent
            mock_create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_message(self, orchestrator):
        """Test processing a message through the orchestrator"""
        message = "Show me AR aging report"
        
        # Mock the initialization
        orchestrator._initialized = True
        orchestrator.fast_agent = Mock()
        
        # Mock the internal methods
        with patch.object(orchestrator, '_detect_intent', new_callable=AsyncMock) as mock_detect:
            with patch.object(orchestrator, '_select_tools', new_callable=AsyncMock) as mock_select:
                with patch.object(orchestrator, '_execute_tools', new_callable=AsyncMock) as mock_execute:
                    
                    mock_detect.return_value = "ar_aging_report"
                    mock_select.return_value = [{"name": "get_ar_aging", "server": "sage-intacct"}]
                    mock_execute.return_value = {
                        "response": "AR Aging Report generated",
                        "metadata": {"tool": "get_ar_aging"}
                    }
                    
                    result = await orchestrator.process(message)
                    
                    # Verify the result
                    assert result["response"] == "AR Aging Report generated"
                    assert "metadata" in result
                    
                    # Verify the methods were called
                    mock_detect.assert_called_once_with(message)
                    mock_select.assert_called_once()
                    mock_execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_error_handling(self, orchestrator):
        """Test error handling in the orchestrator"""
        message = "Invalid request"
        
        # Mock initialization
        orchestrator._initialized = True
        
        # Mock an error in intent detection
        with patch.object(orchestrator, '_detect_intent', side_effect=Exception("Test error")):
            result = await orchestrator.process(message)
            
            assert "error" in result
            assert "Test error" in result["error"]


# Backward compatibility test
class TestLegacyOrchestrator:
    """Test that the legacy orchestrator has been properly removed"""
    
    def test_legacy_import_fails(self):
        """Test that importing the old orchestrator fails as expected"""
        with pytest.raises(ImportError):
            from agents.intacct.orchestrator_agent import OrchestratorAgent
    
    def test_intacct_module_empty(self):
        """Test that the intacct module is now empty"""
        from agents.intacct import __all__
        assert __all__ == []