"""
Test script to analyze Sage Intacct MCP Server structure and available tools
"""

import os
import re
import ast
from pathlib import Path
from typing import Dict, List, Set


def find_tool_definitions(file_path: str) -> List[Dict[str, str]]:
    """Find tool definitions in a Python file"""
    tools = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Look for various patterns of tool definitions
        patterns = [
            # FastMCP style: @server.tool(...)
            r'@\w+\.tool\s*\([^)]*\)\s*async\s+def\s+(\w+)',
            # MCP style: async def tool_name with specific naming
            r'async\s+def\s+(get_\w+|create_\w+|update_\w+|delete_\w+|list_\w+|search_\w+)',
            # Method style tools
            r'def\s+(get_\w+|create_\w+|update_\w+|delete_\w+|list_\w+|search_\w+)\s*\(',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.MULTILINE)
            for match in matches:
                # Try to extract description from docstring
                doc_pattern = rf'def\s+{match}\s*\([^)]*\):\s*"""([^"]+)"""'
                doc_match = re.search(doc_pattern, content, re.MULTILINE | re.DOTALL)
                description = doc_match.group(1).strip() if doc_match else "No description"
                
                tools.append({
                    'name': match,
                    'file': os.path.basename(file_path),
                    'description': description.split('\n')[0]  # First line of docstring
                })
    
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    
    return tools


def analyze_mcp_server(server_path: str):
    """Analyze the MCP server structure and find all tools"""
    
    print("=" * 80)
    print("Sage Intacct MCP Server Analysis")
    print("=" * 80)
    print(f"\nServer Path: {server_path}")
    
    if not os.path.exists(server_path):
        print(f"ERROR: Server path not found: {server_path}")
        return
    
    # Check directory structure
    print("\n[Directory Structure]")
    print("-" * 40)
    
    src_path = os.path.join(server_path, "src")
    if os.path.exists(src_path):
        for root, dirs, files in os.walk(src_path):
            level = root.replace(src_path, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                if file.endswith('.py') and not file.startswith('__'):
                    print(f"{subindent}{file}")
    
    # Find all Python files
    print("\n[Searching for Tool Definitions]")
    print("-" * 40)
    
    all_tools = []
    modules = {
        'GL': [],
        'AR': [],
        'AP': [],
        'Common': [],
        'Auth': []
    }
    
    # Search in servers directory
    servers_path = os.path.join(src_path, "servers")
    if os.path.exists(servers_path):
        for root, dirs, files in os.walk(servers_path):
            for file in files:
                if file.endswith('.py') and not file.startswith('__'):
                    file_path = os.path.join(root, file)
                    tools = find_tool_definitions(file_path)
                    
                    if tools:
                        # Categorize tools
                        module_name = os.path.basename(os.path.dirname(file_path))
                        if module_name in ['gl', 'ar', 'ap']:
                            modules[module_name.upper()].extend(tools)
                        elif 'cross' in file or 'composite' in file:
                            modules['Common'].extend(tools)
                        else:
                            modules['Common'].extend(tools)
                        
                        all_tools.extend(tools)
                        print(f"\nFound {len(tools)} tools in {os.path.relpath(file_path, server_path)}:")
                        for tool in tools[:3]:  # Show first 3
                            print(f"  - {tool['name']}")
                        if len(tools) > 3:
                            print(f"  ... and {len(tools) - 3} more")
    
    # Summary
    print("\n" + "=" * 80)
    print("SUMMARY")
    print("=" * 80)
    
    print(f"\nTotal tools found: {len(all_tools)}")
    print("\nTools by module:")
    for module, tools in modules.items():
        if tools:
            print(f"\n{module} Module: {len(tools)} tools")
            # Show unique tool names
            unique_tools = list(set(tool['name'] for tool in tools))
            for tool_name in sorted(unique_tools)[:5]:
                print(f"  - {tool_name}")
            if len(unique_tools) > 5:
                print(f"  ... and {len(unique_tools) - 5} more")
    
    # Check authentication
    print("\n[Authentication Check]")
    print("-" * 40)
    
    env_vars = {
        "OAuth": ["INTACCT_CLIENT_ID", "INTACCT_CLIENT_SECRET"],
        "Legacy": ["INTACCT_COMPANY_ID", "INTACCT_USER_ID", "INTACCT_SENDER_ID"]
    }
    
    for auth_type, vars in env_vars.items():
        print(f"\n{auth_type} Authentication:")
        configured = sum(1 for var in vars if os.environ.get(var))
        print(f"  {configured}/{len(vars)} variables configured")
        for var in vars:
            status = "[OK]" if os.environ.get(var) else "[NOT SET]"
            print(f"  {status} {var}")
    
    # Check for config files
    print("\n[Configuration Files]")
    print("-" * 40)
    
    config_files = [
        "config/server_config.yaml",
        "config/modules.yaml",
        ".env",
        "pyproject.toml"
    ]
    
    for config_file in config_files:
        file_path = os.path.join(server_path, config_file)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"[OK] {config_file} ({size:,} bytes)")
        else:
            print(f"[X] {config_file} (not found)")
    
    # List all unique tools alphabetically
    print("\n" + "=" * 80)
    print("ALL AVAILABLE TOOLS (Alphabetical)")
    print("=" * 80)
    
    unique_tools = {}
    for tool in all_tools:
        if tool['name'] not in unique_tools:
            unique_tools[tool['name']] = tool
    
    for i, (tool_name, tool_info) in enumerate(sorted(unique_tools.items()), 1):
        print(f"\n{i}. {tool_name}")
        print(f"   File: {tool_info['file']}")
        if tool_info['description'] != "No description":
            print(f"   Description: {tool_info['description']}")


if __name__ == "__main__":
    server_path = r"C:\Users\<USER>\Documents\GitHub\sage-intacct-mcp-server"
    analyze_mcp_server(server_path)
