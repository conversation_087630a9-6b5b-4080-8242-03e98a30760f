#!/usr/bin/env python
"""
CLI Tool for Testing AI Workspace Agents

This script demonstrates how to test agents directly from the command line
using the fast-agent framework.

Usage:
    python tools/agent_cli.py                    # Interactive mode with orchestrator
    python tools/agent_cli.py --message "test"   # Send single message
    python tools/agent_cli.py --model gpt-4      # Specify model
"""

import asyncio
import argparse
import sys
from pathlib import Path

# Add project root to path (we're now in tools/)
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import the orchestrator
from agents.orchestrator import DynamicOrchestrator
from services.agent_registration import register_all_agents
from services.agent_manager import get_agent_manager


async def test_agent(agent_name: str, message: str = None, model: str = None):
    """Test an agent with optional message and model"""
    
    print(f"\n{'='*60}")
    print(f"Testing {agent_name.upper()} Agent")
    print(f"{'='*60}\n")
    
    # Select agent
    if agent_name == "gl":
        agent = GLAgent()
    elif agent_name == "ar":
        agent = ARAgent()
    else:
        print(f"Unknown agent: {agent_name}")
        return
    
    # If model specified, it would be passed to fast-agent
    # (This would require modification of agent initialization)
    if model:
        print(f"Using model: {model}")
    
    try:
        if message:
            # Send single message
            print(f"Sending message: {message}")
            response = await agent.send({"role": "user", "content": message})
            print(f"\nResponse: {response['content']}")
        else:
            # Interactive mode
            print("Starting interactive chat (type 'exit' to quit)...")
            print("-" * 60)
            
            while True:
                user_input = input("\nYou: ")
                if user_input.lower() in ['exit', 'quit', 'bye']:
                    print("Goodbye!")
                    break
                
                response = await agent.send({"role": "user", "content": user_input})
                print(f"\nAgent: {response['content']}")
                
                if response.get('metadata', {}).get('requires_human_input'):
                    print("\n[Agent is requesting additional information]")
    
    except Exception as e:
        print(f"\nError: {e}")
        print("\nNote: Make sure you have:")
        print("1. Set up LLM API keys in fastagent.secrets.yaml")
        print("2. Configured Intacct credentials in environment")
        print("3. Started any required MCP servers")


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="Test AI Workspace Agents from CLI"
    )
    parser.add_argument(
        "--agent",
        choices=["gl", "ar"],
        default="gl",
        help="Agent to test (default: gl)"
    )
    parser.add_argument(
        "--message",
        help="Send a single message (non-interactive)"
    )
    parser.add_argument(
        "--model",
        help="Specify LLM model (e.g., gpt-4, sonnet)"
    )
    
    args = parser.parse_args()
    
    # Run the test
    asyncio.run(test_agent(args.agent, args.message, args.model))


if __name__ == "__main__":
    main()
