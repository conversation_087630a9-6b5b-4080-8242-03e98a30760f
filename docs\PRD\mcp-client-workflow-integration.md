# MCP Client Workflow Integration PRD

## Problem Statement
The current system bypasses MCP client's native multi-step workflow capabilities by doing single-shot tool execution. This prevents proper error handling, tool chaining, and retry logic that MCP clients provide by design.

**Root Issue: Architectural Over-Engineering**
- We built WorkflowManager to replicate MCP client features
- Enhanced LLM Service returns single tool calls instead of delegating to MCP client
- The FastMCP client already handles multi-step workflows, auth retries, and error recovery

**Current Broken Flow:**
```
User Query → Tool Filtering → Single Tool Selection → Direct Execution → End
```

**Correct MCP Flow:**
```
User Query → Tool Filtering → MCP Client Delegation → Native Multi-step Orchestration → Complete Response
```

## Core Insight
**FastMCP Client Already Provides:**
- Multi-step tool chaining and orchestration
- Auth error handling with automatic retries
- Context persistence across related tool calls
- Native MCP protocol workflow behavior

**Our WorkflowManager is redundant** - it reimplements what MCP clients do natively.

## Solution: Direct MCP Client Delegation

### Architecture Simplification
1. **Keep intelligent filtering** (95% token reduction)
2. **Remove WorkflowManager complexity** (redundant with MCP client)
3. **Delegate to MCP client** with filtered tools
4. **Let MCP handle orchestration** as designed

### Implementation Approach

**Current Problem Code** (`agents/orchestrator.py:196`):
```python
# WRONG: Single-shot execution bypassing MCP client
result = await self.tool_executor.execute_tool_directly(
    tool_call['tool'],
    tool_call.get('parameters', {})
)
```

**Correct Solution**:
```python
# RIGHT: Delegate to MCP client with filtered tools
return await self._delegate_to_mcp_client(message, filtered_tools, context)
```

## Implementation Tasks

### Task 1: Remove Single-Shot Execution ✅
- [x] **Research**: Identified `orchestrator.py:_process_with_llm()` as bypass point
- [x] Replace single tool execution with MCP client delegation
- [x] Modify Enhanced LLM Service to support delegation mode

### Task 2: MCP Client Delegation Interface ✅
- [x] **Research**: `MCPClientWrapper` already exists with proper capabilities
- [x] Create delegation method in orchestrator (`_delegate_to_mcp_client()`)
- [x] Pass filtered tools + user query to MCP client
- [x] Let MCP client handle multi-step scenarios natively

### Task 3: Intelligent Filtering Integration ✅
- [x] **Research**: `EnhancedLLMServiceWithFiltering` provides 95% token reduction
- [x] Ensure filtered tools maintain MCP protocol compliance
- [x] Preserve filtering performance benefits
- [x] Enhanced filtering metadata to include server information for delegation

### Task 4: Architecture Cleanup ✅
- [x] Remove/deprecate WorkflowManager (redundant with MCP client)
- [x] Simplify orchestrator flow
- [x] Update tool execution paths

### Task 5: Testing & Validation ✅
- [x] Test multi-step scenarios (accounts payable query → auth workflow)
- [x] Verify native MCP error handling and retries work
- [x] Ensure filtering performance is maintained (87.9% token reduction confirmed)
- [x] **REAL-WORLD TEST PASSED**: Sage Business Cloud Accounting authentication flow executed successfully

## Success Criteria
- **Multi-step workflows work natively** (auth errors trigger retries, related tools get chained)
- **95% token reduction maintained** through intelligent filtering
- **Simplified architecture** (remove WorkflowManager redundancy)
- **Native MCP compliance** (error handling, retries, tool chaining work as designed)
- **Performance improvement** (no abstraction overhead)

## Key Files to Modify

### Primary Changes
- `agents/orchestrator.py:172-236` - Replace tool execution with MCP delegation
- `services/enhanced_llm_service_with_filtering.py` - Support delegation mode

### Secondary Changes  
- `agents/orchestrator_modules/workflow_manager.py` - Deprecate (redundant)
- `services/mcp_client_wrapper.py` - Enhance delegation interface if needed

## Architecture Benefits
1. **No Duplicate Logic** - Stop reimplementing MCP client features
2. **Native MCP Behavior** - Auth, retries, chaining work as designed
3. **Standards Compliant** - Uses MCP protocol as intended
4. **Simpler Codebase** - Remove WorkflowManager complexity
5. **Better Performance** - Direct client usage, no abstraction overhead
6. **Maintainability** - Follow MCP standards instead of custom implementations