"""
Enhanced LLM Service for Unified Orchestration

This service combines request analysis, tool selection, and response generation
into a single intelligent layer. It eliminates the need for pattern matching
fallbacks by making one comprehensive LLM call that understands the full context.

Part of Phase 3: Unified LLM Architecture.
"""
import json
import logging
from typing import Dict, Any, List, Optional, Tuple, Union
import aiohttp
from datetime import datetime

from config.model_config import get_model_for_agent, get_model_config
from services.config import get_settings

logger = logging.getLogger(__name__)


class EnhancedLLMService:
    """
    Enhanced LLM service that combines analysis and response generation
    into a single, context-aware layer.
    """
    
    def __init__(self, model_name: Optional[str] = None):
        """
        Initialize enhanced LLM service
        
        Args:
            model_name: Optional model override
        """
        self.model_config = get_model_config()
        self.model_name = model_name or get_model_for_agent("orchestrator")
        
        # Get API key from validated settings
        settings = get_settings()
        self.api_key = settings.openai_api_key
        self.base_url = "https://api.openai.com/v1/chat/completions"
        
        # Financial terminology mappings
        self.financial_terms = {
            "debtors": ["accounts receivable", "customers who owe money", "outstanding receivables", "AR", "people who owe us", "money owed to us"],
            "creditors": ["accounts payable", "vendors we owe", "outstanding payables", "AP", "people we owe", "money we owe"],
            "revenue": ["sales", "income", "turnover", "top line", "gross sales", "total sales"],
            "profit": ["net income", "bottom line", "earnings", "net profit", "profit margin"],
            "cash flow": ["cash movement", "liquidity", "cash position", "working capital", "cash in/out"],
            "trial balance": ["TB", "account balances", "ledger balances", "GL balances"],
            "journal entry": ["JE", "journal", "GL entry", "accounting entry", "ledger entry"],
            "invoice": ["bill", "customer invoice", "sales invoice", "billing", "statement"],
            "payment": ["receipt", "collection", "settlement", "payment received", "cash receipt"],
            "period": ["month", "fiscal period", "accounting period", "financial period"],
            "YTD": ["year to date", "year-to-date", "yearly cumulative", "this year", "current year"],
            "MTD": ["month to date", "month-to-date", "monthly cumulative", "this month", "current month"],
            "variance": ["difference", "budget vs actual", "deviation", "delta", "gap"],
            "reconciliation": ["recon", "matching", "balance verification", "account reconciliation"],
            "close": ["month-end close", "period close", "close the books", "closing process"],
            "aging": ["aged receivables", "aged payables", "days outstanding", "overdue analysis"],
            "balance sheet": ["BS", "statement of financial position", "assets and liabilities"],
            "income statement": ["P&L", "profit and loss", "statement of operations", "earnings statement"],
            "customer": ["client", "account", "buyer", "purchaser"],
            "vendor": ["supplier", "provider", "seller", "merchant"],
            "outstanding": ["unpaid", "open", "unsettled", "pending", "due"]
        }
        
        logger.info(f"Enhanced LLM service initialized with model: {self.model_name}")
        logger.info(f"API key configured: {'Yes' if self.api_key and self.api_key.startswith('sk-') else 'No'}")
    
    async def analyze_and_respond(self, 
                                 message: str,
                                 tool_catalog: Dict[str, Any],
                                 context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analyze user request and generate response in a single LLM call.
        This method handles:
        1. Intent understanding with financial terminology
        2. Tool selection and parameter determination
        3. Response generation (either tool execution plan or direct response)
        
        Args:
            message: User's request
            tool_catalog: Complete catalog of available tools
            context: Additional context
            
        Returns:
            Complete analysis and response plan
        """
        logger.info(f"Analyzing and generating response for: {message[:100]}...")
        logger.debug(f"Available tools count: {len(tool_catalog)}")
        
        try:
            # Build comprehensive prompt with all necessary context
            system_prompt = self._build_comprehensive_system_prompt()
            user_prompt = self._build_unified_prompt(message, tool_catalog, context)
            
            logger.info("Making unified LLM call for analysis and response...")
            logger.debug(f"System prompt length: {len(system_prompt)} chars")
            logger.debug(f"User prompt length: {len(user_prompt)} chars")
            
            # Make single LLM call
            response = await self._call_llm(system_prompt, user_prompt)
            logger.info(f"LLM response received, length: {len(response)} chars")
            
            # Parse and validate response
            result = self._parse_unified_response(response)
            logger.info(f"Parsed result type: {result.get('response_type')}")
            
            # Validate tool calls if present
            if result.get("tool_calls"):
                validated_calls = self._validate_and_enhance_tool_calls(
                    result["tool_calls"], 
                    tool_catalog
                )
                result["tool_calls"] = validated_calls
                logger.info(f"Validated {len(validated_calls)} tool calls")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to analyze and respond: {str(e)}", exc_info=True)
            # Return error response - no fallback to pattern matching
            return {
                "response_type": "error",
                "error_message": f"I encountered an error processing your request: {str(e)}",
                "conversational_response": "I apologize, but I'm having trouble processing your request. Please ensure the system is properly configured and try again.",
                "requires_llm": True
            }
    
    def _build_comprehensive_system_prompt(self) -> str:
        """Build a comprehensive system prompt with all necessary context"""
        financial_context = self._build_financial_terminology_context()
        
        return f"""You are the Sage Intacct AI Workspace Orchestrator - an intelligent financial operations assistant.

## Your Core Capabilities

You understand and can help with:
1. **Financial Operations**: General ledger, accounts receivable/payable, financial reporting
2. **Accounting Terminology**: You understand various ways users might refer to financial concepts
3. **Tool Execution**: You can execute tools to retrieve data and perform operations
4. **Conversational Responses**: You can answer questions without tools when appropriate

## Financial Terminology Understanding

{financial_context}

## Available Tool Categories

### 1. Search and Query Tools
- **search_across_modules**: Find any data across GL, AR, AP modules
  - Use for: finding customers, vendors, transactions, accounts, debtors, creditors

### 2. Financial Reporting Tools
- **get_financial_summary**: Generate financial summaries with date ranges
  - Use for: YTD/MTD reports, revenue summaries, P&L data, financial overviews
- **generate_consolidated_report**: Create detailed financial reports
  - Use for: monthly reports, financial statements, custom period analysis

### 3. Operations Tools
- **execute_month_end_close**: Run month-end closing procedures
  - Use for: period closes, month-end processes, closing the books
- **list_enabled_modules**: Show available system modules
  - Use for: checking capabilities, available features
- **health_check**: Verify system connectivity
  - Use for: system status, troubleshooting

## Example User Queries and Appropriate Responses

### Example 1: Accounts Receivable Query
User: "Who are my debtors?"
Response:
```json
{{
    "response_type": "tool_execution",
    "analysis": "User wants to see customers with outstanding receivables",
    "intent": "accounts_receivable_query",
    "tool_calls": [
        {{
            "tool": "mcp__sage-intacct__search_across_modules",
            "parameters": {{
                "query": "outstanding receivables",
                "modules": ["AR"],
                "limit": 50
            }},
            "purpose": "Find customers with outstanding balances"
        }}
    ],
    "response_preview": "I'll search for customers with outstanding receivables to show you who owes money.",
    "confidence": 0.95
}}
```

### Example 2: Financial Report Request
User: "Show me YTD sales revenue"
Response:
```json
{{
    "response_type": "tool_execution",
    "analysis": "User wants year-to-date sales/revenue information",
    "intent": "financial_report",
    "tool_calls": [
        {{
            "tool": "mcp__sage-intacct__get_financial_summary",
            "parameters": {{
                "start_date": "2025-01-01",
                "end_date": "2025-06-06",
                "include_modules": ["GL", "AR"]
            }},
            "purpose": "Get YTD revenue figures"
        }}
    ],
    "response_preview": "I'll retrieve your year-to-date sales revenue figures.",
    "confidence": 0.95
}}
```

### Example 3: Conversational Query
User: "Hello, how are you?"
Response:
```json
{{
    "response_type": "conversational",
    "analysis": "User is greeting the assistant",
    "intent": "greeting",
    "conversational_response": "Hello! I'm doing well, thank you. I'm here to help you with your Sage Intacct financial operations. What can I assist you with today?",
    "confidence": 0.95
}}
```

### Example 4: Non-Financial Query
User: "What's the weather like?"
Response:
```json
{{
    "response_type": "conversational",
    "analysis": "User asking about weather, which is outside my domain",
    "intent": "off_topic",
    "conversational_response": "I'm specialized in Sage Intacct financial operations and don't have access to weather information. However, I can help you with financial reports, account balances, transactions, and other accounting tasks. What financial information can I help you with?",
    "confidence": 0.90
}}
```

## Response Guidelines

For EVERY user request, you must:
1. Understand the intent, considering financial terminology variations
2. Determine if tools are needed or if a conversational response is appropriate
3. If tools are needed, specify exact tool calls with parameters
4. If no tools needed, provide a helpful conversational response
5. Always be polite and professional

## Response Format

You MUST respond with valid JSON in ONE of these formats:

### Format 1: Tool Execution Response
```json
{{
    "response_type": "tool_execution",
    "analysis": "Brief explanation of what the user wants",
    "intent": "primary intent (e.g., 'accounts_receivable_query', 'financial_report', 'general_question')",
    "tool_calls": [
        {{
            "tool": "full_tool_name",
            "parameters": {{}},
            "purpose": "why this tool is needed"
        }}
    ],
    "response_preview": "I'll [brief description of what you'll do with the tools]",
    "confidence": 0.95
}}
```

### Format 2: Conversational Response
```json
{{
    "response_type": "conversational",
    "analysis": "Brief explanation of the query",
    "intent": "general_question",
    "conversational_response": "Your helpful response to the user",
    "confidence": 0.95
}}
```

## Important Rules

1. **ALWAYS respond with valid JSON** - no other text
2. **Understand financial terminology** - "debtors" means accounts receivable
3. **Be helpful** - if asked about weather or non-financial topics, politely redirect to financial topics
4. **Execute tools when needed** - don't just describe what you would do
5. **Use appropriate parameters** - extract dates, filters, and limits from the request
6. **Single response** - choose either tool execution OR conversational response, not both
7. **Use full tool names** - always use the mcp__server__tool format for tool names"""
    
    def _build_financial_terminology_context(self) -> str:
        """Build context about financial terminology"""
        term_lines = []
        for term, synonyms in self.financial_terms.items():
            term_lines.append(f"- **{term}**: also known as {', '.join(synonyms)}")
        
        return "Common financial terms and their variations:\n" + "\n".join(term_lines)
    
    def _build_unified_prompt(self, 
                            message: str, 
                            tool_catalog: Dict[str, Any], 
                            context: Optional[Dict[str, Any]]) -> str:
        """Build unified prompt for analysis and response"""
        # Format tools with complete information
        tools_desc = self._format_tools_comprehensively(tool_catalog)
        
        prompt_parts = [
            f"User Request: {message}",
            "",
            "Available Tools:",
            tools_desc,
            ""
        ]
        
        # Add context if available
        if context:
            prompt_parts.extend([
                "Additional Context:",
                json.dumps(context, indent=2),
                ""
            ])
        
        # Add current date/time for time-based queries
        now = datetime.now()
        prompt_parts.extend([
            f"Current Date: {now.strftime('%Y-%m-%d')}",
            f"Current Time: {now.strftime('%H:%M:%S')}",
            "",
            "Analyze this request and provide the appropriate JSON response."
        ])
        
        return "\n".join(prompt_parts)
    
    def _format_tools_comprehensively(self, tools: Dict[str, Any]) -> str:
        """Format tools with full details for LLM understanding"""
        formatted_tools = []
        
        # Group by category
        tools_by_category = {}
        for tool_name, tool_info in tools.items():
            category = tool_info.category if hasattr(tool_info, 'category') else 'general'
            if category not in tools_by_category:
                tools_by_category[category] = []
            
            # Include full tool information
            tool_desc = {
                "name": f"mcp__{tool_info.server_name}__{tool_info.name}",
                "description": tool_info.description,
                "parameters": self._format_parameters_detailed(tool_info.parameters),
                "examples": self._get_tool_examples(tool_info.name)
            }
            tools_by_category[category].append(tool_desc)
        
        # Format for prompt
        for category, cat_tools in sorted(tools_by_category.items()):
            formatted_tools.append(f"\n### {category.replace('_', ' ').title()} Tools:")
            for tool in cat_tools:
                formatted_tools.append(f"\n**{tool['name']}**")
                formatted_tools.append(f"Description: {tool['description']}")
                if tool['parameters']:
                    formatted_tools.append("Parameters:")
                    for param_name, param_desc in tool['parameters'].items():
                        formatted_tools.append(f"  - {param_name}: {param_desc}")
                if tool['examples']:
                    formatted_tools.append(f"Example uses: {', '.join(tool['examples'])}")
        
        return "\n".join(formatted_tools)
    
    def _format_parameters_detailed(self, params: Any) -> Dict[str, str]:
        """Format parameters with detailed descriptions"""
        if not params:
            return {}
        
        formatted = {}
        if isinstance(params, dict) and "properties" in params:
            # JSON Schema format
            for prop, schema in params.get("properties", {}).items():
                param_type = schema.get("type", "string")
                required = prop in params.get("required", [])
                desc = schema.get("description", "")
                default = schema.get("default", "")
                
                param_desc = f"{param_type}"
                if required:
                    param_desc += " (required)"
                if desc:
                    param_desc += f" - {desc}"
                if default:
                    param_desc += f" [default: {default}]"
                
                formatted[prop] = param_desc
        
        return formatted
    
    def _get_tool_examples(self, tool_name: str) -> List[str]:
        """Get example use cases for specific tools"""
        tool_examples = {
            "search_across_modules": [
                "finding debtors/accounts receivable",
                "who owes us money",
                "searching for specific transactions",
                "locating customer records",
                "finding vendor balances",
                "outstanding invoices",
                "unpaid bills",
                "customer aging reports",
                "vendor payment status"
            ],
            "get_financial_summary": [
                "YTD sales/revenue",
                "year-to-date profit",
                "financial overview",
                "period summaries",
                "monthly revenue",
                "quarterly results",
                "cash position",
                "expense analysis",
                "income statement data"
            ],
            "generate_consolidated_report": [
                "monthly financial reports",
                "custom date range reports",
                "financial statements",
                "balance sheet",
                "P&L statement",
                "cash flow report",
                "trial balance",
                "management reports",
                "board reports"
            ],
            "execute_month_end_close": [
                "close the books",
                "period-end processing",
                "month-end close",
                "financial period close",
                "close accounting period"
            ],
            "health_check": [
                "system status",
                "connectivity verification",
                "is system running",
                "check connection"
            ],
            "list_enabled_modules": [
                "available features",
                "system capabilities",
                "what modules are active",
                "available functionality"
            ]
        }
        
        return tool_examples.get(tool_name, [])
    
    async def _call_llm(self, system_prompt: str, user_prompt: str) -> str:
        """Call LLM API with enhanced error handling"""
        if not self.api_key:
            raise ValueError("OpenAI API key is required for EnhancedLLMService")
        
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": self.model_name,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "temperature": 0.3,  # Lower for consistency
                "max_tokens": 1500,  # Enough for detailed responses
                "response_format": {"type": "json_object"}  # Ensure JSON
            }
            
            logger.info(f"Calling OpenAI API with model: {self.model_name}")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.base_url, json=payload, headers=headers) as response:
                    logger.info(f"OpenAI API response status: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        content = data["choices"][0]["message"]["content"]
                        logger.debug(f"LLM response: {content[:500]}...")
                        return content
                    else:
                        error_text = await response.text()
                        logger.error(f"LLM API error: {response.status} - {error_text}")
                        raise Exception(f"LLM API error: {response.status}")
                        
        except Exception as e:
            logger.error(f"Failed to call LLM: {str(e)}", exc_info=True)
            raise
    
    def _parse_unified_response(self, response: str) -> Dict[str, Any]:
        """Parse the unified LLM response"""
        try:
            result = json.loads(response)
            
            # Ensure required fields
            if "response_type" not in result:
                result["response_type"] = "conversational"
            
            # Add default confidence if missing
            if "confidence" not in result:
                result["confidence"] = 0.8
            
            return result
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse LLM response as JSON: {response}")
            return {
                "response_type": "error",
                "error_message": "Invalid response format",
                "raw_response": response,
                "confidence": 0.0
            }
    
    def _validate_and_enhance_tool_calls(self, 
                                       tool_calls: List[Dict[str, Any]], 
                                       tool_catalog: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Validate and enhance tool calls with proper formatting"""
        validated = []
        
        for call in tool_calls:
            tool_name = call.get("tool", "")
            
            # Find tool in catalog
            tool_found = False
            tool_info = None
            
            for catalog_name, catalog_info in tool_catalog.items():
                full_name = f"mcp__{catalog_info.server_name}__{catalog_info.name}"
                if tool_name == full_name or tool_name == catalog_name:
                    tool_found = True
                    tool_info = catalog_info
                    call["tool"] = full_name  # Ensure full name
                    break
            
            if tool_found and tool_info:
                # Enhance parameters with defaults if needed
                params = call.get("parameters", {})
                
                # Add current date for date-based tools if not specified
                if "start_date" in str(tool_info.parameters) and "start_date" not in params:
                    now = datetime.now()
                    params["start_date"] = now.strftime('%Y-%m-01')
                    params["end_date"] = now.strftime('%Y-%m-%d')
                
                # Clean parameters
                clean_params = {k: v for k, v in params.items() if v is not None}
                call["parameters"] = clean_params
                
                validated.append(call)
            else:
                logger.warning(f"Tool not found in catalog: {tool_name}")
        
        return validated
    
    def requires_llm(self) -> bool:
        """
        Check if LLM is required (always true for enhanced service)
        
        Returns:
            True - this service always requires LLM
        """
        return True
    
    def is_available(self) -> bool:
        """
        Check if the service is available (has API key)
        
        Returns:
            True if API key is configured
        """
        return bool(self.api_key and self.api_key.startswith('sk-'))