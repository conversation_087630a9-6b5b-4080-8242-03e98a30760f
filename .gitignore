# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Environment files
.env
*.env
.env.*

# Secrets - IMPORTANT: Never commit these
fastagent.secrets.yaml
fastagent.secrets.yml
secrets.yaml
secrets.yml
*secrets*

# Logs
*.log
logs/

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# MacOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db

# Project specific
mcp_test_results.json
test_*.py
!tests/test_*.py
