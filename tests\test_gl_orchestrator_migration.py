"""
Test Suite for GL Operations Through Orchestrator

This test validates that all GL agent functionality works correctly
through the dynamic orchestrator without the GL agent.
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.orchestrator import DynamicOrchestrator
from services.mcp_registry import MCPServerRegistry, MCPServerConfig
import yaml
import json


class GLOrchestratorTester:
    """Test harness for GL operations through orchestrator"""
    
    def __init__(self):
        self.orchestrator = None
        self.registry = None
        self.results = []
        
    async def setup(self):
        """Initialize orchestrator with sage-intacct server"""
        # Create registry
        self.registry = MCPServerRegistry()
        
        # Load config
        config_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "mcp.config.yaml"
        )
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Register sage-intacct server
        intacct_config = config['mcp']['servers']['sage-intacct']
        server_config = MCPServerConfig(
            name='sage-intacct',
            command=intacct_config['command'],
            args=intacct_config['args'],
            env=intacct_config.get('env', {})
        )
        
        await self.registry.register_server(server_config)
        
        # Initialize orchestrator with the registry
        self.orchestrator = DynamicOrchestrator(self.registry)
        await self.orchestrator.initialize()
        
        print("✅ Orchestrator initialized successfully\n")

    async def test_balance_inquiry(self):
        """Test account balance inquiries"""
        test_cases = [
            "What is the balance of account 1000?",
            "Show me the balance for account 1100 Cash",
            "How much is in account 2000 Accounts Payable?",
            "Check balance for all cash accounts"
        ]
        
        print("\n🔍 Testing Balance Inquiries:")
        for query in test_cases:
            try:
                result = await self.orchestrator.process(query)
                status = "✅" if result else "❌"
                print(f"{status} {query}")
                self.results.append({
                    "category": "balance_inquiry",
                    "query": query,
                    "success": bool(result),
                    "intent": result.get('intent') if isinstance(result, dict) else None
                })
            except Exception as e:
                print(f"❌ {query} - Error: {str(e)}")
                self.results.append({
                    "category": "balance_inquiry",
                    "query": query,
                    "success": False,
                    "error": str(e)
                })
    
    async def test_journal_entries(self):
        """Test journal entry creation"""
        test_cases = [
            "Create a journal entry debiting cash 1000 for $500 and crediting revenue 4000",
            "Post journal entry: debit office supplies 5100 $250, credit cash 1000 $250",
            "Record depreciation expense $1000 debit 6100, credit accumulated depreciation 1700",
            "Create adjusting entry for accrued wages $5000"
        ]
        
        print("\n📝 Testing Journal Entries:")
        for query in test_cases:
            try:
                result = await self.orchestrator.process(query)
                status = "✅" if result else "❌"
                print(f"{status} {query[:60]}...")
                self.results.append({
                    "category": "journal_entry",
                    "query": query,
                    "success": bool(result),
                    "intent": result.get('intent') if isinstance(result, dict) else None
                })
            except Exception as e:
                print(f"❌ {query[:60]}... - Error: {str(e)}")
                self.results.append({
                    "category": "journal_entry",
                    "query": query,
                    "success": False,
                    "error": str(e)
                })

    async def test_reconciliation(self):
        """Test account reconciliation"""
        test_cases = [
            "Run account reconciliation for bank account 1010",
            "Reconcile cash account 1000 with bank statement",
            "Start reconciliation process for credit card account 2100",
            "Show reconciliation status for all bank accounts"
        ]
        
        print("\n🔄 Testing Reconciliation:")
        for query in test_cases:
            try:
                result = await self.orchestrator.process(query)
                status = "✅" if result else "❌"
                print(f"{status} {query}")
                self.results.append({
                    "category": "reconciliation",
                    "query": query,
                    "success": bool(result),
                    "intent": result.get('intent') if isinstance(result, dict) else None
                })
            except Exception as e:
                print(f"❌ {query} - Error: {str(e)}")
                self.results.append({
                    "category": "reconciliation",
                    "query": query,
                    "success": False,
                    "error": str(e)
                })
    
    async def test_analysis(self):
        """Test financial analysis requests"""
        test_cases = [
            "Show me budget vs actual variance analysis for Q1",
            "Generate trial balance for current month",
            "Analyze expense trends for last 6 months",
            "Create financial statement analysis report",
            "Show me the P&L statement for this year"
        ]
        
        print("\n📊 Testing Financial Analysis:")
        for query in test_cases:
            try:
                result = await self.orchestrator.process(query)
                status = "✅" if result else "❌"
                print(f"{status} {query}")
                self.results.append({
                    "category": "analysis",
                    "query": query,
                    "success": bool(result),
                    "intent": result.get('intent') if isinstance(result, dict) else None
                })
            except Exception as e:
                print(f"❌ {query} - Error: {str(e)}")
                self.results.append({
                    "category": "analysis",
                    "query": query,
                    "success": False,
                    "error": str(e)
                })

    async def test_month_end_close(self):
        """Test month-end close operations"""
        test_cases = [
            "What are the steps for month-end close?",
            "Start month-end close process for November",
            "Check month-end close checklist status",
            "Run preliminary close procedures"
        ]
        
        print("\n🏁 Testing Month-End Close:")
        for query in test_cases:
            try:
                result = await self.orchestrator.process(query)
                status = "✅" if result else "❌"
                print(f"{status} {query}")
                self.results.append({
                    "category": "month_end",
                    "query": query,
                    "success": bool(result),
                    "intent": result.get('intent') if isinstance(result, dict) else None
                })
            except Exception as e:
                print(f"❌ {query} - Error: {str(e)}")
                self.results.append({
                    "category": "month_end",
                    "query": query,
                    "success": False,
                    "error": str(e)
                })
    
    async def run_all_tests(self):
        """Run all GL tests"""
        print("=" * 60)
        print("GL ORCHESTRATOR MIGRATION TEST SUITE")
        print("=" * 60)
        
        await self.setup()
        
        # Run all test categories
        await self.test_balance_inquiry()
        await self.test_journal_entries()
        await self.test_reconciliation()
        await self.test_analysis()
        await self.test_month_end_close()
        
        # Generate summary report
        self.generate_report()
    
    def generate_report(self):
        """Generate test summary report"""
        print("\n" + "=" * 60)
        print("TEST SUMMARY REPORT")
        print("=" * 60)
        
        # Calculate statistics by category
        categories = {}
        for result in self.results:
            cat = result['category']
            if cat not in categories:
                categories[cat] = {'total': 0, 'passed': 0}
            categories[cat]['total'] += 1
            if result['success']:
                categories[cat]['passed'] += 1
        
        # Print category results
        total_tests = 0
        total_passed = 0
        for cat, stats in categories.items():
            total_tests += stats['total']
            total_passed += stats['passed']
            pass_rate = (stats['passed'] / stats['total'] * 100) if stats['total'] > 0 else 0
            status = "✅" if pass_rate == 100 else "⚠️" if pass_rate >= 50 else "❌"
            print(f"{status} {cat}: {stats['passed']}/{stats['total']} ({pass_rate:.0f}%)")
        
        # Overall results
        print("\n" + "-" * 40)
        overall_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        overall_status = "✅" if overall_rate == 100 else "⚠️" if overall_rate >= 80 else "❌"
        print(f"{overall_status} OVERALL: {total_passed}/{total_tests} ({overall_rate:.0f}%)")
        
        # Save detailed results
        report_path = os.path.join(
            os.path.dirname(os.path.abspath(__file__)),
            "gl_orchestrator_test_results.json"
        )
        with open(report_path, 'w') as f:
            json.dump({
                'timestamp': asyncio.get_event_loop().time(),
                'results': self.results,
                'summary': {
                    'total_tests': total_tests,
                    'total_passed': total_passed,
                    'pass_rate': overall_rate,
                    'by_category': categories
                }
            }, f, indent=2)
        print(f"\nDetailed results saved to: {report_path}")


async def main():
    """Main test runner"""
    tester = GLOrchestratorTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    # Run the test suite
    asyncio.run(main())
