"""
Parallel Tool Execution Service

Enables efficient parallel execution of multiple MCP tools with dependency analysis,
partial failure handling, and intelligent result aggregation.
"""
import asyncio
import logging
from typing import Dict, Any, List, Optional, Set, Tuple, Union
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
import json

logger = logging.getLogger(__name__)


class ExecutionStatus(Enum):
    """Status of tool execution"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class ToolCall:
    """Represents a single tool call"""
    id: str
    tool_name: str
    parameters: Dict[str, Any]
    dependencies: Set[str] = field(default_factory=set)
    status: ExecutionStatus = ExecutionStatus.PENDING
    result: Optional[Any] = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    
    @property
    def execution_time(self) -> Optional[float]:
        """Get execution time in seconds"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None


@dataclass
class ExecutionBatch:
    """A batch of tool calls that can be executed in parallel"""
    batch_id: int
    tool_calls: List[ToolCall]
    
    def can_add(self, tool_call: ToolCall, completed_ids: Set[str]) -> bool:
        """Check if a tool call can be added to this batch"""
        # Tool can be added if all its dependencies are completed
        return tool_call.dependencies.issubset(completed_ids)


class ToolDependencyAnalyzer:
    """Analyzes dependencies between tool calls to enable parallel execution"""
    
    def __init__(self):
        self.dependency_patterns = {
            # Data dependencies - these tools must run before others that use their data
            'data_providers': [
                'search_across_modules',
                'get_financial_summary',
                'list_enabled_modules'
            ],
            
            # These tools depend on data from providers
            'data_consumers': [
                'generate_consolidated_report',
                'execute_month_end_close'
            ],
            
            # Independent tools that can run in parallel
            'independent': [
                'health_check',
                'list_enabled_modules'
            ]
        }
    
    def analyze_dependencies(self, tool_calls: List[Dict[str, Any]]) -> List[ToolCall]:
        """
        Analyze tool calls and create ToolCall objects with dependencies
        
        Args:
            tool_calls: List of tool call specifications
            
        Returns:
            List of ToolCall objects with dependencies set
        """
        # Create ToolCall objects
        calls = []
        for i, call_spec in enumerate(tool_calls):
            tool_call = ToolCall(
                id=call_spec.get('id', f"call_{i}"),
                tool_name=call_spec['tool_name'],
                parameters=call_spec.get('parameters', {})
            )
            calls.append(tool_call)
        
        # Analyze dependencies based on tool types and parameters
        for i, call in enumerate(calls):
            # Check if this tool depends on data from previous tools
            if call.tool_name in self.dependency_patterns['data_consumers']:
                # Find potential data providers that should run first
                for j, other_call in enumerate(calls[:i]):
                    if other_call.tool_name in self.dependency_patterns['data_providers']:
                        # Check if they operate on same data (e.g., same module)
                        if self._shares_context(call, other_call):
                            call.dependencies.add(other_call.id)
            
            # Check for explicit parameter dependencies
            for param_value in call.parameters.values():
                if isinstance(param_value, str) and param_value.startswith("{{") and param_value.endswith("}}"):
                    # This is a reference to another tool's output
                    ref_id = param_value[2:-2].split('.')[0]
                    call.dependencies.add(ref_id)
        
        return calls
    
    def _shares_context(self, call1: ToolCall, call2: ToolCall) -> bool:
        """Check if two tool calls share context (e.g., same module)"""
        # Check for common module parameters
        modules1 = call1.parameters.get('modules', [])
        modules2 = call2.parameters.get('modules', [])
        
        if modules1 and modules2:
            return bool(set(modules1) & set(modules2))
        
        # Check for date range overlap
        if 'start_date' in call1.parameters and 'start_date' in call2.parameters:
            return True
        
        return False
    
    def create_execution_batches(self, tool_calls: List[ToolCall]) -> List[ExecutionBatch]:
        """
        Group tool calls into batches that can be executed in parallel
        
        Args:
            tool_calls: List of ToolCall objects with dependencies
            
        Returns:
            List of ExecutionBatch objects
        """
        batches = []
        completed_ids = set()
        remaining_calls = tool_calls.copy()
        batch_id = 0
        
        while remaining_calls:
            # Find all tools that can be executed now
            ready_calls = []
            for call in remaining_calls[:]:
                if call.dependencies.issubset(completed_ids):
                    ready_calls.append(call)
                    remaining_calls.remove(call)
            
            if not ready_calls:
                # Circular dependency or error
                logger.error(f"Unable to schedule remaining tools: {[c.id for c in remaining_calls]}")
                break
            
            # Create batch
            batch = ExecutionBatch(batch_id=batch_id, tool_calls=ready_calls)
            batches.append(batch)
            
            # Mark these as completed for dependency purposes
            completed_ids.update(call.id for call in ready_calls)
            batch_id += 1
        
        return batches


class ParallelToolExecutor:
    """Executes multiple tools in parallel with smart error handling and result aggregation"""
    
    def __init__(self, tool_executor_func):
        """
        Initialize the parallel executor
        
        Args:
            tool_executor_func: Async function that executes a single tool
                               Should accept (tool_name, parameters) and return result
        """
        self.tool_executor = tool_executor_func
        self.analyzer = ToolDependencyAnalyzer()
        self.max_concurrent = 5  # Limit concurrent executions
    
    async def execute_parallel(self, tool_calls: List[Dict[str, Any]], 
                             allow_partial_failure: bool = True) -> Dict[str, Any]:
        """
        Execute multiple tools in parallel when possible
        
        Args:
            tool_calls: List of tool call specifications
            allow_partial_failure: Whether to continue if some tools fail
            
        Returns:
            Dict with results, errors, and execution metadata
        """
        start_time = datetime.now(timezone.utc)
        
        # Analyze dependencies
        analyzed_calls = self.analyzer.analyze_dependencies(tool_calls)
        
        # Create execution batches
        batches = self.analyzer.create_execution_batches(analyzed_calls)
        
        logger.info(f"Executing {len(analyzed_calls)} tools in {len(batches)} batches")
        
        # Execute batches
        all_results = {}
        all_errors = {}
        
        for batch in batches:
            batch_results = await self._execute_batch(batch, all_results)
            
            # Check for failures
            failed_calls = [c for c in batch.tool_calls if c.status == ExecutionStatus.FAILED]
            if failed_calls and not allow_partial_failure:
                # Abort execution
                for call in failed_calls:
                    all_errors[call.id] = call.error
                break
            
            # Store results and errors
            for call in batch.tool_calls:
                if call.status == ExecutionStatus.SUCCESS:
                    all_results[call.id] = call.result
                elif call.status == ExecutionStatus.FAILED:
                    all_errors[call.id] = call.error
        
        # Aggregate results
        aggregated_result = self._aggregate_results(all_results, analyzed_calls)
        
        end_time = datetime.now(timezone.utc)
        execution_time = (end_time - start_time).total_seconds()
        
        return {
            "success": len(all_errors) == 0,
            "results": all_results,
            "aggregated": aggregated_result,
            "errors": all_errors,
            "metadata": {
                "total_tools": len(analyzed_calls),
                "successful": len(all_results),
                "failed": len(all_errors),
                "batches": len(batches),
                "execution_time": execution_time,
                "timestamp": end_time.isoformat()
            }
        }
    
    async def _execute_batch(self, batch: ExecutionBatch, 
                           previous_results: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a batch of tool calls in parallel"""
        logger.info(f"Executing batch {batch.batch_id} with {len(batch.tool_calls)} tools")
        
        # Create tasks for parallel execution
        tasks = []
        for call in batch.tool_calls:
            # Substitute any parameter references with actual values
            resolved_params = self._resolve_parameters(call.parameters, previous_results)
            task = self._execute_tool_with_retry(call, resolved_params)
            tasks.append(task)
        
        # Execute with concurrency limit
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def bounded_task(task):
            async with semaphore:
                return await task
        
        bounded_tasks = [bounded_task(task) for task in tasks]
        await asyncio.gather(*bounded_tasks, return_exceptions=True)
        
        return previous_results
    
    async def _execute_tool_with_retry(self, tool_call: ToolCall, 
                                     parameters: Dict[str, Any],
                                     max_retries: int = 2) -> None:
        """Execute a single tool with retry logic"""
        tool_call.status = ExecutionStatus.RUNNING
        tool_call.start_time = datetime.now(timezone.utc)
        
        for attempt in range(max_retries + 1):
            try:
                result = await self.tool_executor(tool_call.tool_name, parameters)
                tool_call.result = result
                tool_call.status = ExecutionStatus.SUCCESS
                tool_call.end_time = datetime.now(timezone.utc)
                logger.info(f"Tool {tool_call.id} completed successfully in {tool_call.execution_time:.2f}s")
                return
                
            except Exception as e:
                logger.warning(f"Tool {tool_call.id} attempt {attempt + 1} failed: {str(e)}")
                if attempt < max_retries:
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                else:
                    tool_call.error = str(e)
                    tool_call.status = ExecutionStatus.FAILED
                    tool_call.end_time = datetime.now(timezone.utc)
                    logger.error(f"Tool {tool_call.id} failed after {max_retries + 1} attempts")
    
    def _resolve_parameters(self, parameters: Dict[str, Any], 
                          results: Dict[str, Any]) -> Dict[str, Any]:
        """Resolve parameter references to actual values from previous results"""
        resolved = {}
        
        for key, value in parameters.items():
            if isinstance(value, str) and value.startswith("{{") and value.endswith("}}"):
                # This is a reference like {{call_1.data}}
                ref = value[2:-2]
                parts = ref.split('.')
                
                if len(parts) == 2:
                    call_id, field = parts
                    if call_id in results:
                        result = results[call_id]
                        resolved[key] = result.get(field) if isinstance(result, dict) else result
                    else:
                        resolved[key] = None
                else:
                    resolved[key] = results.get(ref)
            else:
                resolved[key] = value
        
        return resolved
    
    def _aggregate_results(self, results: Dict[str, Any], 
                         tool_calls: List[ToolCall]) -> Dict[str, Any]:
        """
        Intelligently aggregate results based on tool output types
        
        Args:
            results: Dict of tool results by ID
            tool_calls: List of all tool calls for context
            
        Returns:
            Aggregated result structure
        """
        if not results:
            return {}
        
        # Group results by tool type
        by_tool_type = {}
        for call in tool_calls:
            if call.id in results:
                tool_type = call.tool_name.split('__')[-1]  # Get last part of tool name
                if tool_type not in by_tool_type:
                    by_tool_type[tool_type] = []
                by_tool_type[tool_type].append(results[call.id])
        
        # Aggregate based on result types
        aggregated = {}
        
        for tool_type, tool_results in by_tool_type.items():
            if len(tool_results) == 1:
                aggregated[tool_type] = tool_results[0]
            else:
                # Multiple results of same type - merge intelligently
                if all(isinstance(r, dict) for r in tool_results):
                    # Merge dictionaries
                    merged = {}
                    for result in tool_results:
                        for key, value in result.items():
                            if key not in merged:
                                merged[key] = value
                            elif isinstance(value, list) and isinstance(merged[key], list):
                                merged[key].extend(value)
                            elif isinstance(value, (int, float)) and isinstance(merged[key], (int, float)):
                                merged[key] += value
                    aggregated[tool_type] = merged
                elif all(isinstance(r, list) for r in tool_results):
                    # Concatenate lists
                    aggregated[tool_type] = sum(tool_results, [])
                else:
                    # Keep as array
                    aggregated[tool_type] = tool_results
        
        return aggregated