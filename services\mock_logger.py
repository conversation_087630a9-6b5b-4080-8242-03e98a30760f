"""
Mock logger for when structlog is not available.

This provides a minimal logging interface to allow the system to run
even when structlog is not installed.
"""

import logging


class MockStructlogLogger:
    """Mock structlog logger that falls back to standard logging."""
    
    def __init__(self, name=None):
        self.logger = logging.getLogger(name or __name__)
        
    def info(self, msg, **kwargs):
        """Log info message."""
        extra_info = " ".join(f"{k}={v}" for k, v in kwargs.items())
        if extra_info:
            self.logger.info(f"{msg} [{extra_info}]")
        else:
            self.logger.info(msg)
            
    def warning(self, msg, **kwargs):
        """Log warning message."""
        extra_info = " ".join(f"{k}={v}" for k, v in kwargs.items())
        if extra_info:
            self.logger.warning(f"{msg} [{extra_info}]")
        else:
            self.logger.warning(msg)
            
    def error(self, msg, **kwargs):
        """Log error message."""
        extra_info = " ".join(f"{k}={v}" for k, v in kwargs.items())
        if extra_info:
            self.logger.error(f"{msg} [{extra_info}]")
        else:
            self.logger.error(msg)
            
    def debug(self, msg, **kwargs):
        """Log debug message."""
        extra_info = " ".join(f"{k}={v}" for k, v in kwargs.items())
        if extra_info:
            self.logger.debug(f"{msg} [{extra_info}]")
        else:
            self.logger.debug(msg)


class MockStructlog:
    """Mock structlog module."""
    
    @staticmethod
    def get_logger(name=None):
        """Get a mock logger."""
        return MockStructlogLogger(name)


# Try to import real structlog, fall back to mock
try:
    import structlog
except ImportError:
    logging.basicConfig(level=logging.INFO)
    structlog = MockStructlog()