"""
Test Response Processing (Phase 5)

Tests the response processing and LLM integration functionality
"""
import asyncio
import pytest
from unittest.mock import Mock, AsyncMock, patch
import json

from services.response_processor import ResponseProcessor
from services.llm_service import LLMService
from agents.orchestrator import DynamicOrchestrator


class TestResponseProcessing:
    """Test response processing functionality"""
    
    @pytest.mark.asyncio
    async def test_response_processor_data_response(self):
        """Test processing data query responses"""
        processor = ResponseProcessor()
        
        # Mock tool results
        tool_results = [
            {
                "tool": "mcp__sage-intacct__get_financial_summary",
                "success": True,
                "result": {
                    "content": {
                        "total_revenue": 1500000,
                        "total_expenses": 1200000,
                        "net_income": 300000,
                        "period": "2024-01"
                    }
                }
            }
        ]
        
        # Process results (without actual LLM call)
        with patch.object(processor, '_call_llm', return_value="Financial Summary: Revenue $1.5M, Expenses $1.2M, Net Income $300K"):
            result = await processor.process_tool_results(
                user_request="What's my YTD revenue?",
                tool_results=tool_results
            )
        
        assert result["type"] == "data"
        assert "Revenue" in result["response"]
        assert len(result["tools_used"]) == 1
    
    @pytest.mark.asyncio
    async def test_response_processor_error_handling(self):
        """Test error response handling"""
        processor = ResponseProcessor()
        
        # Mock failed tool results
        tool_results = [
            {
                "tool": "mcp__sage-intacct__health_check",
                "success": False,
                "error": "Connection timeout"
            }
        ]
        
        result = await processor.process_tool_results(
            user_request="Check system health",
            tool_results=tool_results
        )
        
        assert result["type"] == "error"
        assert "Connection timeout" in result["response"]
    
    @pytest.mark.asyncio
    async def test_llm_service_analyze_request(self):
        """Test LLM service request analysis"""
        llm_service = LLMService()
        
        # Mock available tools
        mock_tools = {
            "financial_summary": Mock(
                name="get_financial_summary",
                server_name="sage-intacct",
                description="Get financial summary data",
                parameters={"properties": {"include_modules": {"type": "array"}}}
            )
        }
        
        # Test with fallback (no API key)
        with patch.object(llm_service, 'api_key', None):
            analysis = await llm_service.analyze_request(
                user_request="Show me YTD sales",
                available_tools=mock_tools
            )
        
        assert "analysis" in analysis
        assert "tool_calls" in analysis
        assert analysis["fallback"] == True
    
    @pytest.mark.asyncio
    async def test_llm_service_pattern_matching(self):
        """Test fallback pattern matching"""
        llm_service = LLMService()
        
        # Mock tools
        mock_tools = {
            "health": Mock(
                name="health_check",
                server_name="sage-intacct",
                description="Check health"
            ),
            "search": Mock(
                name="search_across_modules",
                server_name="sage-intacct",
                description="Search data"
            ),
            "report": Mock(
                name="generate_consolidated_report",
                server_name="sage-intacct",
                description="Generate reports"
            )
        }
        
        # Test health check pattern
        analysis = llm_service._generate_fallback_analysis(
            "Check system health status",
            mock_tools
        )
        
        assert len(analysis["tool_calls"]) > 0
        assert "health_check" in analysis["tool_calls"][0]["tool"]
    
    @pytest.mark.asyncio
    async def test_orchestrator_with_response_processing(self):
        """Test orchestrator integration with response processing"""
        orchestrator = DynamicOrchestrator()
        
        # Mock the registry and tools
        mock_tool = Mock(
            name="test_tool",
            server_name="test-server",
            description="Test tool",
            parameters={}
        )
        
        orchestrator._available_tools = {"test_tool": mock_tool}
        orchestrator._initialized = True
        
        # Mock tool execution
        with patch.object(orchestrator, '_execute_tool_directly', return_value={"result": "test"}):
            with patch.object(orchestrator.llm_service, 'analyze_request', return_value={
                "analysis": "Test analysis",
                "tool_calls": [{
                    "tool": "mcp__test-server__test_tool",
                    "parameters": {}
                }]
            }):
                with patch.object(orchestrator.response_processor, 'process_tool_results', return_value={
                    "response": "Test completed successfully",
                    "type": "data"
                }):
                    result = await orchestrator._process_with_llm("Test request", {})
        
        assert "Test completed" in result["response"]
        assert len(result["tools_executed"]) == 1
        assert "analysis" in result["execution_details"]
    
    @pytest.mark.asyncio
    async def test_workflow_response_formatting(self):
        """Test workflow response formatting"""
        processor = ResponseProcessor()
        
        workflow_result = {
            "status": "completed",
            "completed_steps": ["validate_data", "generate_report", "send_notification"],
            "failed_steps": [],
            "final_results": {
                "report_id": "RPT-123",
                "status": "success"
            }
        }
        
        with patch.object(processor, '_call_llm', return_value="Workflow completed successfully with 3 steps"):
            response = await processor.format_workflow_response(
                workflow_name="financial_reporting",
                workflow_result=workflow_result
            )
        
        assert "completed" in response.lower()
    
    @pytest.mark.asyncio
    async def test_parallel_response_formatting(self):
        """Test parallel execution response formatting"""
        processor = ResponseProcessor()
        
        parallel_result = {
            "success": True,
            "results": {
                "call_1": {"data": "result1"},
                "call_2": {"data": "result2"}
            },
            "errors": {},
            "aggregated": {
                "total_operations": 2,
                "success_rate": 1.0
            },
            "metadata": {
                "total_time": 1.5,
                "parallel_groups": 1
            }
        }
        
        with patch.object(processor, '_call_llm', return_value="Parallel execution completed: 2 operations successful"):
            response = await processor.format_parallel_response(parallel_result)
        
        assert "2 operations" in response


def test_integration():
    """Run integration test"""
    async def run_test():
        print("\n=== Testing Response Processing (Phase 5) ===\n")
        
        # Initialize services
        processor = ResponseProcessor()
        llm_service = LLMService()
        
        # Test 1: Response formatting
        print("1. Testing response formatting...")
        tool_results = [
            {
                "tool": "mcp__sage-intacct__list_enabled_modules",
                "success": True,
                "result": {
                    "content": ["GL", "AR", "AP", "CM"]
                }
            }
        ]
        
        # Format without LLM (fallback mode)
        formatted = processor._format_tool_results(tool_results)
        assert len(formatted) == 1
        assert formatted[0]["success"] == True
        print("✓ Response formatting works")
        
        # Test 2: Pattern matching
        print("\n2. Testing pattern matching...")
        mock_tools = {
            "summary": Mock(
                name="get_financial_summary",
                server_name="sage-intacct"
            )
        }
        
        analysis = llm_service._generate_fallback_analysis(
            "What are my sales this year?",
            mock_tools
        )
        
        assert len(analysis["tool_calls"]) > 0
        print("✓ Pattern matching works")
        
        # Test 3: Tool validation
        print("\n3. Testing tool validation...")
        tool_calls = [
            {
                "tool": "mcp__sage-intacct__health_check",
                "parameters": {"invalid": None}
            }
        ]
        
        available = {
            "health": Mock(
                name="health_check",
                server_name="sage-intacct"
            )
        }
        
        validated = llm_service._validate_tool_calls(tool_calls, available)
        assert len(validated) == 1
        assert "invalid" not in validated[0]["parameters"]
        print("✓ Tool validation works")
        
        print("\n✅ All Phase 5 tests passed!")
    
    asyncio.run(run_test())


if __name__ == "__main__":
    test_integration()