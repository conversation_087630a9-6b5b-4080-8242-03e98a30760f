"""
Query Enhancer for Intelligent Tool Filtering System

This module provides query enhancement capabilities to improve tool search relevance
by leveraging embeddings and learned patterns rather than hardcoded rules.
"""

import logging
from typing import List, Dict, Any, Optional, Set
from datetime import datetime, timezone
import numpy as np

logger = logging.getLogger(__name__)


class QueryEnhancer:
    """
    Enhances user queries with context for better tool matching.
    
    This class uses embeddings and semantic understanding to:
    - Find similar successful queries from history
    - Extract key concepts from conversation context
    - Identify patterns from past tool usage
    - Expand queries based on learned associations
    """
    
    def __init__(self, vector_store, embedding_service):
        """
        Initialize the QueryEnhancer.
        
        Args:
            vector_store: Vector store for finding similar patterns
            embedding_service: Service for generating embeddings
        """
        self.vector_store = vector_store
        self.embedding_service = embedding_service
        self.logger = logger
    
    async def enhance_query(self,
                          query: str,
                          conversation_context: Optional[List[Dict[str, Any]]] = None,
                          user_preferences: Optional[Dict[str, Any]] = None) -> str:
        """
        Enhance a query with contextual information using embeddings.
        
        Args:
            query: The raw user query
            conversation_context: Recent conversation history
            user_preferences: User's historical preferences and patterns
            
        Returns:
            Enhanced query string with additional context
        """
        enhanced_parts = [query]
        
        # Find similar successful queries from history
        similar_queries = await self._find_similar_queries(query)
        if similar_queries:
            # Add concepts from similar successful queries
            concepts = self._extract_concepts_from_similar(similar_queries)
            if concepts:
                enhanced_parts.append(f"Related concepts: {', '.join(concepts[:5])}")
        
        # Extract key terms from conversation context using embeddings
        if conversation_context:
            context_terms = await self._extract_context_terms(conversation_context)
            if context_terms:
                enhanced_parts.append(f"Context: {', '.join(context_terms[:7])}")
        
        # Add user's tool usage patterns
        if user_preferences:
            pattern_text = await self._format_usage_patterns(user_preferences)
            if pattern_text:
                enhanced_parts.append(pattern_text)
        
        # Find semantically related expansions
        expansions = await self._semantic_query_expansion(query)
        if expansions:
            enhanced_parts.append(f"Expanded: {', '.join(expansions[:5])}")
        
        enhanced_query = " | ".join(enhanced_parts)
        
        self.logger.debug(f"Enhanced query: '{query}' -> '{enhanced_query}'")
        
        return enhanced_query
    
    async def _find_similar_queries(self, query: str) -> List[Dict[str, Any]]:
        """
        Find similar queries from successful tool executions.
        
        Args:
            query: The current query
            
        Returns:
            List of similar query patterns with their metadata
        """
        # Generate embedding for the query
        query_embedding = await self.embedding_service.encode(query)
        
        # Search for similar patterns in our pattern store
        # These would be stored from successful tool executions
        similar = await self.vector_store.search(
            query_embedding=query_embedding,
            k=10,
            threshold=0.7,
            filters={"type": "query_pattern", "success": True}
        )
        
        return similar
    
    def _extract_concepts_from_similar(self, similar_queries: List[Dict[str, Any]]) -> List[str]:
        """
        Extract key concepts from similar successful queries.
        
        Args:
            similar_queries: List of similar query results
            
        Returns:
            List of extracted concepts
        """
        concepts = set()
        
        for result in similar_queries:
            # Handle tuple format (tool_id, similarity, metadata) from vector search
            if isinstance(result, tuple) and len(result) >= 3:
                _, _, metadata = result
            else:
                # Handle dict format
                metadata = result.get("metadata", {}) if isinstance(result, dict) else {}
            
            # Add the tool that was successfully used
            if metadata.get("selected_tool"):
                concepts.add(metadata["selected_tool"])
            
            # Add any learned associations
            if metadata.get("concepts"):
                concepts.update(metadata["concepts"])
            
            # Add domain if identified
            if metadata.get("domain"):
                concepts.add(metadata["domain"])
        
        # Return unique concepts, ordered by frequency
        return list(concepts)[:10]
    
    async def _extract_context_terms(self, conversation_context: List[Dict[str, Any]]) -> List[str]:
        """
        Extract important terms from conversation context using embeddings.
        
        Args:
            conversation_context: Recent conversation history
            
        Returns:
            List of important context terms
        """
        if not conversation_context:
            return []
        
        # Combine recent messages
        recent_messages = []
        for msg in conversation_context[-3:]:  # Last 3 messages
            if msg.get("role") == "user" and msg.get("content"):
                recent_messages.append(msg["content"])
        
        if not recent_messages:
            return []
        
        # Generate embedding for the combined context
        context_text = " ".join(recent_messages)
        context_embedding = await self.embedding_service.encode(context_text)
        
        # Find tools that are semantically similar to the context
        context_tools = await self.vector_store.search(
            query_embedding=context_embedding,
            k=5,
            threshold=0.6
        )
        
        # Extract key terms from the matching tools
        terms = set()
        for tool in context_tools:
            # Handle tuple format (tool_id, similarity, metadata) from vector search
            if isinstance(tool, tuple) and len(tool) >= 3:
                _, _, metadata = tool
            else:
                # Handle dict format
                metadata = tool.get("metadata", {}) if isinstance(tool, dict) else {}
            
            if metadata.get("category"):
                terms.add(metadata["category"])
            # Add key terms from tool description
            if metadata.get("key_terms"):
                terms.update(metadata["key_terms"][:3])
        
        return list(terms)
    
    async def _format_usage_patterns(self, user_preferences: Dict[str, Any]) -> str:
        """
        Format user's usage patterns based on their history.
        
        Args:
            user_preferences: User preference data
            
        Returns:
            Formatted pattern string
        """
        parts = []
        
        # Get frequently used tools with high success rates
        if user_preferences.get("tool_usage_stats"):
            top_tools = sorted(
                user_preferences["tool_usage_stats"].items(),
                key=lambda x: x[1].get("success_rate", 0) * x[1].get("count", 0),
                reverse=True
            )[:5]
            
            if top_tools:
                tool_names = [tool[0] for tool in top_tools]
                parts.append(f"Frequently uses: {', '.join(tool_names)}")
        
        # Add temporal patterns (what tools are used at what times)
        if user_preferences.get("temporal_patterns"):
            current_hour = datetime.now(timezone.utc).hour
            time_period = "morning" if current_hour < 12 else "afternoon" if current_hour < 17 else "evening"
            
            if time_period in user_preferences["temporal_patterns"]:
                common_tools = user_preferences["temporal_patterns"][time_period][:3]
                parts.append(f"At this time often uses: {', '.join(common_tools)}")
        
        return " | ".join(parts) if parts else ""
    
    async def _semantic_query_expansion(self, query: str) -> List[str]:
        """
        Expand query using semantic similarity rather than hardcoded synonyms.
        
        Args:
            query: The query to expand
            
        Returns:
            List of semantically related terms
        """
        # Generate embedding for the query
        query_embedding = await self.embedding_service.encode(query)
        
        # Find semantically similar tools
        similar_tools = await self.vector_store.search(
            query_embedding=query_embedding,
            k=20,
            threshold=0.5  # Lower threshold to get more related concepts
        )
        
        # Extract unique terms from similar tools
        expansion_terms = set()
        
        for tool in similar_tools:
            # Handle tuple format (tool_id, similarity, metadata) from vector search
            if isinstance(tool, tuple) and len(tool) >= 3:
                _, _, metadata = tool
            else:
                # Handle dict format
                metadata = tool.get("metadata", {}) if isinstance(tool, dict) else {}
            
            # Add categories
            if metadata.get("category"):
                expansion_terms.add(metadata["category"])
            
            # Add key action verbs from tool names
            tool_name = metadata.get("tool_name", "")
            if "_" in tool_name:
                # Extract verb from tool names like "create_invoice"
                parts = tool_name.split("_")
                if parts[0]:
                    expansion_terms.add(parts[0])
            
            # Add any semantic tags
            if metadata.get("semantic_tags"):
                expansion_terms.update(metadata["semantic_tags"][:2])
        
        # Remove the original query terms to avoid redundancy
        query_words = set(query.lower().split())
        expansion_terms = {term for term in expansion_terms if term.lower() not in query_words}
        
        return list(expansion_terms)[:10]
    
    async def learn_from_selection(self,
                                 query: str,
                                 enhanced_query: str,
                                 selected_tool: str,
                                 success: bool):
        """
        Learn from tool selection to improve future enhancements.
        
        Args:
            query: Original query
            enhanced_query: Enhanced version that was used
            selected_tool: Tool that was selected
            success: Whether the tool execution was successful
        """
        if not success:
            return
        
        # Create a pattern embedding that captures this successful interaction
        pattern_text = f"{query} -> {selected_tool}"
        pattern_embedding = await self.embedding_service.encode(pattern_text)
        
        # Extract concepts from the interaction
        concepts = []
        
        # Get tool metadata
        tool_search = await self.vector_store.search(
            query_embedding=pattern_embedding,
            k=1,
            filter={"tool_name": selected_tool}
        )
        
        if tool_search:
            # Handle tuple format (tool_id, similarity, metadata) from vector search
            first_result = tool_search[0]
            if isinstance(first_result, tuple) and len(first_result) >= 3:
                _, _, tool_metadata = first_result
            else:
                # Handle dict format
                tool_metadata = first_result.get("metadata", {}) if isinstance(first_result, dict) else {}
            
            if tool_metadata.get("category"):
                concepts.append(tool_metadata["category"])
            if tool_metadata.get("domain"):
                concepts.append(tool_metadata["domain"])
        
        # Store this successful pattern
        await self.vector_store.upsert(
            id=f"pattern_{hash(pattern_text)}",
            embedding=pattern_embedding,
            metadata={
                "type": "query_pattern",
                "query": query,
                "enhanced_query": enhanced_query,
                "selected_tool": selected_tool,
                "success": True,
                "concepts": concepts,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )
        
        self.logger.info(f"Learned pattern: '{query}' -> '{selected_tool}'")
    
    async def get_query_intent_embedding(self, query: str) -> np.ndarray:
        """
        Get an embedding that represents the intent of the query.
        
        This is used by other components to understand query similarity.
        
        Args:
            query: The query to analyze
            
        Returns:
            Intent embedding vector
        """
        # For intent, we might want to emphasize action words
        # Extract potential action words (first word often indicates intent)
        words = query.split()
        if words:
            # Give more weight to the first word (likely the action)
            intent_text = f"{words[0]} {query}"
        else:
            intent_text = query
        
        return await self.embedding_service.encode(intent_text)