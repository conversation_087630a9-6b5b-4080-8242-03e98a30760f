# Fast-Agent Configuration File
# Dynamic Orchestration Configuration
# 
# This configuration focuses on the orchestrator agent which dynamically
# routes requests to appropriate MCP tools based on intent detection.
# Additional MCP servers can be added at runtime via the API.

# MCP Server Configurations
mcp:
  servers:
    # Sage Intacct MCP Server - Primary business system
    # Connects to independently running SSE server via Supergateway
    sage-intacct:
      transport: sse
      url: "http://localhost:8001/sse"
      connection_type: sse
      endpoint: "http://localhost:8001"
      headers: {}
      health_check:
        enabled: true
        interval: 30
        timeout: 10
      # Runtime metadata (used by dynamic config)
      tags: ["intacct", "financial", "erp"]
      metadata:
        modules: ["GL", "AR", "AP", "Core"]
        capabilities: ["read", "write", "report", "validate"]

    "Sage Business Cloud Accounting":
      transport: sse
      # url: "https://sbca-mcp.provenlabs.xyz/sse"
      connection_type: sse
      endpoint: "https://sbca-mcp.provenlabs.xyz/sse"
      headers: {}
      health_check:
        enabled: true
        interval: 30
        timeout: 10
      # Runtime metadata (used by dynamic config)
      tags: ["sbca", "accounting", "cloud", "saas"]
      metadata:
        modules: ["Sales", "Inventory", "Accounting", "Reporting"]
        capabilities: ["read", "write", "report", "export"]
    
    # Example: Sage Business Cloud Accounting (can be removed if not needed)
    # "Sage Business Cloud Accounting":
    #   command: npx
    #   args: ["supergateway", "https://sbca-mcp.provenlabs.xyz/sse"]
    #   env: {}
    #   health_check:
    #     enabled: true
    #     interval: 30
    #     timeout: 10

# Single Orchestrator Agent Configuration
agents:
  # The orchestrator dynamically routes all requests to appropriate MCP tools
  orchestrator:
    name: "Dynamic Orchestration Agent"
    description: "Intelligently routes requests to MCP tools based on intent"
    mcp_servers:
      - sage-intacct
      - "Sage Business Cloud Accounting"
      # Additional servers are discovered dynamically
    timeout: 90
    # Core orchestration capabilities
    capabilities:
      - "Intent detection and routing"
      - "Dynamic tool discovery"
      - "Multi-tool workflows"
      - "Confidence-based clarification"
      - "GL operations (via MCP tools)"
      - "AR operations (via MCP tools)"
      - "AP operations (via MCP tools)"
      - "Financial analysis (via MCP tools)"
      - "Report generation (via MCP tools)"
      - "Data validation (via MCP tools)"

# Service Configuration
service:
  host: "0.0.0.0"
  port: 8000
  debug: ${DEBUG:false}
  log_level: ${LOG_LEVEL:info}
  
# Development Settings
development:
  auto_reload: true
  verbose_logging: true
  
# Production Settings  
production:
  auto_reload: false
  verbose_logging: false
  workers: 4