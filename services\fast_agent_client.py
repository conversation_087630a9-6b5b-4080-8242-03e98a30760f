"""
FastAgent Client - Frontend Integration Layer

This module provides the client-side interface for communicating with the 
FastAPI agent service. It replaces direct agent imports in the frontend
with API calls to the decoupled agent service.
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, AsyncIterator, Dict, List, Optional, Union
from urllib.parse import urljoin

import aiohttp
import websockets
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class AgentMessage(BaseModel):
    """Message to send to an agent"""
    message: str
    context: Optional[Dict[str, Any]] = Field(default_factory=dict)
    correlation_id: Optional[str] = None


class AgentResponse(BaseModel):
    """Response from an agent"""
    response: Any
    agent_id: str
    timestamp: str
    status: str
    correlation_id: Optional[str] = None
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)


class AgentInfo(BaseModel):
    """Agent information"""
    id: str
    name: str
    status: str
    started_at: Optional[str] = None
    last_activity: Optional[str] = None
    error_count: int = 0
    total_requests: int = 0
    avg_response_time: float = 0.0
    capabilities: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class FastAgentClient:
    """
    Client for interacting with the FastAPI agent service.
    
    This client provides both REST and WebSocket interfaces for communicating
    with agents running in the decoupled service.
    """
    
    def __init__(
        self,
        base_url: str = "http://localhost:8000",
        ws_url: str = "ws://localhost:8000",
        timeout: float = 30.0,
        max_retries: int = 3
    ):
        """
        Initialize the FastAgent client.
        
        Args:
            base_url: Base URL for REST API calls
            ws_url: Base URL for WebSocket connections
            timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts
        """
        self.base_url = base_url.rstrip('/')
        self.ws_url = ws_url.rstrip('/')
        self.timeout = timeout
        self.max_retries = max_retries
        self._session: Optional[aiohttp.ClientSession] = None
        self._ws_connection: Optional[websockets.WebSocketClientProtocol] = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.disconnect()
    
    async def connect(self):
        """Initialize HTTP session"""
        if not self._session:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self._session = aiohttp.ClientSession(timeout=timeout)
    
    async def disconnect(self):
        """Clean up connections"""
        if self._session:
            await self._session.close()
            self._session = None
        if self._ws_connection:
            await self._ws_connection.close()
            self._ws_connection = None
    
    # REST API Methods
    
    async def health_check(self) -> Dict[str, Any]:
        """Check service health"""
        url = urljoin(self.base_url, "/health")
        async with self._session.get(url) as response:
            response.raise_for_status()
            return await response.json()
    
    async def list_agents(self) -> List[AgentInfo]:
        """List all available agents"""
        url = urljoin(self.base_url, "/agents")
        async with self._session.get(url) as response:
            response.raise_for_status()
            data = await response.json()
            return [AgentInfo(**agent) for agent in data["agents"]]
    
    async def get_agent_status(self, agent_id: str) -> AgentInfo:
        """Get status of a specific agent"""
        url = urljoin(self.base_url, f"/agents/{agent_id}/status")
        async with self._session.get(url) as response:
            response.raise_for_status()
            return AgentInfo(**await response.json())
    
    async def reset_agent(self, agent_id: str) -> Dict[str, str]:
        """Reset an agent's state"""
        url = urljoin(self.base_url, f"/agents/{agent_id}/reset")
        async with self._session.post(url) as response:
            response.raise_for_status()
            return await response.json()
    
    async def get_agent_history(
        self, agent_id: str, limit: int = 50
    ) -> Dict[str, Any]:
        """Get conversation history for an agent"""
        url = urljoin(self.base_url, f"/agents/{agent_id}/history")
        params = {"limit": limit}
        async with self._session.get(url, params=params) as response:
            response.raise_for_status()
            return await response.json()
    
    async def send_message(
        self,
        agent_id: str,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None
    ) -> AgentResponse:
        """
        Send a message to an agent via REST API.
        
        Args:
            agent_id: ID of the agent to send to
            message: Message content
            context: Optional context data
            correlation_id: Optional correlation ID for tracking
            
        Returns:
            Agent response
        """
        url = urljoin(self.base_url, f"/agents/{agent_id}/chat")
        
        payload = AgentMessage(
            message=message,
            context=context or {},
            correlation_id=correlation_id
        )
        
        # Retry logic with exponential backoff
        for attempt in range(self.max_retries):
            try:
                async with self._session.post(
                    url, json=payload.model_dump()
                ) as response:
                    response.raise_for_status()
                    return AgentResponse(**await response.json())
            except aiohttp.ClientError as e:
                if attempt < self.max_retries - 1:
                    wait_time = 2 ** attempt  # Exponential backoff
                    logger.warning(
                        f"Request failed (attempt {attempt + 1}), "
                        f"retrying in {wait_time}s: {e}"
                    )
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"All retry attempts failed: {e}")
                    raise
    
    # WebSocket Methods
    
    async def connect_websocket(self) -> None:
        """Establish WebSocket connection"""
        if self._ws_connection and not self._ws_connection.closed:
            return  # Already connected
        
        ws_endpoint = urljoin(self.ws_url, "/agents/ws")
        self._ws_connection = await websockets.connect(ws_endpoint)
        logger.info("WebSocket connection established")
    
    async def disconnect_websocket(self) -> None:
        """Close WebSocket connection"""
        if self._ws_connection and not self._ws_connection.closed:
            await self._ws_connection.close()
            self._ws_connection = None
            logger.info("WebSocket connection closed")
    
    async def send_message_ws(
        self,
        agent_id: str,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None
    ) -> AgentResponse:
        """
        Send a message to an agent via WebSocket.
        
        Args:
            agent_id: ID of the agent to send to
            message: Message content
            context: Optional context data
            correlation_id: Optional correlation ID for tracking
            
        Returns:
            Agent response
        """
        if not self._ws_connection or self._ws_connection.closed:
            await self.connect_websocket()
        
        # Send message
        payload = {
            "agent_id": agent_id,
            "message": message,
            "context": context or {},
            "correlation_id": correlation_id
        }
        
        await self._ws_connection.send(json.dumps(payload))
        
        # Wait for response
        response_data = await self._ws_connection.recv()
        response = json.loads(response_data)
        
        if response.get("type") == "error":
            raise Exception(f"WebSocket error: {response.get('error')}")
        
        return AgentResponse(
            response=response.get("response"),
            agent_id=response.get("agent_id"),
            timestamp=response.get("timestamp"),
            status=response.get("status"),
            correlation_id=response.get("correlation_id"),
            error=response.get("error"),
            metadata=response.get("metadata", {})
        )
    
    async def stream_messages(
        self,
        agent_id: str,
        messages: AsyncIterator[str],
        context: Optional[Dict[str, Any]] = None
    ) -> AsyncIterator[AgentResponse]:
        """
        Stream messages to an agent and receive responses.
        
        Args:
            agent_id: ID of the agent
            messages: Async iterator of messages to send
            context: Optional context data
            
        Yields:
            Agent responses
        """
        if not self._ws_connection or self._ws_connection.closed:
            await self.connect_websocket()
        
        # Create tasks for sending and receiving
        send_task = asyncio.create_task(
            self._send_stream(agent_id, messages, context)
        )
        
        try:
            while True:
                # Receive responses
                response_data = await self._ws_connection.recv()
                response = json.loads(response_data)
                
                if response.get("type") == "error":
                    logger.error(f"Stream error: {response.get('error')}")
                    break
                
                if response.get("type") == "agent_response":
                    yield AgentResponse(
                        response=response.get("response"),
                        agent_id=response.get("agent_id"),
                        timestamp=response.get("timestamp"),
                        status=response.get("status"),
                        correlation_id=response.get("correlation_id"),
                        error=response.get("error"),
                        metadata=response.get("metadata", {})
                    )
                
                # Check if sending is complete
                if send_task.done():
                    break
                    
        except websockets.exceptions.ConnectionClosed:
            logger.warning("WebSocket connection closed during streaming")
        finally:
            if not send_task.done():
                send_task.cancel()
    
    async def _send_stream(
        self,
        agent_id: str,
        messages: AsyncIterator[str],
        context: Optional[Dict[str, Any]]
    ):
        """Helper method to send messages in a stream"""
        async for message in messages:
            payload = {
                "agent_id": agent_id,
                "message": message,
                "context": context or {}
            }
            await self._ws_connection.send(json.dumps(payload))


# Convenience functions for single-use cases

async def chat_with_agent(
    agent_id: str,
    message: str,
    context: Optional[Dict[str, Any]] = None,
    base_url: str = "http://localhost:8000"
) -> AgentResponse:
    """
    Convenience function to send a single message to an agent.
    
    Args:
        agent_id: ID of the agent
        message: Message to send
        context: Optional context data
        base_url: Base URL of the agent service
        
    Returns:
        Agent response
    """
    async with FastAgentClient(base_url=base_url) as client:
        return await client.send_message(agent_id, message, context)


async def list_available_agents(
    base_url: str = "http://localhost:8000"
) -> List[AgentInfo]:
    """
    Convenience function to list available agents.
    
    Args:
        base_url: Base URL of the agent service
        
    Returns:
        List of available agents
    """
    async with FastAgentClient(base_url=base_url) as client:
        return await client.list_agents()
